/**
 * @file app_geometry_test.cc
 * @brief This file contains main function to do unit test
 *
 * This program mainly tests basic math tool functions
 *
 * <AUTHOR> <EMAIL>
 * @date 10/18/2024
 */
#include <heron/util/warp.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>

using namespace heron;
using namespace heron::warp;

// quad_size_x: 1.7777778 y: 1

// inputs
static Vector3f s_quad_position(0, 0, -3);
static Quatf s_quad_rotation(1, 0, 0, 0); // watch the order: [w, x, y, z]
static Vector3f s_new_pose_position(-0.045154784, 0.058474857, -0.056850716);
static Quatf s_new_pose_rotation(0.9915057, -0.048350833, 0.11949474, -0.017312935);
static Vector3f s_plane_normal_oldpose(0, 0, 1);
static Vector3f s_plane_point_oldpose(1, 1, 0);
// NR_COMPONENT_LEFT_DISPLAY
static Vector3f s_left_display_position(-0.032214206, -0.00014244283, -3.362323e-05);
static Quatf s_left_display_rotation(0.9999191, -0.0005736282, -0.012649442, -0.0012580671);

int main(int argc, char **argv)
{
    Transform new_pose_transform;
    new_pose_transform.position = s_new_pose_position;
    new_pose_transform.rotation = s_new_pose_rotation;
    Transform left_eye_to_head;
    left_eye_to_head.position = s_left_display_position;
    left_eye_to_head.rotation = s_left_display_rotation;

    Transform left_eye_transform;
    TransformToEyePoseLegacy(new_pose_transform, left_eye_to_head, left_eye_transform);
    PrintObject("expected left_eye_transform", left_eye_transform);

    Transform left_eye_transform_new;
    TransformToEyePose(new_pose_transform, left_eye_to_head, left_eye_transform_new);
    PrintObject("new left_eye_transform", left_eye_transform_new);

    CheckObject("head pose to eye pose", left_eye_transform, left_eye_transform_new, 1e-5);


    framework::util::log::Logger::shutdown();
    return 0;
}
