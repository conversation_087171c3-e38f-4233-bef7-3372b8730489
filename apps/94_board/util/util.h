#ifndef UTIL_H
#define UTIL_H

#include <time.h>
#include <sys/time.h>

#include <stdint.h>
#include <string>
#include <vector>

//! Helper define to make code more readable.
#define OS_NS_PER_SEC (1000000000)
#define OS_MSEC_PER_SEC (1000000)
#define OS_NS_PER_MSEC (1000000)
#define OS_NS_PER_USEC (1000)
#define F_TIME_1MS_IN_NS (1000000)

/*!
 * @brief Convert a timespec struct to nanoseconds.
 *
 * Note that this just does the value combining, no adjustment for epochs is performed.
 */
static inline uint64_t FTimespecToNs(const struct timespec *spec)
{
	uint64_t ns = 0;
	ns += (uint64_t)spec->tv_sec * OS_NS_PER_SEC;
	ns += (uint64_t)spec->tv_nsec;
	return ns;
}

/*!
 * @brief Return a monotonic clock in nanoseconds.
 */
static inline uint64_t FMonotonicGetNs(void)
{
	struct timespec ts;
	int ret = clock_gettime(CLOCK_MONOTONIC, &ts);
	if (ret != 0) {
		return 0;
	}

	return FTimespecToNs(&ts);
}


/* need to zero out the ticklist array before starting */
/* average will ramp up until the buffer is full */
/* returns average calls per second over the samples last calls */
/* no print when print_interval_sec is 0 */
class CallCounter
{
public:
    CallCounter(std::string name, uint32_t samples) : name_(name), samples_(samples),
                                                      tick_list_(samples, 0) {}
    void Update();

private:
    std::string name_;

    int samples_;
    int tick_index_ = 0;
    uint64_t tick_sum_ = 0;
    std::vector<uint64_t> tick_list_;
    uint64_t last_tick_ = 0;

    uint64_t last_print_timestamp_;
    const uint32_t PRINT_INTERVAL_SEC = 3;
};

void GenVOMask(char* mask_data[2]);

#endif