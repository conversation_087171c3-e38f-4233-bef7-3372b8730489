#include <util/util.h>
#include <framework/util/json.h>
#include <log.h>

#include <fstream>

void CallCounter::Update()
{
    if (last_tick_ == 0)
    {
        last_tick_ = FMonotonicGetNs();
        return;
    }
    uint64_t curr_tick = FMonotonicGetNs();
    uint64_t new_tick = curr_tick - last_tick_;
    last_tick_ = curr_tick;
    tick_sum_ += new_tick;                /* add new value */
    tick_sum_ -= tick_list_[tick_index_]; /* subtract value falling off */
    tick_list_[tick_index_] = new_tick;   /* save new value so it can be subtracted later */
    if (++tick_index_ == samples_)
        tick_index_ = 0; /* inc buffer index */
    if (PRINT_INTERVAL_SEC > 0)
    {
        if (last_print_timestamp_ == 0)
        {
            last_print_timestamp_ = curr_tick;
            return;
        }
        if ((curr_tick - last_print_timestamp_) / OS_NS_PER_SEC > PRINT_INTERVAL_SEC)
        {
            double avg_calls_per_second = (double)OS_NS_PER_SEC * samples_ / tick_sum_;
            last_print_timestamp_ = curr_tick;
            BOARD_LOG_INFO("{} fps: {:.2f}", name_, avg_calls_per_second);
        }
    }
}

static bool ParseMesh(const Json::Value &distortion_data)
{
    BOARD_LOG_INFO("Parse display distortion data and gen mesh");
    int type = 0, num_rows = 0, num_cols = 0;
    float *data = nullptr;
    if (!distortion_data.isMember("left_display"))
    {
        BOARD_LOG_ERROR("no left_display in display_distortion");
        return false;
    }
    const Json::Value &left_distortion_data = distortion_data["left_display"];
    if (left_distortion_data.isMember("type"))
        type = left_distortion_data["type"].asInt();
    if (left_distortion_data.isMember("num_row"))
        num_rows = left_distortion_data["num_row"].asInt();
    if (left_distortion_data.isMember("num_col"))
        num_cols = left_distortion_data["num_col"].asInt();
    BOARD_LOG_INFO("Parse distortion for left display type:{} rows:{} cols:{}.", type, num_rows, num_cols);
    Json::Value distortion_left_mesh_data = left_distortion_data["data"];
    int left_distortion_data_size = (int)distortion_left_mesh_data.size();
    if (left_distortion_data_size != num_rows * num_cols * 4)
    {
        BOARD_LOG_ERROR("left distortion data size mismatch. expect{}x{}x4={} got:{}.", num_rows, num_cols, num_rows * num_cols * 4, left_distortion_data_size);
        return false;
    }
    float dis_pt_x, dis_pt_y, dis_grid_x, dis_grid_y;
    data = new float[left_distortion_data_size];
    for (int i = 0; i < num_rows * num_cols; ++i)
    {
        int32_t col = i % num_cols;
        int32_t row = i / num_cols;
        dis_grid_x = distortion_left_mesh_data[4 * i + 0].asFloat();
        dis_grid_y = distortion_left_mesh_data[4 * i + 1].asFloat();
        dis_pt_x = distortion_left_mesh_data[4 * i + 2].asFloat();
        dis_pt_y = distortion_left_mesh_data[4 * i + 3].asFloat();
        BOARD_LOG_INFO("left display row:{} col:{} grid:({:.2f}, {:.2f}) pt:({:.2f}, {:.2f})", row, col, dis_grid_x, dis_grid_y, dis_pt_x, dis_pt_y);
    }

    if (!distortion_data.isMember("right_display"))
    {
        BOARD_LOG_ERROR("no right_display in display_distortion");
        return false;
    }
    const Json::Value &right_distortion_data = distortion_data["right_display"];
    if (right_distortion_data.isMember("type"))
        type = right_distortion_data["type"].asInt();
    if (right_distortion_data.isMember("num_row"))
        num_rows = right_distortion_data["num_row"].asInt();
    if (right_distortion_data.isMember("num_col"))
        num_cols = right_distortion_data["num_col"].asInt();
    BOARD_LOG_INFO("Parse distortion for right display type:{} rows:{} cols:{}.", type, num_rows, num_cols);
    Json::Value distortion_right_mesh_data = right_distortion_data["data"];
    int right_distortion_data_size = (int)distortion_right_mesh_data.size();
    if (right_distortion_data_size != num_rows * num_cols * 4)
    {
        BOARD_LOG_ERROR("right distortion data size mismatch. expect{}x{}x4={} got:{}.", num_rows, num_cols, num_rows * num_cols * 4, right_distortion_data_size);
        return false;
    }
    for (int i = 0; i < num_rows * num_cols; ++i)
    {
        int32_t col = i % num_cols;
        int32_t row = i / num_cols;
        dis_grid_x = distortion_right_mesh_data[4 * i + 0].asFloat();
        dis_grid_y = distortion_right_mesh_data[4 * i + 1].asFloat();
        dis_pt_x = distortion_right_mesh_data[4 * i + 2].asFloat();
        dis_pt_y = distortion_right_mesh_data[4 * i + 3].asFloat();
        BOARD_LOG_INFO("right display row:{} col:{} grid:({:.2f}, {:.2f}) pt:({:.2f}, {:.2f})", row, col, dis_grid_x, dis_grid_y, dis_pt_x, dis_pt_y);
    }
    delete[] data;
    return true;
}

bool ParseGlassesConfig(std::string file_path)
{
    std::ifstream file(file_path, std::ios::binary | std::ios::ate); // 以二进制模式打开，并定位到文件末尾
    if (!file.is_open()) {
        throw std::runtime_error("Failed to open file: " + file_path);
    }

    // 获取文件大小
    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg); // 回到文件开头

    // 读取文件内容到 vector<char>
    std::vector<char> buffer(size);
    if (!file.read(buffer.data(), size)) {
        throw std::runtime_error("Failed to read file: " + file_path);
    }

    // 返回数据（注意：buffer.data() 的生命周期由调用者管理）
    Json::Value json_root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    std::istringstream json_stream(std::string(buffer.data(), static_cast<uint32_t>(size)));
    if (!parseFromStream(json_builder, json_stream, &json_root, &json_errs))
    {
        BOARD_LOG_ERROR("Parse glasses config error, json_errs = {}", json_errs.c_str());
        return false;
    }

    if (!json_root.isMember("display"))
    {
        BOARD_LOG_ERROR("No display metadata in config");
        return false;
    }
    const Json::Value &distortion_data = json_root["display_distortion"];
    if (!json_root.isMember("display_distortion"))
    {
        BOARD_LOG_ERROR("No display_distortion in config");
        return false;
    }
    if (!ParseMesh(distortion_data))
    {
        return false;
    }
    return true;
}

void GenVOMask(char *mask_data[2])
{
    ParseGlassesConfig("/factory/glasses_config.json");
    for (int i = 0; i < 2; ++i) // 0 corresponds to left, 1 corresponds to right
    {
        mask_data[i] = new char[1920 * 1080 * 2];
        BOARD_LOG_INFO("GenVOMask {}:{}", i, (void *)mask_data[i]);
        memset(mask_data[i], 0, 1920 * 1080 * 2);
        for (int col = 0; col < 1920; ++col)
        {
            for (int row = 0; row < 1080; ++row)
            {
                int idx = (row * 1920 + col) * 2;
                int x_diff = col - 960 * i;
                int y_diff = row - 540;
                if (x_diff * x_diff + y_diff * y_diff > 540 * 540)
                {
                    if (i == 1)
                    {
                        mask_data[i][idx] = 0x00;
                        mask_data[i][idx + 1] = 0xf0;
                    }
                    else
                    {
                        mask_data[i][idx] = 0x00;
                        mask_data[i][idx + 1] = 0xf0;
                    }
                }
            }
        }
    }
}
