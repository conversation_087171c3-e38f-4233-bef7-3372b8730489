#define NRPLUGIN
#define XR_BSP_API __attribute__((visibility("default")))

#include <heron/interface/device_api/nr_extra_dp.h>
#include <heron/interface/device_api/nr_extra_display.h>
#include <board_context/board_context.h>
#include <log.h>

#include <framework/util/os_time.h>

#include <mpi_vi.h>

#include <memory>
#include <queue>

#define DUMMY_STUB_FUNC                               \
    BOARD_LOG_TRACE("calling {} stub", __FUNCTION__); \
    return AR_SUCCESS;

#define FRAME_INFO_COUNT_FOR_DP 3
#define DP_FPS 60
#define DPU_FPS 60
#define V_BLANK_US 0
#define SCREEN_HEIGHT_DEFAULT 1080

#define AR_ALIGN128(_x) (((_x) + 0x7f) & ~0x7f)

typedef AR_S32 (*VO_SUBSCRIBE_FUNC_T)(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info);
enum DPU_ID : int32_t
{
    DPU_ID_LEFT_DISPLAY = 0,
    DPU_ID_RIGHT_DISPLAY = 1,
    DPU_ID_COUNT = 2,
};
#define ROW_COUNT_IN_BLOCK 32

static bool DUAL_GDC = true;

static VO_IRQ_ATTR_S s_vo_irq_attr[DPU_ID_COUNT] = {};
static VO_SUBSCRIBE_ATTR_S s_vo_subscribe_attr[DPU_ID_COUNT] = {};

static AR_S32 AR_MPI_VI_GetChnFrame_stub(VI_PIPE ViPipe, VI_CHN ViChn, VIDEO_FRAME_INFO_S *pstFrameInfo, AR_S32 s32MilliSec);
static AR_S32 AR_MPI_VI_ReleaseChnFrame_stub(VI_PIPE ViPipe, VI_CHN ViChn, const VIDEO_FRAME_INFO_S *pstFrameInfo);

static void DpuStartStubThreads();
static void DpuStopStubThreads();

extern "C"
{
    XR_BSP_API NRResult NRLocalDisplayConfigService(const NRDisplayConfig *display_config)
    {
        for (int i = 0; i < DPU_ID_COUNT; i++)
        {
            if (!DUAL_GDC && i == DPU_ID_RIGHT_DISPLAY)
            {
                BOARD_LOG_INFO("No need to register callback to VO{}", DPU_ID_RIGHT_DISPLAY);
                continue;
            }
            s_vo_irq_attr[i].enIrqType = IRQ_TYPE_INTERVAL_LINE;
            s_vo_irq_attr[i].stIrqAttr.stLineIrqPara.u32LineCount = display_config->callback_block_cnt * ROW_COUNT_IN_BLOCK;

            s_vo_subscribe_attr[i].u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
            // s_vo_subscribe_attr[i].subscribe_call_back = reinterpret_cast<VO_SUBSCRIBE_FUNC_T>(display_config->callback);
        }
        return NR_RESULT_SUCCESS;
    }

    // XXX: now heron only supports starting dp before dpu
    static bool gdc0_started = false;
    static bool gdc1_started = false;
    static bool dpu_started = false;
    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Process(AR_GDC_ADV_TRANSFORM_S *pstParam)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        if (pstParam->s32CoreId == 0)
            gdc0_started = true;
        if (pstParam->s32CoreId == 1)
            gdc1_started = true;
        if (!dpu_started)
        {
            if (gdc0_started && gdc1_started)
            {
                DpuStartStubThreads();
                dpu_started = true;
            }
        }
        return AR_SUCCESS;
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Config(AR_GDC_ADV_TRANSFORM_S *pstParam)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        return AR_SUCCESS;
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Config_CPUBp_Line(AR_GDC_ADV_LINE_PARAMS_S *pstLineParam)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        return AR_SUCCESS;
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Get_FrmPts(AR_GDC_ADV_FRMPTS_PARAMS_S *pstGdcFrmPtsParams)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        return AR_SUCCESS;
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Start(AR_GDC_ADV_TRANSFORM_S *pstParam)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        return AR_SUCCESS;
    }

    XR_BSP_API AR_S32 XR_ar_hal_sys_mmz_alloc(AR_U64 *pu64_phy_addr, AR_VOID **p_vir_addr,
                                              const AR_CHAR *pstr_mmb, const AR_CHAR *pstr_zone, AR_U32 u32_len)
    {
        BOARD_LOG_TRACE("calling {} stub name:{}", __FUNCTION__, pstr_mmb);
        *p_vir_addr = malloc(u32_len);
        if (*p_vir_addr)
        {
            *pu64_phy_addr = (uint64_t)(*p_vir_addr);
            return AR_SUCCESS;
        }
        return AR_FAILURE;
    }

    XR_BSP_API AR_S32 XR_ar_hal_sys_mmz_free(AR_U64 u64_phy_addr, AR_VOID *p_vir_addr)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        if (p_vir_addr)
            free(p_vir_addr);
        return AR_SUCCESS;
    }

    XR_BSP_API NRResult NRLoadAR94APIs(bool log_all_level)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        Logger::GetInstance()->SetLogAllLevel(log_all_level);
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRInitBoardContext()
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRReleaseBoardContext()
    {
        DpuStopStubThreads();
        return NR_RESULT_SUCCESS;
    }
    /// @brief 从 DP 模块获取当前的一帧图像，对应AR_MPI_VI_GetChnFrame(0,0,frame,-1)
    /// \n air-like:
    /// \n light:
    /// \n gina: new
    /// @param data 图像数据
    /// @param timsout_ms 获取图像帧的超时时间，单位毫秒
    /// @return 结果

    XR_BSP_API NRResult NRLocalDpGetFrame(NRDpFrameData *nr_dp_frame, uint32_t timeout_ms)
    {
        VIDEO_FRAME_INFO_S *pstFrameInfo = new VIDEO_FRAME_INFO_S();
        if (AR_SUCCESS != AR_MPI_VI_GetChnFrame_stub(0, 0, pstFrameInfo, timeout_ms))
            return NR_RESULT_FAILURE;
        nr_dp_frame->ar_frame_handle = (uint64_t)(pstFrameInfo);
        nr_dp_frame->data.x = pstFrameInfo->stVFrame.u64VirAddr[0];
        nr_dp_frame->data.y = pstFrameInfo->stVFrame.u64VirAddr[1];
        nr_dp_frame->data.z = pstFrameInfo->stVFrame.u64VirAddr[2];
        nr_dp_frame->data_ext.x = pstFrameInfo->stVFrame.u64PhyAddr[0];
        nr_dp_frame->data_ext.y = pstFrameInfo->stVFrame.u64PhyAddr[1];
        nr_dp_frame->data_ext.z = pstFrameInfo->stVFrame.u64PhyAddr[2];
        nr_dp_frame->frame_id = pstFrameInfo->stVFrame.u32FrameId;
        nr_dp_frame->width = pstFrameInfo->stVFrame.u32Width;
        nr_dp_frame->height = pstFrameInfo->stVFrame.u32Height;
        nr_dp_frame->strides.x = pstFrameInfo->stVFrame.u32Stride[0];
        nr_dp_frame->strides.y = pstFrameInfo->stVFrame.u32Stride[1];
        nr_dp_frame->strides.z = pstFrameInfo->stVFrame.u32Stride[2];
        nr_dp_frame->pts = pstFrameInfo->stVFrame.u64PTS;
        nr_dp_frame->pixel_format = NR_FRAME_BUFFER_FORMAT_YUV420_PLANAR;
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDpReleaseFrame(const void *frame_info)
    {
        const VIDEO_FRAME_INFO_S *handle = (const VIDEO_FRAME_INFO_S *)(((NRDpFrameData *)frame_info)->ar_frame_handle);
        AR_S32 ret = AR_MPI_VI_ReleaseChnFrame_stub(0, 0, handle);
        delete (handle);
        if (AR_SUCCESS != ret)
        {
            BOARD_LOG_ERROR("AR_MPI_VI_ReleaseChnFrame_stub error: {:x}", ret);
            return NR_RESULT_FAILURE;
        }
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDisplayStartOSDRender(NRDisplayUsage, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDisplayStopOSDRender()
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        return NR_RESULT_SUCCESS;
    }

    static uint32_t s_overlay_frame_alloc_count = 0; // its just an example
    XR_BSP_API NRResult NRLocalDisplayAllocOverlayFrame(NROverlayFrameData *overlay_frame_info)
    {
        if (s_overlay_frame_alloc_count >= 4)
        { // its just an example, should not implement like this
            BOARD_LOG_ERROR("exceeds maximum overlay frame alloc count: {}", 4);
            return NR_RESULT_FAILURE;
        }
        uint32_t width = 960;
        uint32_t height = 540;
        uint32_t stride = AR_ALIGN128(width);
        uint32_t u32Size = stride * height * 4;

        uint32_t i = s_overlay_frame_alloc_count;
        overlay_frame_info->width = width;
        overlay_frame_info->height = height;
        overlay_frame_info->data_data = (const char *)malloc(u32Size);
        overlay_frame_info->data_size = u32Size;

        s_overlay_frame_alloc_count++;

        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDisplayDeallocOverlayFrame(const NROverlayFrameData *overlay_frame_info)
    {
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDisplaySendOverlayFrame(NRDisplayUsage display_usage, const NROverlayFrameData *overlay_frame_info)
    {
        BOARD_LOG_TRACE("calling {} stub", __FUNCTION__);
        usleep(16 * 1000);
        return NR_RESULT_SUCCESS;
    }
}

/// @brief for DP input source stub logic
template <typename T>
class IndexedBlockingBufferPool
{
public:
    IndexedBlockingBufferPool(uint32_t size) : size_(size), buffers_(size)
    {
        for (uint32_t i = 0; i < size; ++i)
        {
            available_indexes_.push(i);
            buffers_[i].first = i;
        }
    }

    std::pair<uint32_t, T> &Get()
    {
        std::unique_lock<std::mutex> lock(mutex_);
        while (available_indexes_.empty())
        {
            condition_.wait(lock);
        }
        uint32_t index = available_indexes_.front();
        available_indexes_.pop();
        return buffers_[index];
    }

    void Release(uint32_t index)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        available_indexes_.push(index);
        condition_.notify_one();
    }

private:
    int size_;
    std::vector<std::pair<uint32_t, T>> buffers_;
    std::queue<uint32_t> available_indexes_;
    std::mutex mutex_;
    std::condition_variable condition_;
};

static IndexedBlockingBufferPool<VIDEO_FRAME_INFO_S> s_buffer_pool(FRAME_INFO_COUNT_FOR_DP);
static uint32_t s_dp_src_frame_index = 0;
AR_S32 AR_MPI_VI_GetChnFrame_stub(VI_PIPE ViPipe, VI_CHN ViChn, VIDEO_FRAME_INFO_S *pstFrameInfo, AR_S32 s32MilliSec)
{
    BOARD_LOG_TRACE("calling {}", __FUNCTION__);
    usleep(1000 * 1000 / DP_FPS);
    std::pair<uint32_t, VIDEO_FRAME_INFO_S> &result = s_buffer_pool.Get();
    uint32_t index = result.first;
    *pstFrameInfo = result.second;
    pstFrameInfo->stVFrame.u64ExtPhyAddr[0] = (uint64_t)index; // XXX just use this field
    pstFrameInfo->stVFrame.u64PTS = framework::util::FMonotonicGetNs();
    pstFrameInfo->stVFrame.u32FrameId = s_dp_src_frame_index;
    BOARD_LOG_TRACE("stub generating frame from frame_info_from_pool_index: {} frame_id: {} pstFrameInfo: {}", index, s_dp_src_frame_index, (void *)pstFrameInfo);
    s_dp_src_frame_index++;
    return AR_SUCCESS;
}

AR_S32 AR_MPI_VI_ReleaseChnFrame_stub(VI_PIPE ViPipe, VI_CHN ViChn, const VIDEO_FRAME_INFO_S *pstFrameInfo)
{
    BOARD_LOG_TRACE("calling {}", __FUNCTION__);
    uint32_t releasing_index = pstFrameInfo->stVFrame.u64ExtPhyAddr[0];
    s_buffer_pool.Release(releasing_index);
    BOARD_LOG_TRACE("released frame_info_from_pool_index: {} frame_id: {}", releasing_index, pstFrameInfo->stVFrame.u32FrameId);
    return AR_SUCCESS;
}

/// dpu stub threads
static bool s_dpu_quit = false;
void DpuThreadStub()
{
    uint32_t dpu_refresh_interval_us = 1000 * 1000 / DPU_FPS;
    uint32_t line_callback_interval_lines = s_vo_irq_attr[0].stIrqAttr.stLineIrqPara.u32LineCount;
    uint32_t block_callback_interval_us = (dpu_refresh_interval_us - V_BLANK_US) * line_callback_interval_lines / SCREEN_HEIGHT_DEFAULT;

    VO_SUBSCRIBE_INFO_S sub_info;
    BOARD_LOG_TRACE("Dpu stub thread starting. dpu_refresh_interval_us: {} vblank: {}",
                    dpu_refresh_interval_us,
                    V_BLANK_US);
    while (!s_dpu_quit)
    {
        usleep(V_BLANK_US);
        if (s_vo_subscribe_attr[0].u32SubscribeType & IRQ_TYPE_VSYNC)
        {
            if (!s_vo_subscribe_attr[0].subscribe_call_back)
            {
                BOARD_LOG_ERROR("Dpu error! [vsync] callback enabled but no pfn registered.");
            }
            else
            {
                sub_info.u32IrqType = IRQ_TYPE_VSYNC;
                sub_info.u64IrqTimeNs = framework::util::FMonotonicGetNs();
                s_vo_subscribe_attr[0].subscribe_call_back((VO_DEV)0, &sub_info);
                if (DUAL_GDC)
                {
                    s_vo_subscribe_attr[1].subscribe_call_back((VO_DEV)1, &sub_info);
                }
            }
        }
        uint32_t flushed_line_count = 0;
        uint32_t current_interrupt_id = 0;
        while (flushed_line_count + line_callback_interval_lines < SCREEN_HEIGHT_DEFAULT)
        {
            usleep(block_callback_interval_us);
            flushed_line_count += line_callback_interval_lines;
            if (s_vo_subscribe_attr[0].u32SubscribeType & IRQ_TYPE_INTERVAL_LINE)
            {
                if (!s_vo_subscribe_attr[0].subscribe_call_back)
                {
                    BOARD_LOG_ERROR("Dpu error! [line] callback enabled but no pfn registered.");
                }
                else
                {
                    sub_info.u32IrqType = IRQ_TYPE_INTERVAL_LINE;
                    sub_info.u64IrqTimeNs = framework::util::FMonotonicGetNs();
                    sub_info.stExtpara.stIntervalLinePara.u32CurBlockID = current_interrupt_id; // start from 0
                    s_vo_subscribe_attr[0].subscribe_call_back((VO_DEV)0, &sub_info);
                    if (DUAL_GDC)
                    {
                        s_vo_subscribe_attr[1].subscribe_call_back((VO_DEV)1, &sub_info);
                    }
                }
                current_interrupt_id++;
            }
        }
        if (s_vo_subscribe_attr[0].u32SubscribeType & IRQ_TYPE_FRAME_DONE)
        {
            if (!s_vo_subscribe_attr[0].subscribe_call_back)
            {
                BOARD_LOG_ERROR("Dpu error! [frame done] callback enabled but no pfn registered.");
            }
            else
            {
                sub_info.u32IrqType = IRQ_TYPE_FRAME_DONE;
                sub_info.u64IrqTimeNs = framework::util::FMonotonicGetNs();
                s_vo_subscribe_attr[0].subscribe_call_back((VO_DEV)0, &sub_info);
                if (DUAL_GDC)
                {
                    s_vo_subscribe_attr[1].subscribe_call_back((VO_DEV)1, &sub_info);
                }
            }
        }
    }
    BOARD_LOG_TRACE("Dpu stub thread quit.");
}

// to avoid 2 threads have larger and larger time drift, we simulate callbacks in one thread
static std::thread s_dpu_thread;
void DpuStartStubThreads()
{
    s_dpu_quit = false;
    s_dpu_thread = std::thread(DpuThreadStub);
}

void DpuStopStubThreads()
{
    s_dpu_quit = true;
    if (s_dpu_thread.joinable())
        s_dpu_thread.join();
}
