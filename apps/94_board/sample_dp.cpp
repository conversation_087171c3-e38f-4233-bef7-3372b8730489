#include <94_board.h>

#include <util/util.h>
#include <log.h>

#include <hal_vo.h>

bool LoadBoardSymbols();

extern PFN_AR_MPI_GDC_ADV_Process pfn_AR_MPI_GDC_ADV_Process;
extern PFN_AR_MPI_GDC_ADV_Config pfn_AR_MPI_GDC_ADV_Config;
extern PFN_AR_MPI_GDC_ADV_Start pfn_AR_MPI_GDC_ADV_Start;
extern PFN_AR_MPI_GDC_ADV_Stop pfn_AR_MPI_GDC_ADV_Stop;
extern PFN_AR_MPI_GDC_ADV_Config_CPUBp_Line pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line;
extern PFN_AR_MPI_GDC_ADV_Get_FrmPts pfn_AR_MPI_GDC_ADV_Get_FrmPts;
extern PFN_AR_MPI_VIN_OpenDev pfn_AR_MPI_VIN_OpenDev;
extern PFN_AR_MPI_VIN_CloseDev pfn_AR_MPI_VIN_CloseDev;
extern PFN_AR_MPI_VIN_GetSensorObj pfn_AR_MPI_VIN_GetSensorObj;
extern PFN_AR_MPI_VIN_CloseSensorObj pfn_AR_MPI_VIN_CloseSensorObj;
extern PFN_AR_MPI_VIN_PipeBindSensor pfn_AR_MPI_VIN_PipeBindSensor;
extern PFN_AR_MPI_SYS_Init pfn_AR_MPI_SYS_Init;
extern PFN_AR_MPI_SYS_Exit pfn_AR_MPI_SYS_Exit;
extern PFN_AR_MPI_SYS_Mmap pfn_AR_MPI_SYS_Mmap;
extern PFN_AR_MPI_VB_Init pfn_AR_MPI_VB_Init;
extern PFN_AR_MPI_VB_Exit pfn_AR_MPI_VB_Exit;
extern PFN_AR_MPI_VB_SetConfig pfn_AR_MPI_VB_SetConfig;
extern PFN_AR_MPI_VB_ExitModCommPool pfn_AR_MPI_VB_ExitModCommPool;
extern PFN_AR_MPI_VB_CreatePool pfn_AR_MPI_VB_CreatePool;
extern PFN_AR_MPI_VB_GetBlock pfn_AR_MPI_VB_GetBlock;
extern PFN_AR_MPI_VB_ReleaseBlock pfn_AR_MPI_VB_ReleaseBlock;
extern PFN_AR_MPI_VB_Handle2PoolId pfn_AR_MPI_VB_Handle2PoolId;
extern PFN_AR_MPI_VB_Handle2PhysAddr pfn_AR_MPI_VB_Handle2PhysAddr;
extern PFN_AR_MPI_VB_PhysAddr2Handle pfn_AR_MPI_VB_PhysAddr2Handle;
extern PFN_AR_MPI_VB_MmapPool pfn_AR_MPI_VB_MmapPool;
extern PFN_AR_MPI_VB_MunmapPool pfn_AR_MPI_VB_MunmapPool;
extern PFN_AR_MPI_VB_GetBlockVirAddr pfn_AR_MPI_VB_GetBlockVirAddr;

extern PFN_AR_MPI_VI_SetChnAttr pfn_AR_MPI_VI_SetChnAttr;
extern PFN_AR_MPI_VI_SetChnExtAttr pfn_AR_MPI_VI_SetChnExtAttr;
extern PFN_AR_MPI_VI_SetChnCmpAttr pfn_AR_MPI_VI_SetChnCmpAttr;
extern PFN_AR_MPI_VI_SetMipiBindDev pfn_AR_MPI_VI_SetMipiBindDev;
extern PFN_AR_MPI_VI_SetComboDevAttr pfn_AR_MPI_VI_SetComboDevAttr;
extern PFN_AR_MPI_VI_SetDevAttr pfn_AR_MPI_VI_SetDevAttr;
extern PFN_AR_MPI_VI_SetDevBindPipe pfn_AR_MPI_VI_SetDevBindPipe;
extern PFN_AR_MPI_VI_EnableDev pfn_AR_MPI_VI_EnableDev;
extern PFN_AR_MPI_VI_DisableDev pfn_AR_MPI_VI_DisableDev;
extern PFN_AR_MPI_VI_EnableChn pfn_AR_MPI_VI_EnableChn;
extern PFN_AR_MPI_VI_DisableChn pfn_AR_MPI_VI_DisableChn;
extern PFN_AR_MPI_VI_GetPipeExtAttr pfn_AR_MPI_VI_GetPipeExtAttr;
extern PFN_AR_MPI_VI_SetPipeExtAttr pfn_AR_MPI_VI_SetPipeExtAttr;
extern PFN_AR_MPI_VI_StartPipe pfn_AR_MPI_VI_StartPipe;
extern PFN_AR_MPI_VI_StopPipe pfn_AR_MPI_VI_StopPipe;
extern PFN_AR_MPI_VI_CreatePipe pfn_AR_MPI_VI_CreatePipe;
extern PFN_AR_MPI_VI_DestroyPipe pfn_AR_MPI_VI_DestroyPipe;
extern PFN_AR_MPI_VI_GetChnFrame pfn_AR_MPI_VI_GetChnFrame;
extern PFN_AR_MPI_VI_GetHightPricisionPipeFPS pfn_AR_MPI_VI_GetHightPricisionPipeFPS;
extern PFN_AR_MPI_VI_ReShapeChExt pfn_AR_MPI_VI_ReShapeChExt;

extern PFN_AR_MPI_VI_ReleaseChnFrame pfn_AR_MPI_VI_ReleaseChnFrame;
extern PFN_AR_MPI_VO_Enable pfn_AR_MPI_VO_Enable;
extern PFN_AR_MPI_VO_Disable pfn_AR_MPI_VO_Disable;
extern PFN_AR_MPI_VO_EnableVideoLayer pfn_AR_MPI_VO_EnableVideoLayer;
extern PFN_AR_MPI_VO_DisableVideoLayer pfn_AR_MPI_VO_DisableVideoLayer;
extern PFN_AR_MPI_VO_SetVideoLayerAttr pfn_AR_MPI_VO_SetVideoLayerAttr;
extern PFN_AR_MPI_VO_SetVideoLayerPos pfn_AR_MPI_VO_SetVideoLayerPos;
extern PFN_AR_MPI_VO_EnableChn pfn_AR_MPI_VO_EnableChn;
extern PFN_AR_MPI_VO_DisableChn pfn_AR_MPI_VO_DisableChn;
extern PFN_AR_MPI_VO_SetChnAttr pfn_AR_MPI_VO_SetChnAttr;
extern PFN_AR_MPI_VO_SetUserIntfSyncInfo pfn_AR_MPI_VO_SetUserIntfSyncInfo;
extern PFN_AR_MPI_VO_SetIrqAttr pfn_AR_MPI_VO_SetIrqAttr;
extern PFN_AR_MPI_VO_SubscribeEnable pfn_AR_MPI_VO_SubscribeEnable;
extern PFN_AR_MPI_VO_SubscribeDisable pfn_AR_MPI_VO_SubscribeDisable;

extern PFN_AR_MPI_VO_SetPubAttr pfn_AR_MPI_VO_SetPubAttr;
extern PFN_AR_MPI_VO_SetDevFrameRate pfn_AR_MPI_VO_SetDevFrameRate;
extern PFN_AR_MPI_VO_SetStartAttr pfn_AR_MPI_VO_SetStartAttr;
extern PFN_AR_MPI_VO_SetLowdelayAttr pfn_AR_MPI_VO_SetLowdelayAttr;
extern PFN_AR_MPI_VO_Dsi_SetAttr pfn_AR_MPI_VO_Dsi_SetAttr;
extern PFN_AR_MPI_VO_Dsi_Enable pfn_AR_MPI_VO_Dsi_Enable;
extern PFN_AR_MPI_VO_SendFrame pfn_AR_MPI_VO_SendFrame;
extern PFN_AR_MPI_ISP_Init pfn_AR_MPI_ISP_Init;
extern PFN_AR_MPI_ISP_Run pfn_AR_MPI_ISP_Run;
extern PFN_AR_MPI_ISP_MemInit pfn_AR_MPI_ISP_MemInit;
extern PFN_AR_MPI_ISP_SetPubAttr pfn_AR_MPI_ISP_SetPubAttr;
extern PFN_AR_MPI_ISP_Exit pfn_AR_MPI_ISP_Exit;
extern PFN_ar_hal_sys_mmz_alloc pfn_ar_hal_sys_mmz_alloc;
extern PFN_ar_hal_sys_mmz_free pfn_ar_hal_sys_mmz_free;
extern PFN_ar_hal_dp_rx_set_edid pfn_ar_hal_dp_rx_set_edid;
extern PFN_ar_hal_dp_rx_get_hpd_status pfn_ar_hal_dp_rx_get_hpd_status;
extern PFN_ar_hal_dp_rx_set_hpd_status pfn_ar_hal_dp_rx_set_hpd_status;

extern PFN_ar_log_init pfn_ar_log_init;
extern PFN_ar_log_func pfn_ar_log_func;
extern PFN_ar_log_func_raw pfn_ar_log_func_raw;
extern PFN_ar_memset pfn_ar_memset;

extern PFN_ar_queue_create pfn_ar_queue_create;
extern PFN_ar_queue_push_force pfn_ar_queue_push_force;
extern PFN_ar_queue_pop_timeout pfn_ar_queue_pop_timeout;

static VO_DSI_ATTR_S pstDsiCfg_60Hz =
    {
        .u32Lane = {1, 1, 1, 1},
        .u32BitsPerPixel = 24,
        .stSyncInfo = {
            .u32Hsa = 60,
            .u32Hbp = 200,
            .u32Hact = 1920,
            .u32Hfp = 120,

            .u32Vsa = 2,
            .u32Vbp = 14,
            .u32Vact = 1080,
            .u32Vfp = 16},
        .fDphyClkMhz = 920.736,
};

static VO_DSI_ATTR_S pstDsiCfg_90Hz =
    {
        .u32Lane = {1, 1, 1, 1},
        .u32BitsPerPixel = 24,
        .stSyncInfo = {
            .u32Hsa = 60,
            .u32Hbp = 200,
            .u32Hact = 1920,
            .u32Hfp = 120,

            .u32Vsa = 2,
            .u32Vbp = 14,
            .u32Vact = 1080,
            .u32Vfp = 16},
        .fDphyClkMhz = 1381.104,
};

/******************************************************************************
 * function : vb init & MPI system init
 ******************************************************************************/
static AR_S32 SAMPLE_COMM_SYS_Init(VB_CONFIG_S *pstVbConfig)
{
    AR_S32 s32Ret = AR_FAILURE;

    pfn_AR_MPI_SYS_Exit();
    pfn_AR_MPI_VB_Exit();

    if (NULL == pstVbConfig)
    {
        BOARD_LOG_ERROR("input parameter is null, it is invaild!");
        return AR_FAILURE;
    }

    s32Ret = pfn_AR_MPI_VB_SetConfig(pstVbConfig);

    if (AR_SUCCESS != s32Ret)
    {
        BOARD_LOG_ERROR("AR_MPI_VB_SetConf failed!");
        return AR_FAILURE;
    }

    s32Ret = pfn_AR_MPI_VB_Init();

    if (AR_SUCCESS != s32Ret)
    {
        BOARD_LOG_ERROR("AR_MPI_VB_Init failed!");
        return AR_FAILURE;
    }

    s32Ret = pfn_AR_MPI_SYS_Init();

    if (AR_SUCCESS != s32Ret)
    {
        BOARD_LOG_ERROR("AR_MPI_SYS_Init failed!");
        pfn_AR_MPI_VB_Exit();
        return AR_FAILURE;
    }

    return AR_SUCCESS;
}

static AR_VOID SAMPLE_COMM_SYS_Exit(void)
{
    pfn_AR_MPI_SYS_Exit();
    pfn_AR_MPI_VB_ExitModCommPool(VB_UID_VDEC);
    pfn_AR_MPI_VB_Exit();
    return;
}

#define COLOR_RGB_RED 0xFF0000
#define COLOR_RGB_GREEN 0x00FF00
#define COLOR_RGB_BLUE 0x0000FF
#define COLOR_RGB_BLACK 0x000000
#define COLOR_RGB_YELLOW 0xFFFF00
#define COLOR_RGB_CYN 0x00ffff
#define COLOR_RGB_WHITE 0xffffff

#define AR_ALIGN4(_x) (((_x) + 0x03) & ~0x03)
#define AR_ALIGN8(_x) (((_x) + 0x07) & ~0x07)
#define AR_ALIGN16(_x) (((_x) + 0x0f) & ~0x0f)
#define AR_ALIGN32(_x) (((_x) + 0x1f) & ~0x1f)
#define AR_ALIGN64(_x) (((_x) + 0x3f) & ~0x3f)
#define AR_ALIGN128(_x) (((_x) + 0x7f) & ~0x7f)
#define AR_ALIGN256(_x) (((_x) + 0xff) & ~0xff)
#define AR_ALIGN512(_x) (((_x) + 0x1ff) & ~0x1ff)
#define AR_ALIGN16384(_x) (((_x) + 0x3fff) & ~0x3fff)

#define DPTEST_ALIGNE_TO(size, num) ((num) * (((size) + (num) - 1) / (num)))
#define SAMPLE_CHECK_RET(express, name)                                                          \
    do                                                                                           \
    {                                                                                            \
        AR_S32 Ret;                                                                              \
        Ret = express;                                                                           \
        if (Ret != AR_SUCCESS)                                                                   \
        {                                                                                        \
            printf("%s failed at %s : LINE: %d with %#x!\n", name, __FUNCTION__, __LINE__, Ret); \
            return Ret;                                                                          \
        }                                                                                        \
    } while (0)

#define SAMPLE_PRT(fmt, ...)           \
    do                                 \
    {                                  \
        ar_always(fmt, ##__VA_ARGS__); \
    } while (0)

#define CHECK_RET(express, name)                                                                                    \
    do                                                                                                              \
    {                                                                                                               \
        AR_S32 Ret;                                                                                                 \
        Ret = express;                                                                                              \
        if (AR_SUCCESS != Ret)                                                                                      \
        {                                                                                                           \
            printf("\033[0;31m%s failed at %s: LINE: %d with %#x!\033[0;39m\n", name, __FUNCTION__, __LINE__, Ret); \
            return Ret;                                                                                             \
        }                                                                                                           \
    } while (0)

static AR_BOOL quit = AR_FALSE;

#define GDC_RUN_NUM 2
#define WARP_FILE_NUM 500
#define MESH_SIZE_NUM 82
#define RESHAPE_SIZE_NUM 200

static AR_GDC_ADV_TRANSFORM_S g_stGdcParam[GDC_RUN_NUM] = {0};
static AR_U64 pu64WarpParaPa[GDC_RUN_NUM][WARP_FILE_NUM] = {0};
static void *ppvWarpParaVa[GDC_RUN_NUM][WARP_FILE_NUM] = {NULL};

static SIZE_S stMeshSize[MESH_SIZE_NUM] =
    {{1000, 564},
     {1020, 572},
     {1036, 584},
     {1056, 596},
     {1076, 604},
     {1096, 616},
     {1116, 628},
     {1132, 640},
     {1152, 648},
     {1172, 660},
     {1192, 672},
     {1212, 680},
     {1228, 692},
     {1248, 704},
     {1268, 712},
     {1288, 724},
     {1308, 736},
     {1324, 748},
     {1344, 756},
     {1364, 768},
     {1384, 780},
     {1404, 788},
     {1420, 800},
     {1440, 812},
     {1460, 820},
     {1480, 832},
     {1500, 844},
     {1516, 856},
     {1536, 864},
     {1556, 876},
     {1576, 888},
     {1596, 896},
     {1612, 908},
     {1632, 920},
     {1652, 928},
     {1672, 940},
     {1692, 952},
     {1708, 964},
     {1728, 972},
     {1748, 984},
     {1768, 996},
     {1788, 1004},
     {1804, 1016},
     {1824, 1028},
     {1844, 1036},
     {1864, 1048},
     {1884, 1060},
     {1900, 1072},
     {1920, 1080},
     {384, 216},
     {404, 228},
     {424, 240},
     {444, 248},
     {460, 260},
     {480, 272},
     {500, 280},
     {520, 292},
     {540, 304},
     {556, 316},
     {576, 324},
     {596, 336},
     {616, 348},
     {636, 356},
     {640, 480},
     {652, 368},
     {672, 380},
     {692, 388},
     {712, 400},
     {732, 412},
     {748, 424},
     {768, 432},
     {788, 444},
     {808, 456},
     {828, 464},
     {844, 476},
     {864, 488},
     {884, 496},
     {904, 508},
     {924, 520},
     {940, 532},
     {960, 540},
     {980, 552}};

static SIZE_S stRshapeSize[RESHAPE_SIZE_NUM] =
    {{1920, 1080},
     {1900, 1069},
     {1881, 1058},
     {1862, 1047},
     {1843, 1036},
     {1824, 1026},
     {1804, 1015},
     {1785, 1004},
     {1766, 993},
     {1747, 982},
     {1728, 972},
     {1708, 961},
     {1689, 950},
     {1670, 939},
     {1651, 928},
     {1632, 918},
     {1612, 907},
     {1593, 896},
     {1574, 885},
     {1555, 874},
     {1536, 864},
     {1516, 853},
     {1497, 842},
     {1478, 831},
     {1459, 820},
     {1440, 810},
     {1420, 799},
     {1401, 788},
     {1382, 777},
     {1363, 766},
     {1344, 756},
     {1324, 745},
     {1305, 734},
     {1286, 723},
     {1267, 712},
     {1248, 702},
     {1228, 691},
     {1209, 680},
     {1190, 669},
     {1171, 658},
     {1152, 648},
     {1132, 637},
     {1113, 626},
     {1094, 615},
     {1075, 604},
     {1056, 594},
     {1036, 583},
     {1017, 572},
     {998, 561},
     {979, 550},
     {960, 540},
     {940, 529},
     {921, 518},
     {902, 507},
     {883, 496},
     {864, 486},
     {844, 475},
     {825, 464},
     {806, 453},
     {787, 442},
     {768, 432},
     {748, 421},
     {729, 410},
     {710, 399},
     {691, 388},
     {672, 378},
     {652, 367},
     {633, 356},
     {614, 345},
     {595, 334},
     {576, 324},
     {556, 313},
     {537, 302},
     {518, 291},
     {499, 280},
     {480, 270},
     {460, 259},
     {441, 248},
     {422, 237},
     {403, 226},
     {384, 216},
     {384, 216},
     {403, 226},
     {422, 237},
     {441, 248},
     {460, 259},
     {480, 270},
     {499, 280},
     {518, 291},
     {537, 302},
     {556, 313},
     {575, 323},
     {595, 334},
     {614, 345},
     {633, 356},
     {652, 367},
     {671, 377},
     {691, 388},
     {710, 399},
     {729, 410},
     {748, 421},
     {767, 431},
     {787, 442},
     {806, 453},
     {825, 464},
     {844, 475},
     {863, 485},
     {883, 496},
     {902, 507},
     {921, 518},
     {940, 529},
     {959, 539},
     {979, 550},
     {998, 561},
     {1017, 572},
     {1036, 583},
     {1055, 593},
     {1075, 604},
     {1094, 615},
     {1113, 626},
     {1132, 637},
     {1151, 647},
     {1171, 658},
     {1190, 669},
     {1209, 680},
     {1228, 691},
     {1247, 701},
     {1267, 712},
     {1286, 723},
     {1305, 734},
     {1324, 745},
     {1343, 755},
     {1363, 766},
     {1382, 777},
     {1401, 788},
     {1420, 799},
     {1439, 809},
     {1459, 820},
     {1478, 831},
     {1497, 842},
     {1516, 853},
     {1535, 863},
     {1555, 874},
     {1574, 885},
     {1593, 896},
     {1612, 907},
     {1631, 917},
     {1651, 928},
     {1670, 939},
     {1689, 950},
     {1708, 961},
     {1727, 971},
     {1747, 982},
     {1766, 993},
     {1785, 1004},
     {1804, 1015},
     {1823, 1025},
     {1843, 1036},
     {1862, 1047},
     {1881, 1058},
     {1900, 1069},
     {1919, 1079},
     {1920, 1080},
     {1900, 1069},
     {1881, 1058},
     {1862, 1047},
     {1843, 1036},
     {1824, 1026},
     {1804, 1015},
     {1785, 1004},
     {1766, 993},
     {1747, 982},
     {1728, 972},
     {1708, 961},
     {1689, 950},
     {1670, 939},
     {1651, 928},
     {1632, 918},
     {1612, 907},
     {1593, 896},
     {1574, 885},
     {1555, 874},
     {1536, 864},
     {1516, 853},
     {1497, 842},
     {1478, 831},
     {1459, 820},
     {1440, 810},
     {1420, 799},
     {1401, 788},
     {1382, 777},
     {1363, 766},
     {1344, 756},
     {1324, 745},
     {1305, 734},
     {1286, 723},
     {1267, 712},
     {1248, 702},
     {1228, 691},
     {1209, 680}};

AR_CHAR *s8WarpFiles[] =
    {
        "./cmodel_dumpped_bins_1101_1532/data-0-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-1-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-2-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-3-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-4-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-5-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-6-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-7-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-8-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-9-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-10-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-11-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-12-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-13-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-14-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-15-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-16-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-17-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-18-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-19-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-20-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-21-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-22-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-23-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-24-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-25-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-26-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-27-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-28-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-29-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-30-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-31-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-32-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-33-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-34-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-35-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-36-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-37-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-38-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-39-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-40-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-41-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-42-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-43-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-44-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-45-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-46-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-47-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-48-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-49-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-50-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-51-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-52-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-53-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-54-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-55-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-56-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-57-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-58-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-59-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-60-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-61-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-62-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-63-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-64-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-65-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-66-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-67-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-68-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-69-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-70-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-71-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-72-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-73-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-74-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-75-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-76-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-77-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-78-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-79-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-80-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-81-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-82-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-83-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-84-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-85-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-86-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-87-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-88-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-89-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-90-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-91-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-92-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-93-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-94-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-95-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-96-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-97-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-98-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-99-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-100-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-101-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-102-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-103-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-104-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-105-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-106-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-107-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-108-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-109-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-110-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-111-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-112-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-113-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-114-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-115-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-116-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-117-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-118-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-119-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-120-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-121-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-122-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-123-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-124-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-125-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-126-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-127-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-128-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-129-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-130-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-131-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-132-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-133-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-134-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-135-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-136-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-137-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-138-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-139-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-140-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-141-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-142-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-143-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-144-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-145-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-146-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-147-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-148-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-149-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-150-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-151-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-152-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-153-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-154-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-155-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-156-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-157-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-158-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-159-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-160-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-161-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-162-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-163-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-164-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-165-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-166-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-167-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-168-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-169-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-170-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-171-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-172-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-173-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-174-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-175-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-176-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-177-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-178-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-179-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-180-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-181-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-182-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-183-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-184-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-185-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-186-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-187-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-188-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-189-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-190-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-191-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-192-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-193-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-194-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-195-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-196-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-197-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-198-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-199-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-200-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-201-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-202-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-203-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-204-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-205-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-206-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-207-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-208-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-209-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-210-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-211-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-212-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-213-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-214-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-215-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-216-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-217-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-218-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-219-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-220-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-221-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-222-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-223-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-224-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-225-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-226-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-227-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-228-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-229-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-230-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-231-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-232-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-233-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-234-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-235-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-236-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-237-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-238-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-239-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-240-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-241-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-242-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-243-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-244-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-245-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-246-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-247-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-248-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-249-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-250-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-251-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-252-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-253-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-254-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-255-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-256-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-257-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-258-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-259-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-260-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-261-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-262-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-263-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-264-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-265-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-266-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-267-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-268-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-269-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-270-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-271-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-272-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-273-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-274-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-275-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-276-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-277-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-278-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-279-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-280-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-281-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-282-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-283-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-284-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-285-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-286-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-287-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-288-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-289-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-290-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-291-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-292-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-293-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-294-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-295-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-296-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-297-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-298-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-299-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-300-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-301-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-302-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-303-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-304-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-305-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-306-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-307-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-308-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-309-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-310-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-311-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-312-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-313-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-314-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-315-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-316-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-317-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-318-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-319-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-320-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-321-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-322-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-323-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-324-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-325-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-326-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-327-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-328-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-329-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-330-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-331-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-332-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-333-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-334-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-335-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-336-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-337-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-338-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-339-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-340-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-341-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-342-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-343-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-344-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-345-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-346-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-347-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-348-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-349-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-350-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-351-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-352-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-353-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-354-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-355-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-356-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-357-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-358-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-359-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-360-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-361-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-362-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-363-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-364-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-365-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-366-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-367-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-368-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-369-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-370-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-371-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-372-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-373-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-374-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-375-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-376-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-377-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-378-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-379-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-380-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-381-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-382-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-383-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-384-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-385-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-386-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-387-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-388-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-389-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-390-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-391-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-392-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-393-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-394-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-395-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-396-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-397-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-398-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-399-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-400-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-401-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-402-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-403-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-404-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-405-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-406-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-407-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-408-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-409-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-410-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-411-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-412-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-413-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-414-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-415-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-416-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-417-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-418-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-419-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-420-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-421-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-422-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-423-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-424-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-425-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-426-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-427-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-428-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-429-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-430-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-431-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-432-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-433-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-434-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-435-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-436-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-437-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-438-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-439-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-440-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-441-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-442-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-443-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-444-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-445-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-446-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-447-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-448-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-449-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-450-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-451-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-452-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-453-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-454-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-455-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-456-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-457-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-458-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-459-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-460-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-461-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-462-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-463-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-464-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-465-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-466-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-467-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-468-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-469-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-470-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-471-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-472-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-473-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-474-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-475-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-476-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-477-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-478-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-479-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-480-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-481-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-482-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-483-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-484-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-485-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-486-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-487-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-488-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-489-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-490-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-491-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-492-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-493-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-494-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-495-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-496-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-497-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-498-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-499-left/warp_cmodel.dat",
        "./cmodel_dumpped_bins_1101_1532/data-500-left/warp_cmodel.dat"};

typedef struct
{
    AR_U64 PaAddr;
    void *VaAddr;
    AR_U32 u32Width;
    AR_U32 u32Height;
} GDC_MESH_S;

#define AR_DPRX_EDID_SIZE (256)
static AR_U8 edid_groups[][AR_DPRX_EDID_SIZE] = {

    // 1080p 30
    {
        0x00,
        0xFF,
        0xFF,
        0xFF,
        0xFF,
        0xFF,
        0xFF,
        0x00,
        0x36,
        0x47,
        0x32,
        0x31,
        0x00,
        0x88,
        0x88,
        0x88,
        0x13,
        0x21,
        0x01,
        0x03,
        0x80,
        0x0C,
        0x07,
        0x78,
        0x0A,
        0x9E,
        0xE0,
        0xA7,
        0x54,
        0x48,
        0x99,
        0x23,
        0x10,
        0x50,
        0x54,
        0x00,
        0x00,
        0x00,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x1D,
        0x80,
        0x18,
        0x71,
        0x38,
        0x2D,
        0x40,
        0x58,
        0x2C,
        0x45,
        0x00,
        0x80,
        0x38,
        0x74,
        0x00,
        0x00,
        0x06,
        0x00,
        0x00,
        0x00,
        0xFE,
        0x00,
        0x41,
        0x72,
        0x74,
        0x6F,
        0x73,
        0x79,
        0x6E,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x00,
        0x00,
        0x00,
        0xFD,
        0x00,
        0x32,
        0x82,
        0x14,
        0x3C,
        0x3C,
        0x00,
        0x0A,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x00,
        0x00,
        0x00,
        0xFC,
        0x00,
        0x6E,
        0x72,
        0x65,
        0x61,
        0x6C,
        0x20,
        0x61,
        0x69,
        0x72,
        0x0A,
        0x20,
        0x20,
        0x20,
        0x00,
        0xB0,
    },
    // 1080p 60
    {
        0x00,
        0xff,
        0xff,
        0xff,
        0xff,
        0xff,
        0xff,
        0x00,
        0x26,
        0x85,
        0x02,
        0x66,
        0x21,
        0x60,
        0x00,
        0x00,
        0x00,
        0x17,
        0x01,
        0x03,
        0x80,
        0x73,
        0x41,
        0x78,
        0x2a,
        0x7c,
        0x11,
        0x9e,
        0x59,
        0x47,
        0x9b,
        0x27,
        0x10,
        0x50,
        0x54,
        0x00,
        0x00,
        0x00,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x02,
        0x3a,
        0x80,
        0x18,
        0x71,
        0x38,
        0x2d,
        0x40,
        0x58,
        0x2c,
        0x45,
        0x00,
        0x10,
        0x09,
        0x00,
        0x00,
        0x00,
        0x1e,
        0x00,
        0x00,
        0x00,
        0xfc,
        0x00,
        0x0a,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x00,
        0x00,
        0x00,
        0xfc,
        0x00,
        0x49,
        0x54,
        0x45,
        0x36,
        0x38,
        0x30,
        0x32,
        0x0a,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x00,
        0x00,
        0x00,
        0xfd,
        0x00,
        0x30,
        0x7a,
        0x0f,
        0x50,
        0x10,
        0x00,
        0x0a,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x01,
        0x87,

        0x02,
        0x03,
        0x14,
        0x41,
        0x41,
        0x90,
        0x23,
        0x09,
        0x07,
        0x07,
        0x83,
        0x01,
        0x00,
        0x00,
        0x65,
        0x03,
        0x0c,
        0x00,
        0x10,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x00,
        0x93,
    },
    // 3840*1080 15
    {
        0x00,
        0xFF,
        0xFF,
        0xFF,
        0xFF,
        0xFF,
        0xFF,
        0x00,
        0x36,
        0x47,
        0x32,
        0x31,
        0x00,
        0x88,
        0x88,
        0x88,
        0x13,
        0x21,
        0x01,
        0x03,
        0x80,
        0x0C,
        0x07,
        0x78,
        0x0A,
        0x9E,
        0xE0,
        0xA7,
        0x54,
        0x48,
        0x99,
        0x23,
        0x10,
        0x50,
        0x54,
        0x00,
        0x00,
        0x00,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x01,
        0x1D,
        0x00,
        0x30,
        0xF2,
        0x38,
        0x2D,
        0x40,
        0x40,
        0x20,
        0x95,
        0x00,
        0x00,
        0x38,
        0xF4,
        0x00,
        0x00,
        0x06,
        0x00,
        0x00,
        0x00,
        0xFE,
        0x00,
        0x41,
        0x72,
        0x74,
        0x6F,
        0x73,
        0x79,
        0x6E,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x00,
        0x00,
        0x00,
        0xFD,
        0x00,
        0x32,
        0x82,
        0x14,
        0x3C,
        0x3C,
        0x00,
        0x0A,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x20,
        0x00,
        0x00,
        0x00,
        0xFC,
        0x00,
        0x6E,
        0x72,
        0x65,
        0x61,
        0x6C,
        0x20,
        0x61,
        0x69,
        0x72,
        0x0A,
        0x20,
        0x20,
        0x20,
        0x00,
        0x6B,
    },
    // 4k 30
    {
        0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x05, 0xD7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0x22, 0x01, 0x04, 0xA5, 0x32, 0x1F, 0x78, 0x07, 0xEE, 0x95, 0xA3, 0x54, 0x4C, 0x99, 0x26,
        0x0F, 0x50, 0x54, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0x74, 0x00, 0x30, 0xF2, 0x70, 0x5A, 0x80, 0xB0, 0x58,
        0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xFD, 0x00, 0x17, 0xF0, 0x0F,
        0xFF, 0x1E, 0x00, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x55,
        0x48, 0x44, 0x54, 0x56, 0x20, 0x32, 0x31, 0x36, 0x30, 0x70, 0x0A, 0x20, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xAE,
        0x02, 0x03, 0x0A, 0x40, 0x23, 0x09, 0x06, 0x07, 0x41, 0xDF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58}};

typedef struct stSAMPLE_VOU_ThreadCtrl_Info
{
    VI_DEV ViDev;
    VI_PIPE ViPipe;
    VI_CHN ViChn;
    AR_U32 u32Width;
    AR_U32 u32Height;
    AR_S32 gdc_core_id;

    AR_BOOL bQuit;
    AR_BOOL bDestroy;

    pthread_t tid;
} SAMPLE_ThreadCtrl_Info;

AR_VOID SAMPLE_StopUserThd(SAMPLE_ThreadCtrl_Info *pstThdInfo)
{
    pstThdInfo->bQuit = AR_TRUE;
    pstThdInfo->bDestroy = AR_TRUE;
    pthread_join(pstThdInfo->tid, AR_NULL);
    return;
}

static AR_S32 SAMPLE_alloc_vb_buffer(VIDEO_FRAME_INFO_S *p_frame_info, AR_U32 align)
{
    AR_U64 buf_size;
    VB_BLK block_handle;
    VB_CAL_CONFIG_S stCalConfig;
    VIDEO_FRAME_S *p_video_frame = &p_frame_info->stVFrame;
    AR_S32 ret = AR_OK;

    // 初始化p_frame_info结构体
    p_frame_info->u32PoolId = VB_INVALID_POOLID;
    p_frame_info->enModId = (MOD_ID_E)VB_UID_USER;

    // 获取
    COMMON_GetPicBufferConfig(p_video_frame->u32Width, p_video_frame->u32Height,
                              p_video_frame->enPixelFormat, DATA_BITWIDTH_8,
                              COMPRESS_MODE_NONE, align, &stCalConfig);
    buf_size = stCalConfig.u32VBSize;
    ar_assert(stCalConfig.u32VBSize);

    block_handle = pfn_AR_MPI_VB_GetBlock(p_frame_info->u32PoolId, buf_size, NULL);
    if (block_handle == VB_INVALID_HANDLE)
    {
        SAMPLE_PRT("get block failed buf_size=%lld\n", buf_size);
        return block_handle;
    }

    p_frame_info->stVFrame.u32Stride[0] = stCalConfig.u32MainStride;
    p_frame_info->stVFrame.u32Stride[1] = stCalConfig.u32MainStride / 2;
    p_frame_info->stVFrame.u32Stride[2] = stCalConfig.u32MainStride / 2;

    ret = pfn_AR_MPI_VB_Handle2PoolId(block_handle);
    if (ret < 0)
    {
        SAMPLE_PRT("AR_MPI_VB_Handle2PoolId failed ret=%x\n", ret);
        return ret;
    }
    VB_POOL pool_id = ret;

    ret = pfn_AR_MPI_VB_MmapPool(pool_id);
    if (ret < 0)
    {
        SAMPLE_PRT("AR_MPI_VB_MmapPool failed ret=%x\n", ret);
        return ret;
    }

    p_frame_info->stVFrame.u64PhyAddr[0] = pfn_AR_MPI_VB_Handle2PhysAddr(block_handle);
    if (p_frame_info->stVFrame.u64PhyAddr[0] == 0)
    {
        SAMPLE_PRT("AR_MPI_VB_Handle2PhysAddr failed\n");
        return AR_FAILURE;
    }

    ret = pfn_AR_MPI_VB_GetBlockVirAddr(pool_id, p_frame_info->stVFrame.u64PhyAddr[0], (AR_VOID **)&p_frame_info->stVFrame.u64VirAddr[0]);
    if (ret < 0)
    {
        SAMPLE_PRT("AR_MPI_VB_GetBlockVirAddr failed ret=%x\n", ret);
        return ret;
    }

    p_frame_info->stVFrame.u64PhyAddr[1] = p_frame_info->stVFrame.u64PhyAddr[0] + stCalConfig.u32MainYSize;
    p_frame_info->stVFrame.u64VirAddr[1] = p_frame_info->stVFrame.u64VirAddr[0] + stCalConfig.u32MainYSize;

    p_frame_info->stVFrame.u64PhyAddr[2] = p_frame_info->stVFrame.u64PhyAddr[1] + stCalConfig.u32MainYSize / 4;
    p_frame_info->stVFrame.u64VirAddr[2] = p_frame_info->stVFrame.u64VirAddr[1] + stCalConfig.u32MainYSize / 4;

    p_frame_info->stVFrame.u32Len[0] = stCalConfig.u32MainYSize;
    p_frame_info->stVFrame.u32Len[1] = stCalConfig.u32MainYSize / 4;
    p_frame_info->stVFrame.u32Len[2] = stCalConfig.u32MainYSize / 4;

    SAMPLE_PRT("alloc %lld bytes @ %llx blk=%d pool_id=%d\n", buf_size, p_frame_info->stVFrame.u64PhyAddr[0], block_handle, pool_id);

    return AR_SUCCESS;
}

static AR_S32 SAMPLE_free_vb_buffer(VIDEO_FRAME_INFO_S *p_frame_info)
{
    AR_U64 pa = p_frame_info->stVFrame.u64PhyAddr[0];
    VB_BLK block_handle = pfn_AR_MPI_VB_PhysAddr2Handle(pa);
    AR_S32 ret = pfn_AR_MPI_VB_ReleaseBlock(block_handle);

    SAMPLE_PRT("free %llx blk=%d ret=%x\n", pa, block_handle, ret);

    return ret;
}

static AR_VOID SAMPLE_VO_GetUserPubBaseAttr(VO_PUB_ATTR_S *pstPubAttr)
{
    pstPubAttr->u32BgColor = COLOR_RGB_BLUE;
    pstPubAttr->enIntfSync = VO_OUTPUT_USER;
    pstPubAttr->stSyncInfo.bSynm = 0;
    pstPubAttr->stSyncInfo.u8Intfb = 0;
    pstPubAttr->stSyncInfo.bIop = 1;

    pstPubAttr->stSyncInfo.u16Hmid = 1;
    pstPubAttr->stSyncInfo.u16Bvact = 1;
    pstPubAttr->stSyncInfo.u16Bvbb = 1;
    pstPubAttr->stSyncInfo.u16Bvfb = 1;

    pstPubAttr->stSyncInfo.bIdv = 0;
    pstPubAttr->stSyncInfo.bIhs = 0;
    pstPubAttr->stSyncInfo.bIvs = 0;
}

static AR_VOID SAMPLE_VO_GetUserLayerAttr(VO_VIDEO_LAYER_ATTR_S *pstLayerAttr, SIZE_S *pstDevSize)
{
    pstLayerAttr->bClusterMode = AR_FALSE;
    pstLayerAttr->bDoubleFrame = AR_FALSE;
    pstLayerAttr->enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    pstLayerAttr->enPixFormat = PIXEL_FORMAT_YVU_PLANAR_420;

    pstLayerAttr->stDispRect.s32X = 0;
    pstLayerAttr->stDispRect.s32Y = 0;
    pstLayerAttr->stDispRect.u32Height = pstDevSize->u32Height;
    pstLayerAttr->stDispRect.u32Width = pstDevSize->u32Width;

    pstLayerAttr->stImageSize.u32Height = pstDevSize->u32Height;
    pstLayerAttr->stImageSize.u32Width = pstDevSize->u32Width;
}

static AR_VOID SAMPLE_AR_MPI_VIN_OpenDev_Local(AR_S32 mode, AR_S32 hdr_freq, AR_S32 vif_freq, AR_S32 isp_freq, AR_S32 mipi_freq)
{
    VI_DEV_PROP_S Prop = {0};
    // for no need here now, move to sys init

    if (mode == 0)
    {
        Prop.cam_mode = VIN_CAMERA_NORMAL;
    }
    else if (mode == 1)
    {
        Prop.cam_mode = VIN_CAMERA_OFFLINE;
    }
    else if (mode == 2)
    {
        Prop.cam_mode = VIN_CMAERA_MULTI_MODE;
    }
    else
    {
        Prop.cam_mode = VIN_CAMERA_OFFLINE;
    }
    // cfg the fre
    Prop.hdr_fre_mod = 1;
    Prop.hdr_fre_hz = hdr_freq;
    Prop.vif_fre_mod = 1;
    Prop.vif_fre_hz = vif_freq;
    Prop.isp_fre_mod = 1;
    Prop.isp_fre_hz = isp_freq;
    Prop.mipi_fre_mod = 1;
    Prop.mipi_fre_hz = mipi_freq;
    Prop.cvisp0_fre_hz = isp_freq;
    Prop.cvisp1_fre_hz = isp_freq;
    Prop.dpvif_fre_hz = vif_freq;
    Prop.cvvif0_fre_hz = isp_freq;
    Prop.cvvif0_fre_hz = isp_freq;

    pfn_AR_MPI_VIN_OpenDev(&Prop);
    return;
}

static AR_S32 SAMPLE_GDC_Load_Params_General_Local(void *ppvParaVa, AR_CHAR *s8MeshFileName)
{
    if (!ppvParaVa)
    {
        printf("pstParam is NULL\r\n");
        return -1;
    }

    int s32Ret = 0;

    if (s8MeshFileName)
    {
        FILE *pMeshFileFp = 0;
        pMeshFileFp = fopen(s8MeshFileName, "r");
        if (!pMeshFileFp)
        {
            printf("open file %s fail!!\n", s8MeshFileName);
            return -1;
        }
        fseek(pMeshFileFp, 0L, SEEK_END);
        AR_S32 MeshFileLen = ftell(pMeshFileFp);
        rewind(pMeshFileFp);
        // printf("MeshFileLen %d \r\n",MeshFileLen);
        s32Ret = fread(ppvParaVa, 1, MeshFileLen, pMeshFileFp);
        if (s32Ret != MeshFileLen)
        {
            printf("read err :%d\n", s32Ret, MeshFileLen);
            return -1;
        }
        fclose(pMeshFileFp);
        pMeshFileFp = 0;

        float *pm = (float *)(ppvParaVa);
        for (int i = 0; i < 64; i = i + 8)
        {
            // printf("mesh[%d-%d] %f %f %f %f %f %f %f %f ",i,i+7,pm[i],pm[i+1],pm[i+2],pm[i+3],pm[i+4],pm[i+5],pm[i+6],pm[i+7]);
            // printf("\r\n");
        }
    }

    return 0;
}

static void SAMPLE_GDC_DP_RESHAPE_Local(VI_PIPE ViPipe, VI_CHN ViChn, SIZE_S *pstRspSize, AR_U64 id)
{
    if (pstRspSize == NULL)
    {
        assert(0);
    }

    SIZE_S stSize = {0};
    stSize.u32Width = DPTEST_ALIGNE_TO(pstRspSize[id].u32Width, 4);
    stSize.u32Height = DPTEST_ALIGNE_TO(pstRspSize[id].u32Height, 4);
    if (stSize.u32Height > 1080 || stSize.u32Width > 1920)
    {
        AR_LOG_RAW("size error===============\r\n ");
        stSize.u32Height = 1080;
        stSize.u32Width = 1920;
    }

    RECT_S stCrop = {0};
    RECT_S stPostCrop = {0};
    int s32Ret = pfn_AR_MPI_VI_ReShapeChExt(ViPipe, ViChn, stSize, stCrop, stPostCrop);
    if (s32Ret < 0)
    {
        AR_LOG_RAW("AR_MPI_VI_ReShapeCh error\r\n ");
    }
}

void SAMPLE_GDC_LOAD_MESH_DATA_Local(SIZE_S *pstMsSize, GDC_MESH_S *pstMeshData)
{
    int s32Ret = 0;
    char s8MeshMmz[64] = {0};
    char s8MeshFileName[128] = {0};
    int u32ParaMeshDefSize = 32 * 1024;
    for (int x = 0; x < MESH_SIZE_NUM; x++)
    {
        sprintf(s8MeshMmz, "Mesh_%d", x);
        s32Ret = pfn_ar_hal_sys_mmz_alloc(&pstMeshData[x].PaAddr, &pstMeshData[x].VaAddr, "meshdata", NULL, u32ParaMeshDefSize);
        if (s32Ret)
        {
            printf(" malloc Mesh addr error!\r\n");
            return AR_NULL;
        }
        memset((AR_CHAR *)pstMeshData[x].VaAddr, 0, u32ParaMeshDefSize);
        pstMeshData[x].u32Width = pstMsSize[x].u32Width;
        pstMeshData[x].u32Height = pstMsSize[x].u32Height;
        sprintf(s8MeshFileName, "/mnt/dptest/mesh_data/mesh_rsz_%dx%d.dat", pstMsSize[x].u32Width, pstMsSize[x].u32Height);

        SAMPLE_GDC_Load_Params_General_Local(pstMeshData[x].VaAddr, s8MeshFileName);
    }
}

static AR_S32 GDC_Load_Warp_Data_Array_FakeIdentity(AR_U64 *pu64ParaPa, void **ppvParaVa, int warpFileCount, AR_CHAR **s8WarpFiles)
{
    AR_S32 s32Ret = -1;
    if ((!pu64ParaPa) || (!ppvParaVa))
    {
        printf("pu64ParaPa || ppvParaVa is NULL\r\n");
        return -1;
    }
    /*******************Warp Config******************************/
    for (int i = 0; i < warpFileCount; i++)
    {
        pu64ParaPa[i] = 0;
        ppvParaVa[i] = NULL;

        int step = 9 * sizeof(float);
        uint32_t WarpFileLen = step * 35;
        s32Ret = pfn_ar_hal_sys_mmz_alloc(&pu64ParaPa[i], &ppvParaVa[i], "Warp" + i, NULL, WarpFileLen);
        if (s32Ret)
        {
            printf(" get Warp addr error! for %dth warp\r\n", i);
            return -1;
        }
        memset((AR_CHAR *)ppvParaVa[i], 0, WarpFileLen);
        float identity_mat33[9] = {1.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 1.0f};
        for (int idx = 0; idx < 35; idx++)
        {
            memcpy(((AR_CHAR *)ppvParaVa[i]) + idx * step, &identity_mat33[0], step);
        }
        // printf("u64WarpPa[%d] %llx\r\n", i, pu64ParaPa[i]);
    }

    return 0;
}
static AR_S32 GDC_Load_Warp_Data_Array(AR_U64 *pu64ParaPa, void **ppvParaVa, int warpFileCount, AR_CHAR **s8WarpFiles)
{
    AR_S32 s32Ret = -1;
    if ((!pu64ParaPa) || (!ppvParaVa))
    {
        printf("pu64ParaPa || ppvParaVa is NULL\r\n");
        return -1;
    }
    /*******************Warp Config******************************/
    for (int i = 0; i < warpFileCount; i++)
    {
        pu64ParaPa[i] = 0;
        ppvParaVa[i] = NULL;

        FILE *pWarpFileFp = 0;
        pWarpFileFp = fopen(s8WarpFiles[i], "r");
        if (!pWarpFileFp)
        {
            printf("open file %s fail!!\n", s8WarpFiles[i]);
            return -1;
        }
        fseek(pWarpFileFp, 0L, SEEK_END);
        AR_S32 WarpFileLen = ftell(pWarpFileFp);
        // printf("Malloc %dth WarpFileLen %d \r\n", i, WarpFileLen);
        s32Ret = pfn_ar_hal_sys_mmz_alloc(&pu64ParaPa[i], &ppvParaVa[i], "Warp" + i, NULL, WarpFileLen);
        if (s32Ret)
        {
            printf(" get Warp addr error! for %dth warp\r\n", i);
            return -1;
        }
        memset((AR_CHAR *)ppvParaVa[i], 0, WarpFileLen);

        rewind(pWarpFileFp);
        s32Ret = fread(ppvParaVa[i], 1, WarpFileLen, pWarpFileFp);
        if (s32Ret != WarpFileLen)
        {
            printf("read [%dth] warp file err :%d\n", i, s32Ret);
            return -1;
        }

        fclose(pWarpFileFp);
        pWarpFileFp = 0;
        // printf("u64WarpPa[%d] %llx\r\n", i, pu64ParaPa[i]);
    }

    return 0;
}

AR_S32 test1_dp_de(AR_VOID)
{
    AR_S32 s32Ret;
    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_colorbar";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    int w_skip = 16;
    int h_skip = 16;
    int dest_w = 384; // stSize_ch.u32Width/2;
    int dest_h = 216; // stSize_ch.u32Height/2;

    int format = 0;
    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(0, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    // init display
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 2048;
    stLayerAttr.u32Stride[1] = 2048;
    stLayerAttr.u32Stride[2] = 2048;
    if (format == 0)
    {
        stLayerAttr.u32Stride[1] = 1024;
        stLayerAttr.u32Stride[2] = 1024;
    }
    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    /* ENABLE VO LAYER */
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // four layout
    AR_S32 mesh_w = 1920;
    AR_S32 mesh_h = 1080;
    STRU_AR_HAL_VO_RECT chan_pos = {0};
    chan_pos.x = 0;
    chan_pos.y = 0;
    chan_pos.w = mesh_w;
    chan_pos.h = mesh_h;
    // ar_vo_surface_init(&g_vo_obj,0,&chan_pos);

    VO_CHN_ATTR_S astChnAttr = {0};
    /* SET AND ENABLE VO CHN */
    AR_U32 u32Height = stDevSize.u32Height / VoChnNum;
    AR_U32 u32Width = stDevSize.u32Width / VoChnNum;
    astChnAttr.bDeflicker = AR_FALSE;
    astChnAttr.u32Priority = 0;
    astChnAttr.stRect.s32X = chan_pos.x;
    astChnAttr.stRect.s32Y = chan_pos.y;
    astChnAttr.stRect.u32Height = chan_pos.h;
    astChnAttr.stRect.u32Width = chan_pos.w;
    CHECK_RET(pfn_AR_MPI_VO_SetChnAttr(VoLayer, 0, &astChnAttr), "AR_MPI_VO_SetChnAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableChn(VoLayer, 0), "AR_MPI_VO_EnableChn");

    SIZE_S stSize_dest;
    stSize_dest.u32Width = stSize_ch.u32Width;
    stSize_dest.u32Height = stSize_ch.u32Height;

    VIDEO_FRAME_INFO_S FrameInfo = {0};
    AR_S32 ref = 0;
    RECT_S stCrop = {0};
    RECT_S stPostCrop = {0};
    int step_dir = 0;
    while (1)
    {
        int status = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 5000);
        if (status)
        {
            continue;
        }

        ref = 0;
#if 1
        if (step_dir == 0)
        {
            stSize_dest.u32Width -= w_skip;
            stSize_dest.u32Height -= h_skip;

            if (stSize_dest.u32Width <= dest_w && stSize_dest.u32Height <= dest_h)
            {
                step_dir = 1;
            }

            if (stSize_dest.u32Width <= dest_w)
            {
                stSize_dest.u32Width = dest_w;
            }
            if (stSize_dest.u32Height <= dest_h)
            {
                stSize_dest.u32Height = dest_h;
            }
        }
        else
        {
            stSize_dest.u32Width += w_skip;
            stSize_dest.u32Height += h_skip;

            if (stSize_dest.u32Width >= stSize_ch.u32Width && stSize_dest.u32Height >= stSize_ch.u32Height)
            {
                step_dir = 0;
            }

            if (stSize_dest.u32Width >= stSize_ch.u32Width)
            {
                stSize_dest.u32Width = stSize_ch.u32Width;
            }
            if (stSize_dest.u32Height >= stSize_ch.u32Height)
            {
                stSize_dest.u32Height = stSize_ch.u32Height;
            }
        }
#else
        stSize_dest.u32Width = 1000;
        stSize_dest.u32Height = 800;
#endif

        // ar_printf("reshape to %d %d \n",stSize_dest.u32Width,stSize_dest.u32Height);
        pfn_AR_MPI_VI_ReShapeChExt(ViPipe, ViChn, stSize_dest, stCrop, stPostCrop);

        // push to display
        FrameInfo.stVFrame.u32Width = 1920;
        FrameInfo.stVFrame.u32Height = 1080;
        pfn_AR_MPI_VO_SendFrame(0, 0, &FrameInfo, 0);

        pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
    }

    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }

    pfn_AR_MPI_ISP_Exit(ViPipe);

    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    SAMPLE_COMM_SYS_Exit();
}

AR_S32 SAMPLE_FREERUN_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST2(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {
        // if(sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            usleep(2 * 1000);
            // usleep(2.5*1000);  //is not enough
        }
    }
    return 0;
}

AR_S32 test2_dp_gdc_de_freerun_ld(AR_VOID)
{
    AR_S32 s32Ret;

    // Vi Config
    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    AR_CHAR *sensor_name = "dp_colorbar";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    int sensor_mode = 0x80;
    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }

    stSize = default_attr.stPubAttr.stSnsSize;
    s8I2cDev = 2;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    AR_S32 isp_fre = 300000000;
    AR_S32 vif_fre = 300000000;
    AR_S32 pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(0, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    // Vo Config
    AR_S32 VoDev = 0;
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_FREERUN_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST2;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    VO_PUB_ATTR_S stPubAttr = {0};

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = 1;

    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr), "AR_MPI_VO_SetPubAttr");
    pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_GDC0;
    stStartAttr.stAutoAttr.u32InitLineCnt = 1;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    /* ENABLE VO DEV */
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_Enable(VoDev), "AR_MPI_VO_Enable");

    // video layer
    /*SET VO LAYER ATTR*/
    AR_S32 VoLayer = 0x0;
    SIZE_S stDevSize;
    SIZE_S stFrameSize = {1920, 1080};
    stDevSize.u32Width = stFrameSize.u32Width;
    stDevSize.u32Height = stFrameSize.u32Height;
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {{0}};
    SAMPLE_VO_GetUserLayerAttr(&stLayerAttr, &stDevSize);
    stLayerAttr.u32DispFrmRt = 30;
    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    /* ENABLE VO LAYER */
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // Gdc Config
    AR_GDC_ADV_TRANSFORM_S stParam = {0};
    stParam.stOutBuffer.astChannels[0].u32Stride = 4096;
    stParam.stOutBuffer.astChannels[1].u32Stride = 4096;
    stParam.stOutBuffer.astChannels[2].u32Stride = 4096;

    stParam.stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
    stParam.stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
    stParam.stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
    stParam.stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
    stParam.stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
    stParam.stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

    stParam.s32CoreId = 0;
    stParam.s32NonBlock = 1;
    stParam.stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
    stParam.stGdcParam.stStartCfg.u8FrameStart = 1;
    stParam.stGdcParam.stStartCfg.u8StartMode = 3;   // free_run mode
    stParam.stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

    stParam.stGdcParam.stMeshCfg.u32MeshMode = 3;
    stParam.stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
    stParam.stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 1;       // 2; //disable

    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[0] = 0xbf800000;
    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[1] = 0;
    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[2] = 0x44f00000;
    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[3] = 0;
    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[4] = 0x3f800000;
    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[5] = 0;
    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[6] = 0;
    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[7] = 0;
    stParam.stGdcParam.stWarpCfg.u32ApbMatrix[8] = 0x3f800000;

    stParam.u8LdEn = 1;
    stParam.stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
    stParam.stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    stParam.stLdParam.stOutLowdelay.u32Lines64Enable = 0;
    stParam.stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

    int fisrt_start = 1;
    VIDEO_FRAME_INFO_S FrameInfo;
    while (1)
    {
        s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
            continue;
        }

        stParam.stInBuffer.u32IsVB = 1;
        stParam.stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
        stParam.stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
        stParam.stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
        stParam.stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
        stParam.stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
        // lb_lowdelay stride must 4096
        stParam.stOutBuffer.u32Width = FrameInfo.stVFrame.u32Width;
        stParam.stOutBuffer.u32Height = FrameInfo.stVFrame.u32Height;
        stParam.stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
        stParam.stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
        stParam.stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
        stParam.stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
        stParam.stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
        stParam.stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

        stParam.stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
        stParam.stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
        stParam.stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
        stParam.stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);

        if (fisrt_start)
        {
            fisrt_start = 0;
        }
        else
        {
            stParam.stGdcParam.stStartCfg.u8FrameStart = 0;
        }

        s32Ret = pfn_AR_MPI_GDC_ADV_Process(&stParam);
        if (s32Ret < 0)
        {
            pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            printf("AR_MPI_GDC_ADV_Process fail\r\n");
            assert(0);
        }

        s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
            return -1;
        }
    }

    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }

    pfn_AR_MPI_ISP_Exit(ViPipe);

    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    SAMPLE_COMM_SYS_Exit();

    return s32Ret;
}

AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST3(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    VIDEO_FRAME_INFO_S FrameInfo = {0};
    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {
        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            // AR_LOG_RAW("dev_id:%d irq_type:%02x irq_time_ns:%lu \r\n", dev_id,
            // sub_info->u32IrqType,
            // sub_info->u64IrqTimeNs);

            AR_S32 s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }

            g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[0].stOutBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stOutBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
            ;
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                assert(0);
            }

            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
        }
    }

    return 0;
}
AR_S32 test3_dp_gdc_de_shadow_ld(AR_VOID)
{
    AR_S32 s32Ret;

    // Vi Config
    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    AR_CHAR *sensor_name = "dp_colorbar";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    int sensor_mode = 0x80;
    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }

    stSize = default_attr.stPubAttr.stSnsSize;
    s8I2cDev = 2;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    AR_S32 isp_fre = 300000000;
    AR_S32 vif_fre = 300000000;
    AR_S32 pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(0, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    // Vo Config
    AR_S32 VoDev = 0;
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST3;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    VO_PUB_ATTR_S stPubAttr = {0};

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = 1;

    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr), "AR_MPI_VO_SetPubAttr");
    pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_GDC0;
    stStartAttr.stAutoAttr.u32InitLineCnt = 1;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    /* ENABLE VO DEV */
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_Enable(VoDev), "AR_MPI_VO_Enable");

    // video layer
    /*SET VO LAYER ATTR*/
    AR_S32 VoLayer = 0x0;
    SIZE_S stDevSize;
    SIZE_S stFrameSize = {1920, 1080};
    stDevSize.u32Width = stFrameSize.u32Width;
    stDevSize.u32Height = stFrameSize.u32Height;
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {{0}};
    SAMPLE_VO_GetUserLayerAttr(&stLayerAttr, &stDevSize);
    stLayerAttr.u32DispFrmRt = 30;
    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    /* ENABLE VO LAYER */
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // Gdc Config
    g_stGdcParam[0].stOutBuffer.astChannels[0].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[1].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[2].u32Stride = 4096;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

    g_stGdcParam[0].s32CoreId = 0;
    g_stGdcParam[0].s32NonBlock = 1;
    g_stGdcParam[0].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8FrameStart = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8StartMode = 2;   // shadow mode
    g_stGdcParam[0].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshMode = 3;
    g_stGdcParam[0].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 1;       // 2; //disable

    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[0] = 0xbf800000;
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[1] = 0;
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[2] = 0x44f00000;
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[3] = 0;
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[4] = 0x3f800000;
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[5] = 0;
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[6] = 0;
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[7] = 0;
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32ApbMatrix[8] = 0x3f800000;

    g_stGdcParam[0].u8LdEn = 1;
    g_stGdcParam[0].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
    g_stGdcParam[0].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

    int fisrt_start = 1;
    VIDEO_FRAME_INFO_S FrameInfo;
    while (1)
    {
        if (fisrt_start)
        {
            fisrt_start = 0;
            s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 5000);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
                continue;
            }
            g_stGdcParam[0].stInBuffer.u32IsVB = 1;
            g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[0].stOutBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stOutBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
            ;
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }

            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
        }
        sleep(1);
    }

    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }

    pfn_AR_MPI_ISP_Exit(ViPipe);

    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    SAMPLE_COMM_SYS_Exit();

    return s32Ret;
}

#define TEST4_DDR_LD 0
static SIZE_S *pstRshapeSize = NULL;
static SIZE_S *pstMeshSize = NULL;
static GDC_MESH_S *pstMeshData = NULL;
AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST4(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    static int cn_resize = 0;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    VIDEO_FRAME_INFO_S FrameInfo = {0};

    if (sub_info->u32IrqType & IRQ_TYPE_VSYNC)
    {
        SAMPLE_GDC_DP_RESHAPE_Local(ViPipe, ViChn, pstRshapeSize, cn_resize++ % (RESHAPE_SIZE_NUM - 1));
    }

    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {
        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            AR_S32 s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }

            int z = 0;
            for (z = 0; z < MESH_SIZE_NUM; z++)
            {
                if ((FrameInfo.stVFrame.u32Width == pstMeshData[z].u32Width) && (FrameInfo.stVFrame.u32Height == pstMeshData[z].u32Height))
                {
                    // AR_LOG_RAW("[%d]%d %d %d %x\r\n",dev_id,MeshData[z].u32Width,MeshData[z].u32Height, z, MeshData[z].PaAddr);
                    break;
                }
            }

            g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(pstMeshData[z].PaAddr & 0xffffffff);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }

            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
        }
    }
    return 0;
}

static AR_S32 sample_gdc_vb_malloc(VB_POOL Pool, AR_U32 u32W, AR_U32 u32H, AR_U32 u32S, VIDEO_FRAME_INFO_S *pstPing, VB_BLK *pBlk, VB_POOL *pPoolId)
{
    AR_U32 Ups = 1;
    AR_U32 u32Stride = u32S; // pVFrameInfo->stVFrame.u32Stride[0]*Ups;
    AR_U32 u32Width = u32W * Ups;
    AR_U32 u32Height = u32H * Ups;
    AR_U32 u32Size = u32Stride * u32Height * 3 / 2;

    // printf("vo %d %d %d \r\n",u32Stride,u32Width,u32Height);
    VB_BLK blkPing = 0;
    VB_POOL poolIdPing = -1;
    AR_S32 s32Ret = 0;
    AR_VOID *pVirtAddr = NULL;

    // printf("vo %d %d %d \r\n",u32Stride,u32Width,u32Height);
    blkPing = pfn_AR_MPI_VB_GetBlock(Pool, u32Size, NULL);
    if (blkPing == VB_INVALID_HANDLE)
    {
        // printf("Get ping block failed\n");
        return -1;
    }

    poolIdPing = pfn_AR_MPI_VB_Handle2PoolId(blkPing);
    if (poolIdPing < 0)
    {
        // printf("Get ping pool id failed\n");
        pfn_AR_MPI_VB_ReleaseBlock(blkPing);
        return -1;
    }
    s32Ret = pfn_AR_MPI_VB_MmapPool(poolIdPing);
    if (s32Ret)
    {
        // printf("Mmap ping pool failed\n");
        pfn_AR_MPI_VB_ReleaseBlock(blkPing);
        return -1;
    }

    AR_U64 phyAddr = pfn_AR_MPI_VB_Handle2PhysAddr(blkPing);
    // pVirtAddr = pfn_ar_hal_sys_mmap_cache(phyAddr, u32Size);
    s32Ret = pfn_AR_MPI_VB_GetBlockVirAddr(poolIdPing, phyAddr, &pVirtAddr);
    if (s32Ret)
    {
        // printf("Get ping virtual address failed\n");
        pfn_AR_MPI_VB_MunmapPool(poolIdPing);
        pfn_AR_MPI_VB_ReleaseBlock(blkPing);
        return -1;
    }

    memset(pVirtAddr, 128, u32Size);
    pstPing->stVFrame.u32Width = u32Width;
    pstPing->stVFrame.u32Height = u32Height;
    pstPing->stVFrame.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    pstPing->stVFrame.u32Stride[0] = u32Stride;
    pstPing->stVFrame.u32Stride[1] = 1024;
    pstPing->stVFrame.u32Stride[2] = 1024;
    pstPing->stVFrame.u64PhyAddr[0] = phyAddr;
    pstPing->stVFrame.u64VirAddr[0] = (AR_UINTPTR)pVirtAddr;
    pstPing->stVFrame.u64PhyAddr[1] = pstPing->stVFrame.u64PhyAddr[0] + pstPing->stVFrame.u32Stride[0] * u32Height;
    pstPing->stVFrame.u64PhyAddr[2] = pstPing->stVFrame.u64PhyAddr[1] + pstPing->stVFrame.u32Stride[1] * u32Height / 4;
    pstPing->stVFrame.u64VirAddr[1] = pstPing->stVFrame.u64VirAddr[0] + pstPing->stVFrame.u32Stride[0] * u32Height;
    pstPing->stVFrame.u64VirAddr[2] = pstPing->stVFrame.u64VirAddr[1] + pstPing->stVFrame.u32Stride[1] * u32Height / 4;

    *pBlk = blkPing;
    *pPoolId = poolIdPing;

    return 0;
}

AR_S32 test4_dp_gdc_de_shadow_ld_mesh_resize(AR_VOID)
{
    AR_S32 s32Ret;
    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;

    AR_CHAR *sensor_name = "dp_colorbar";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    int sensor_mode = 0x80;
    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

#if TEST4_DDR_LD
    int u32Size = 1920 * 1080 * 2;
    VB_POOL_CONFIG_S stVbPoolCfg = {0};
    stVbPoolCfg.u64BlkSize = u32Size;
    stVbPoolCfg.u32BlkCnt = 4;
    stVbPoolCfg.enRemapMode = VB_REMAP_MODE_NONE;
    VB_POOL Pool = pfn_AR_MPI_VB_CreatePool(&stVbPoolCfg);
    if (Pool == VB_INVALID_POOLID)
    {
        printf("Maybe you not call sys init\n");
        return AR_NULL;
    }

    VB_POOL poolIdPing = -1;
    VB_BLK blkPing = 0;
#endif

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(0, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    // init display
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST4;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_GDC0;
    stStartAttr.stAutoAttr.u32InitLineCnt = 1;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
#if TEST4_DDR_LD
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_DDR;
#endif
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;
#if TEST4_DDR_LD
    stLayerAttr.u32Stride[0] = AR_ALIGN128(stDevSize.u32Width); // ddr
    stLayerAttr.u32Stride[1] = AR_ALIGN128(stDevSize.u32Width / 2);
    stLayerAttr.u32Stride[2] = AR_ALIGN128(stDevSize.u32Width / 2);
#endif
    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

#if TEST4_DDR_LD
    VIDEO_FRAME_INFO_S VPingFrame = {0};
    s32Ret = sample_gdc_vb_malloc(Pool, 1920, 1080, 1920, &VPingFrame, &blkPing, &poolIdPing);
    if (s32Ret < 0)
    {
        printf("sample_gdc_vb_malloc error \r\n");
        return;
    }
#endif

#if TEST4_DDR_LD
    VO_CHN_ATTR_S astChnAttr = {0};
    /* SET AND ENABLE VO CHN */
    AR_U32 u32Height = stDevSize.u32Height / VoChnNum;
    AR_U32 u32Width = stDevSize.u32Width / VoChnNum;
    astChnAttr.bDeflicker = AR_FALSE;
    astChnAttr.u32Priority = 0;
    astChnAttr.stRect.s32X = 0;
    astChnAttr.stRect.s32Y = 0;
    astChnAttr.stRect.u32Height = u32Height;
    astChnAttr.stRect.u32Width = u32Width;
    CHECK_RET(pfn_AR_MPI_VO_SetChnAttr(VoLayer, 0, &astChnAttr), "AR_MPI_VO_SetChnAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableChn(VoLayer, 0), "AR_MPI_VO_EnableChn");

    pfn_AR_MPI_VO_SendFrame(0, 0, &VPingFrame, 0);
#endif

    // Gdc Config
    g_stGdcParam[0].stOutBuffer.u32Width = stDevSize.u32Width;
    g_stGdcParam[0].stOutBuffer.u32Height = stDevSize.u32Height;
    g_stGdcParam[0].stOutBuffer.astChannels[0].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[1].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[2].u32Stride = 4096;
#if TEST4_DDR_LD
    g_stGdcParam[0].stOutBuffer.astChannels[0].u32Stride = AR_ALIGN128(stDevSize.u32Width);
    g_stGdcParam[0].stOutBuffer.astChannels[1].u32Stride = AR_ALIGN128(stDevSize.u32Width / 2);
    g_stGdcParam[0].stOutBuffer.astChannels[2].u32Stride = AR_ALIGN128(stDevSize.u32Width / 2);
    g_stGdcParam[0].stOutBuffer.astChannels[0].uptrAddrVirt = VPingFrame.stVFrame.u64VirAddr[0];
    g_stGdcParam[0].stOutBuffer.astChannels[0].u32AddrPhy = VPingFrame.stVFrame.u64PhyAddr[0];
    g_stGdcParam[0].stOutBuffer.astChannels[1].uptrAddrVirt = VPingFrame.stVFrame.u64VirAddr[1];
    g_stGdcParam[0].stOutBuffer.astChannels[1].u32AddrPhy = VPingFrame.stVFrame.u64PhyAddr[1];
    g_stGdcParam[0].stOutBuffer.astChannels[2].uptrAddrVirt = VPingFrame.stVFrame.u64VirAddr[2];
    g_stGdcParam[0].stOutBuffer.astChannels[2].u32AddrPhy = VPingFrame.stVFrame.u64PhyAddr[2];
#endif

    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

    g_stGdcParam[0].s32CoreId = 0;
    g_stGdcParam[0].s32NonBlock = 1;
    g_stGdcParam[0].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8FrameStart = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8StartMode = 2;   // shadow mode
    g_stGdcParam[0].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshMode = 0;
    g_stGdcParam[0].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 2;       // disable

    g_stGdcParam[0].u8LdEn = 1;
    g_stGdcParam[0].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
    g_stGdcParam[0].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
#if TEST4_DDR_LD
    g_stGdcParam[0].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_DDR;
#endif
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

    AR_U32 MeshSizeOrg = 32 * 1024;
    AR_U64 MeshPaOrg = {0};
    void *MeshVaOrg = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&MeshPaOrg, &MeshVaOrg, "Mesh", NULL, MeshSizeOrg);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)MeshVaOrg, 0, MeshSizeOrg);
    SAMPLE_GDC_Load_Params_General_Local(MeshVaOrg, "/mnt/dptest/mesh1080p_b32f32_hm.dat");
    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(MeshPaOrg & 0xffffffff);
    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride

    pstMeshSize = (SIZE_S *)malloc(sizeof(stMeshSize));
    if (pstMeshSize == NULL)
    {
        assert(0);
    }
    memcpy(pstMeshSize, stMeshSize, sizeof(stMeshSize));

    pstMeshData = (GDC_MESH_S *)malloc(sizeof(GDC_MESH_S) * MESH_SIZE_NUM);
    if (pstMeshData == NULL)
    {
        assert(0);
    }
    SAMPLE_GDC_LOAD_MESH_DATA_Local(pstMeshSize, pstMeshData);

    pstRshapeSize = (SIZE_S *)malloc(sizeof(stRshapeSize));
    if (pstRshapeSize == NULL)
    {
        assert(0);
    }
    memcpy(pstRshapeSize, stRshapeSize, sizeof(stRshapeSize));

    int fisrt_start = 1;
    VIDEO_FRAME_INFO_S FrameInfo = {0};
    int ref = 0;
    while (1)
    {
        if (fisrt_start)
        {
            fisrt_start = 0;
            s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
                continue;
            }

            SAMPLE_GDC_DP_RESHAPE_Local(ViPipe, ViChn, pstRshapeSize, ref++ % (RESHAPE_SIZE_NUM - 1));

            g_stGdcParam[0].stInBuffer.u32IsVB = 1;
            g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
            ;
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }

            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
        }
        sleep(1);
    }

#if TEST4_DDR_LD
    if (blkPing && poolIdPing != -1)
    {
        pfn_AR_MPI_VB_MunmapPool(poolIdPing);
        pfn_AR_MPI_VB_ReleaseBlock(blkPing);
    }
    pfn_AR_MPI_VB_DestroyPool(Pool);
#endif

    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }

    pfn_AR_MPI_ISP_Exit(ViPipe);

    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    SAMPLE_COMM_SYS_Exit();

    free(pstRshapeSize);
    free(pstMeshSize);
    free(pstMeshData);
    return s32Ret;
}

AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST5(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    AR_S32 s32Ret = 0;
    static int frm_cnt = 0;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    static VIDEO_FRAME_INFO_S FrameInfo = {0};

    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {
        // AR_LOG_RAW("dev_id:%d irq_type:%02x Block:%lu \r\n", dev_id,
        //                        sub_info->u32IrqType,
        //                        sub_info->stExtpara.stIntervalLinePara.u32CurBlockID);
        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }

            g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[0].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][frm_cnt++ % (WARP_FILE_NUM - 1)] & 0xffffffff);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }

            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
        }
    }

    return 0;
}

AR_S32 test5_dp_gdc_de_shadow_ld_warp_process(AR_VOID)
{
    AR_S32 s32Ret;
    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_colorbar";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(0, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    // init display
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST5;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);

    /** gdc lines_64_enable=1, start vo line_cnts < 64;
     *	gdc lines_64_enable=0, start vo line_cnts < 32;
     */
    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_GDC0;
    stStartAttr.stAutoAttr.u32InitLineCnt = 1;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // Gdc Config
    g_stGdcParam[0].stOutBuffer.astChannels[0].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[1].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[2].u32Stride = 4096;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

    g_stGdcParam[0].s32CoreId = 0;
    g_stGdcParam[0].s32NonBlock = 1;
    g_stGdcParam[0].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8FrameStart = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8StartMode = 2;   // shadow mode
    g_stGdcParam[0].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshMode = 0;
    g_stGdcParam[0].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

    g_stGdcParam[0].u8LdEn = 1;
    g_stGdcParam[0].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
    g_stGdcParam[0].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "/mnt/dptest/mesh1080p_b32f32_hm.dat");

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride

    GDC_Load_Warp_Data_Array(pu64WarpParaPa[0], ppvWarpParaVa[0], WARP_FILE_NUM, s8WarpFiles);
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][0] & 0xffffffff);

    int fisrt_start = 1;
    VIDEO_FRAME_INFO_S FrameInfo = {0};
    while (1)
    {
        if (fisrt_start)
        {
            fisrt_start = 0;
            s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 5000);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
                continue;
            }

            g_stGdcParam[0].stInBuffer.u32IsVB = 1;
            g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[0].stOutBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stOutBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
            ;
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
            g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }

            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
        }
        sleep(1);
    }

    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }

    pfn_AR_MPI_ISP_Exit(ViPipe);

    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    SAMPLE_COMM_SYS_Exit();

    return s32Ret;
}

static VIDEO_FRAME_INFO_S s_FrameInfo = {0};
static bool vi_first_frame_got = false;
static const uint32_t VI_FPS_SAMPLE_SIZE = 100;
static CallCounter s_VI_GetChnFrame_counter("VI", VI_FPS_SAMPLE_SIZE);
static ar_queue_id_t g_sample_gdc_bf_queue[GDC_RUN_NUM];
AR_VOID *sample_pthread_gdc_get_stream(AR_VOID *pData)
{
    AR_S32 s32Ret = 0;
    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;

    VIDEO_FRAME_INFO_S FrameInfo = {0};
    VIDEO_FRAME_INFO_S FrameInfoAbd = {0};

    while (1)
    {
        s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &s_FrameInfo, 3000);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
            continue;
        }
        s_VI_GetChnFrame_counter.Update();
        vi_first_frame_got = true;
        break;

        for (int i = 0; i < GDC_RUN_NUM; i++)
        {
            s32Ret = pfn_ar_queue_push_force(g_sample_gdc_bf_queue[i], (void *)&FrameInfo, (void *)&FrameInfoAbd);
            if (s32Ret == AR_ERROR_QUEUE_FULL)
            {
                s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfoAbd);
            }
            else if (s32Ret == AR_ERROR)
            {
                s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }
        }
    }

    printf("exit sample_pthread_gdc_get_stream \r\n");

    return NULL;
}

static int warp_file_id[2] = {0};
static int step[2] = {1, 1};

static const int32_t BLOCK_ID_OFFSET = 4;    // should not notice misc effects when cpu back_pressure is not enabled
static const int32_t CALLBACK_BLOCK_CNT = 4; // blocks per callback
static const int32_t TOTAL_BLOCK_CNT = 34;
static const int32_t BLOCK_LINE_CNT = 32;
#define TATAL_CALLBACKS ((TOTAL_BLOCK_CNT - 1) / CALLBACK_BLOCK_CNT)                       // 34th block won't generate any callbacks
#define SPECIAL_CALLBACK_ID ((TOTAL_BLOCK_CNT - BLOCK_ID_OFFSET) / CALLBACK_BLOCK_CNT - 1) // less then CALLBACK_BLOCK_CNT blocks left
#define LAST_CALLBACK_ID (TATAL_CALLBACKS - 1)
#define SECOND_LAST_CALLBACK_ID (LAST_CALLBACK_ID - 1)
#define BLOCK_LEFT_ON_SPECIAL_INTERRUPT (TOTAL_BLOCK_CNT + 1 - (1 + (SPECIAL_CALLBACK_ID + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET))

static const uint32_t DPU_FPS_SAMPLE_SIZE = 100;
static const uint32_t DPU_ID_COUNT = 2;
static CallCounter s_test6_vo_callback_counter[DPU_ID_COUNT]{
    CallCounter("dpu0", DPU_FPS_SAMPLE_SIZE),
    CallCounter("dpu1", DPU_FPS_SAMPLE_SIZE)};
static uint32_t s_vo_frame_count = 0;

static bool s_de_frame_underflow_flag[2] {0};
static int fd_dptest_reg_base;
static unsigned long g_va_base_reg_base;
static unsigned long g_va_de_reg_base[2];
static unsigned long g_va_gdc_reg_base[2];

#define AR_DPTEST_SET_REG_BITS(addr, val) (*(volatile unsigned int *)(addr)) = (val)
#define AR_DPTEST_GET_REG_BITS(addr) (*(volatile unsigned int *)(addr))

// map 0x08800000-08900000  len:0x100000
static void ar_dptest_system_register_map()
{
    fd_dptest_reg_base = open("/dev/mem", O_RDWR | O_SYNC);
    g_va_base_reg_base = (unsigned long)mmap(NULL, 0x100000, PROT_READ | PROT_WRITE, MAP_SHARED, fd_dptest_reg_base, 0x08800000);

    g_va_de_reg_base[0] = g_va_base_reg_base + 0x20000;
    g_va_de_reg_base[1] = g_va_base_reg_base + 0x40000;
    g_va_gdc_reg_base[0] = g_va_base_reg_base + 0x30000;
    g_va_gdc_reg_base[1] = g_va_base_reg_base + 0x50000;
}

static void ar_dptest_system_register_unmap()
{
    munmap((unsigned char *)g_va_base_reg_base, 0x100000);
    close(fd_dptest_reg_base);
}

AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST6(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    AR_S32 s32Ret = 0;
    static AR_S32 frm_cnt[2] = {0};

    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    // VIDEO_FRAME_INFO_S FrameInfo = {0};
    uint32_t interrupt_id = sub_info->stExtpara.stIntervalLinePara.u32CurBlockID;

    if (sub_info->u32IrqType & IRQ_TYPE_VSYNC)
    {
        s_de_frame_underflow_flag[dev_id] = false;
        if (dev_id == 0)
            s_vo_frame_count++;
        s_test6_vo_callback_counter[dev_id].Update();
        uint32_t target_block_id = 1 + BLOCK_ID_OFFSET;
        memcpy(ppvWarpParaVa[dev_id][0] + target_block_id * 9 * 4,
               ppvWarpParaVa[dev_id][warp_file_id[dev_id]] + target_block_id * 9 * 4,
               CALLBACK_BLOCK_CNT * 9 * 4);
        AR_S32 s32LineNum = (CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET) * BLOCK_LINE_CNT;
        AR_GDC_ADV_LINE_PARAMS_S line_params = {dev_id, s32LineNum};
        s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&line_params);
        if (s32Ret < 0)
        {
            AR_LOG_RAW("AR_MPI_GDC_ADV_Config_CPUBp_Line fail\r\n");
        }
        if (s_vo_frame_count % 1000 == 0 && dev_id == 0)
        {
            BOARD_LOG_INFO("GDC on vsync bp to line{}", s32LineNum);
        }
        if (AR_DPTEST_GET_REG_BITS(g_va_de_reg_base[dev_id] + 0x1518) & 0x20)
        {
            s_de_frame_underflow_flag[dev_id] = true;
            BOARD_LOG_WARN("GDC{} vsync callback underflow", dev_id);
        }
    }

    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {
        AR_S32 s32LineNum = 0;
        if (interrupt_id < SPECIAL_CALLBACK_ID)
        {
            uint32_t target_block_id = 1 + (interrupt_id + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET;
            memcpy(ppvWarpParaVa[dev_id][0] + target_block_id * 9 * 4,
                   ppvWarpParaVa[dev_id][warp_file_id[dev_id]] + target_block_id * 9 * 4,
                   CALLBACK_BLOCK_CNT * 9 * 4);
            s32LineNum =
                ((interrupt_id + 2) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET) * BLOCK_LINE_CNT;
            AR_GDC_ADV_LINE_PARAMS_S line_params = {dev_id, s32LineNum};
            s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&line_params);
            if (s32Ret < 0)
            {
                AR_LOG_RAW("AR_MPI_GDC_ADV_Config_CPUBp_Line fail\r\n");
            }
            if (s_vo_frame_count % 1000 == 0 && dev_id == 0)
            {
                BOARD_LOG_INFO("GDC on callback{}({}) bp to line{}", interrupt_id, (interrupt_id + 1) * CALLBACK_BLOCK_CNT * BLOCK_LINE_CNT, s32LineNum);
            }
        }
        if (interrupt_id == SPECIAL_CALLBACK_ID)
        {
            uint32_t target_block_id = 1 + (interrupt_id + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET;
            memcpy(ppvWarpParaVa[dev_id][0] + target_block_id * 9 * 4,
                   ppvWarpParaVa[dev_id][warp_file_id[dev_id]] + target_block_id * 9 * 4,
                   BLOCK_LEFT_ON_SPECIAL_INTERRUPT * 9 * 4);
            s32LineNum = TOTAL_BLOCK_CNT * BLOCK_LINE_CNT; // line 1088
            AR_GDC_ADV_LINE_PARAMS_S line_params = {dev_id, s32LineNum};
            s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&line_params);
            if (s32Ret < 0)
            {
                AR_LOG_RAW("AR_MPI_GDC_ADV_Config_CPUBp_Line fail\r\n");
            }
            if (s_vo_frame_count % 1000 == 0 && dev_id == 0)
            {
                BOARD_LOG_INFO("GDC on callback{}({}) bp to line{}", interrupt_id, (interrupt_id + 1) * CALLBACK_BLOCK_CNT * BLOCK_LINE_CNT, s32LineNum);
            }
            // update wrap file id
            if (warp_file_id[dev_id] == 499)
                step[dev_id] = -1;
            if (warp_file_id[dev_id] == 0)
                step[dev_id] = 1;

            // set warp matrix from the begining.(For usage of next frame)
            warp_file_id[dev_id] += step[dev_id];
            memcpy(ppvWarpParaVa[dev_id][0],
                   ppvWarpParaVa[dev_id][warp_file_id[dev_id]],
                   (BLOCK_ID_OFFSET + 1) * 9 * 4);
        }
        if (interrupt_id > SPECIAL_CALLBACK_ID)
        {
        }

        if (vi_first_frame_got && sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            // s32Ret = pfn_ar_queue_pop_timeout(g_sample_gdc_bf_queue[dev_id], &FrameInfo, NULL, -1);
            // if (s32Ret < 0)
            //{
            //     assert(0);
            // }

            g_stGdcParam[dev_id].stInBuffer.u32IsVB = 1;
            g_stGdcParam[dev_id].stInBuffer.u32Width = s_FrameInfo.stVFrame.u32Width;
            g_stGdcParam[dev_id].stInBuffer.u32Height = s_FrameInfo.stVFrame.u32Height;
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].u32Stride = s_FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].u32Stride = s_FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].u32Stride = s_FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].uptrAddrVirt = s_FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].u32AddrPhy = s_FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].uptrAddrVirt = s_FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].u32AddrPhy = s_FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].uptrAddrVirt = s_FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].u32AddrPhy = s_FrameInfo.stVFrame.u64PhyAddr[2];

            // g_stGdcParam[dev_id].stGdcParam.stWarpCfg.u32WarpAddr= (AR_U32)(pu64WarpParaPa[0][frm_cnt[dev_id]++%(WARP_FILE_NUM-1)] & 0xffffffff);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[dev_id]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &s_FrameInfo);
                assert(0);
            }

            if (dev_id == 0) // or dev_id==1  only can release once!!!
            {
                s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &s_FrameInfo);
                if (AR_SUCCESS != s32Ret)
                {
                    SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                    return -1;
                }
            }
        }
        if (!s_de_frame_underflow_flag[dev_id] && AR_DPTEST_GET_REG_BITS(g_va_de_reg_base[dev_id] + 0x1518) & 0x20)
        {
            s_de_frame_underflow_flag[dev_id] = true;
            BOARD_LOG_WARN("GDC{} on callback{}({}) callback underflow", dev_id, interrupt_id, (interrupt_id + 1) * CALLBACK_BLOCK_CNT * BLOCK_LINE_CNT);
        }
    }
    if (sub_info->u32IrqType & IRQ_TYPE_FRAME_DONE)
    {
        if (!s_de_frame_underflow_flag[dev_id] && AR_DPTEST_GET_REG_BITS(g_va_de_reg_base[dev_id] + 0x1518) & 0x20)
        {
            BOARD_LOG_WARN("GDC{} frame_done callback underflow", dev_id);
        }
        // AR_S32 s32LineNum =
        //     4 * BLOCK_LINE_CNT;
        // AR_GDC_ADV_LINE_PARAMS_S line_params = {dev_id, s32LineNum};
        // s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&line_params);
        // if (s32Ret < 0)
        //{
        //     AR_LOG_RAW("AR_MPI_GDC_ADV_Config_CPUBp_Line fail\r\n");
        // }
    }
    return 0;
}

AR_S32 test6_dual_dp_gdc_de_shadow_ld_warp_process(AR_VOID)
{
    AR_S32 s32Ret;
    ISP_SNS_OBJ_S *p_obj = NULL;

    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_rx";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(1, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    default_attr.stPubAttr.stTiming.hblank = 280;
    default_attr.stPubAttr.stTiming.vblank = 45;

    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    // pfn_ar_hal_dp_rx_set_edid(&edid_groups[1]); // 1080p60
    // unsigned int hpd_status = 0;
    // if (pfn_ar_hal_dp_rx_get_hpd_status(&hpd_status) < 0)
    //{
    //     printf("get hpd_status failed!\n");
    //     return -1;
    // }
    // if (hpd_status)
    //{
    //     int ret = pfn_ar_hal_dp_rx_set_hpd_status(0);
    //     if (ret)
    //         printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);

    //    ret = pfn_ar_hal_dp_rx_set_hpd_status(1);
    //    if (ret)
    //        printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);
    //}

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    // de0 config=============================
    // init display
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST6;

    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfSync = VO_OUTPUT_USER;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;
    stPubAttr.stSyncInfo.bIop = AR_TRUE;
    stPubAttr.stSyncInfo.u8Intfb = 0;
    stPubAttr.stSyncInfo.u16Hpw = 60;
    stPubAttr.stSyncInfo.u16Hbb = 200;
    stPubAttr.stSyncInfo.u16Hact = 1920;
    stPubAttr.stSyncInfo.u16Hfb = 120;
    stPubAttr.stSyncInfo.u16Vpw = 2;
    stPubAttr.stSyncInfo.u16Vbb = 14;
    stPubAttr.stSyncInfo.u16Vact = 1080;
    stPubAttr.stSyncInfo.u16Vfb = 16;
    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;
    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;
    stPubAttr.enIntfType = VO_INTF_MIPI;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_GDC0;
    /** gdc lines_64_enable=1, start vo line_cnts < 64;
     *	gdc lines_64_enable=0, start vo line_cnts < 32;
     */
    stStartAttr.stAutoAttr.u32InitLineCnt = 1;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);
    /* SET MIPI TIMING & ENABLE MIPI DSI */
    // s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_60Hz);
    s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_90Hz);
    s32Ret |= pfn_AR_MPI_VO_Dsi_Enable(VoDev);
    if (s32Ret)
    {
        SAMPLE_PRT("VO_0 set dsi failed with %#x!\n", s32Ret);
        return s32Ret;
    }

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // de1 config=================
    // init display
    VoDev = 1;
    VoLayer = VO_LAYER_ID_VIDEO_1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr1 = {0};
    stIrqAttr1.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr1.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr1), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr1 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr1.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr1.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST6;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr1), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    /* USER SET VO DEV SYNC INFO */
    stPubAttr.enIntfSync = VO_OUTPUT_USER;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;
    stPubAttr.stSyncInfo.bIop = AR_TRUE;
    stPubAttr.stSyncInfo.u8Intfb = 0;
    stPubAttr.stSyncInfo.u16Hpw = 60;
    stPubAttr.stSyncInfo.u16Hbb = 200;
    stPubAttr.stSyncInfo.u16Hact = 1920;
    stPubAttr.stSyncInfo.u16Hfb = 120;
    stPubAttr.stSyncInfo.u16Vpw = 2;
    stPubAttr.stSyncInfo.u16Vbb = 14;
    stPubAttr.stSyncInfo.u16Vact = 1080;
    stPubAttr.stSyncInfo.u16Vfb = 16;
    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;
    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;
    stPubAttr.enIntfType = VO_INTF_MIPI;

    ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);

    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_GDC1;
    /** gdc lines_64_enable=1, start vo line_cnts < 64;
     *	gdc lines_64_enable=0, start vo line_cnts < 32;
     */
    stStartAttr.stAutoAttr.u32InitLineCnt = 1;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);
    /* SET MIPI TIMING & ENABLE MIPI DSI */
    // s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_60Hz);
    s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_90Hz);
    s32Ret |= pfn_AR_MPI_VO_Dsi_Enable(VoDev);
    if (s32Ret)
    {
        SAMPLE_PRT("VO_1 set dsi failed with %#x!\n", s32Ret);
        return s32Ret;
    }

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // Gdc0 Config
    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "mesh_cv.dat");
    GDC_Load_Warp_Data_Array_FakeIdentity(pu64WarpParaPa[0], ppvWarpParaVa[0], WARP_FILE_NUM, s8WarpFiles);
    GDC_Load_Warp_Data_Array_FakeIdentity(pu64WarpParaPa[1], ppvWarpParaVa[1], WARP_FILE_NUM, s8WarpFiles);

    for (int i = 0; i < GDC_RUN_NUM; i++)
    {
        g_stGdcParam[i].stOutBuffer.astChannels[0].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[1].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[2].u32Stride = 4096;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

        g_stGdcParam[i].s32CoreId = i;
        g_stGdcParam[i].s32NonBlock = 1;

        g_stGdcParam[i].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8FrameStart = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8StartMode = 2;   // shadow mode
        g_stGdcParam[i].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

        g_stGdcParam[i].stGdcParam.stLdCfg.stCpuBp.u8CpuBpEn = 1;
        g_stGdcParam[i].stGdcParam.stLdCfg.stCpuBp.u16CpuBpLines = 0; // init value of compute to line

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshMode = 0;
        g_stGdcParam[i].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

        g_stGdcParam[i].u8LdEn = 1;
        g_stGdcParam[i].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
        g_stGdcParam[i].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride
        g_stGdcParam[i].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[i][0] & 0xffffffff);
    }

    g_sample_gdc_bf_queue[0] = pfn_ar_queue_create(4, sizeof(VIDEO_FRAME_INFO_S), NULL);
    g_sample_gdc_bf_queue[1] = pfn_ar_queue_create(4, sizeof(VIDEO_FRAME_INFO_S), NULL);

    pthread_t tid;
    pthread_create(&tid, NULL, sample_pthread_gdc_get_stream, NULL);

    ar_dptest_system_register_map();

    int fisrt_start = 1;
    AR_S32 frm_cnt[2] = {0};
    while (1)
    {
        if (fisrt_start)
        {
            if (!vi_first_frame_got)
            {
                sleep(1);
                continue;
            }
            fisrt_start = 0;
            for (int i = 0; i < GDC_RUN_NUM; i++)
            {
                // ret = pfn_ar_queue_pop_timeout(g_sample_gdc_bf_queue[i], &FrameInfo, NULL, -1);
                // if (ret < 0)
                //{
                //     assert(0);
                // }
                g_stGdcParam[i].stInBuffer.u32IsVB = 1;
                g_stGdcParam[i].stInBuffer.u32Width = s_FrameInfo.stVFrame.u32Width;
                g_stGdcParam[i].stInBuffer.u32Height = s_FrameInfo.stVFrame.u32Height;
                g_stGdcParam[i].stInBuffer.astChannels[0].u32Stride = s_FrameInfo.stVFrame.u32Stride[0];
                g_stGdcParam[i].stInBuffer.astChannels[1].u32Stride = s_FrameInfo.stVFrame.u32Stride[1];
                g_stGdcParam[i].stInBuffer.astChannels[2].u32Stride = s_FrameInfo.stVFrame.u32Stride[2];

                // lb_lowdelay stride must 4096
                g_stGdcParam[i].stOutBuffer.u32Width = s_FrameInfo.stVFrame.u32Width;
                g_stGdcParam[i].stOutBuffer.u32Height = s_FrameInfo.stVFrame.u32Height;
                g_stGdcParam[i].stInBuffer.astChannels[0].uptrAddrVirt = s_FrameInfo.stVFrame.u64VirAddr[0];
                g_stGdcParam[i].stInBuffer.astChannels[0].u32AddrPhy = s_FrameInfo.stVFrame.u64PhyAddr[0];
                g_stGdcParam[i].stInBuffer.astChannels[1].uptrAddrVirt = s_FrameInfo.stVFrame.u64VirAddr[1];
                g_stGdcParam[i].stInBuffer.astChannels[1].u32AddrPhy = s_FrameInfo.stVFrame.u64PhyAddr[1];
                g_stGdcParam[i].stInBuffer.astChannels[2].uptrAddrVirt = s_FrameInfo.stVFrame.u64VirAddr[2];
                g_stGdcParam[i].stInBuffer.astChannels[2].u32AddrPhy = s_FrameInfo.stVFrame.u64PhyAddr[2];

                g_stGdcParam[i].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[i][0] & 0xffffffff);

                g_stGdcParam[i].stLdParam.stOutLowdelay.enFormat = s_FrameInfo.stVFrame.enPixelFormat;
                ;
                g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(s_FrameInfo.stVFrame.u32Width);
                g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(s_FrameInfo.stVFrame.u32Width / 2);
                g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(s_FrameInfo.stVFrame.u32Width / 2);

                s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[i]);
                if (s32Ret < 0)
                {
                    printf("AR_MPI_GDC_ADV_Process fail\r\n");
                    pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &s_FrameInfo);
                    assert(0);
                }
                AR_GDC_ADV_LINE_PARAMS_S line_params = {i, 128};
                s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&line_params);
                if (s32Ret < 0)
                {
                    AR_LOG_RAW("AR_MPI_GDC_ADV_Config_CPUBp_Line fail\r\n");
                    pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &s_FrameInfo);
                    assert(0);
                }
            }

            // s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &s_FrameInfo);
            // if (AR_SUCCESS != s32Ret)
            //{
            //     SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
            //     return -1;
            // }
        }
        sleep(1);
    }

    VoLayer = 0;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoLayer = 0x10;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    VoDev = 0;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoDev = 1;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }

    pfn_AR_MPI_ISP_Exit(ViPipe);
    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    SAMPLE_COMM_SYS_Exit();

    ar_dptest_system_register_unmap();
    return s32Ret;
}

AR_S32 test61_dual_dp_gdc_de_dp_trigger_gdc(AR_VOID)
{
    AR_S32 s32Ret;
    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_rx";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(1, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    default_attr.stPubAttr.stTiming.hblank = 280;
    default_attr.stPubAttr.stTiming.vblank = 45;

    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    // pfn_AR_MPI_ISP_Init(ViPipe);
    // pfn_AR_MPI_ISP_Run(ViPipe);

    // de0 config=============================
    // init display
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST6;

    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfSync = VO_OUTPUT_USER;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;
    stPubAttr.stSyncInfo.bIop = AR_TRUE;
    stPubAttr.stSyncInfo.u8Intfb = 0;
    stPubAttr.stSyncInfo.u16Hpw = 60;
    stPubAttr.stSyncInfo.u16Hbb = 200;
    stPubAttr.stSyncInfo.u16Hact = 1920;
    stPubAttr.stSyncInfo.u16Hfb = 120;
    stPubAttr.stSyncInfo.u16Vpw = 2;
    stPubAttr.stSyncInfo.u16Vbb = 14;
    stPubAttr.stSyncInfo.u16Vact = 1080;
    stPubAttr.stSyncInfo.u16Vfb = 16;
    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;
    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;
    stPubAttr.enIntfType = VO_INTF_MIPI;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    VO_USER_INTFSYNC_INFO_S stUserInfo = {0};
    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /* SET MIPI TIMING & ENABLE MIPI DSI */
    // s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_60Hz);
    s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_90Hz);
    s32Ret |= pfn_AR_MPI_VO_Dsi_Enable(VoDev);
    if (s32Ret)
    {
        BOARD_LOG_ERROR("VO_0 set dsi failed with {:x}!", s32Ret);
        return s32Ret;
    }

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // de1 config=================
    // init display
    VoDev = 1;
    VoLayer = VO_LAYER_ID_VIDEO_1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr1 = {0};
    stIrqAttr1.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr1.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr1), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr1 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr1.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr1.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST6;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr1), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    /* USER SET VO DEV SYNC INFO */
    stPubAttr.enIntfSync = VO_OUTPUT_USER;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;
    stPubAttr.stSyncInfo.bIop = AR_TRUE;
    stPubAttr.stSyncInfo.u8Intfb = 0;
    stPubAttr.stSyncInfo.u16Hpw = 60;
    stPubAttr.stSyncInfo.u16Hbb = 200;
    stPubAttr.stSyncInfo.u16Hact = 1920;
    stPubAttr.stSyncInfo.u16Hfb = 120;
    stPubAttr.stSyncInfo.u16Vpw = 2;
    stPubAttr.stSyncInfo.u16Vbb = 14;
    stPubAttr.stSyncInfo.u16Vact = 1080;
    stPubAttr.stSyncInfo.u16Vfb = 16;
    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;
    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;
    stPubAttr.enIntfType = VO_INTF_MIPI;

    ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);

    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);
    /* SET MIPI TIMING & ENABLE MIPI DSI */
    // s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_60Hz);
    s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_90Hz);
    s32Ret |= pfn_AR_MPI_VO_Dsi_Enable(VoDev);
    if (s32Ret)
    {
        BOARD_LOG_ERROR("VO_1 set dsi failed with {:x}!", s32Ret);
        return s32Ret;
    }

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // Gdc0/1 Config
    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "mesh_cv.dat");
    GDC_Load_Warp_Data_Array_FakeIdentity(pu64WarpParaPa[0], ppvWarpParaVa[0], WARP_FILE_NUM, s8WarpFiles);
    GDC_Load_Warp_Data_Array_FakeIdentity(pu64WarpParaPa[1], ppvWarpParaVa[1], WARP_FILE_NUM, s8WarpFiles);

    for (int i = 0; i < GDC_RUN_NUM; i++)
    {
        g_stGdcParam[i].stOutBuffer.astChannels[0].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[1].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[2].u32Stride = 4096;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

        g_stGdcParam[i].s32CoreId = i;
        g_stGdcParam[i].s32NonBlock = 1;

        g_stGdcParam[i].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8FrameStart = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8StartMode = 2;   // shadow mode
        g_stGdcParam[i].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

        g_stGdcParam[i].stGdcParam.stLdCfg.stCpuBp.u8CpuBpEn = 1;
        g_stGdcParam[i].stGdcParam.stLdCfg.stCpuBp.u16CpuBpLines = 0; // init value of compute to line

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshMode = 0;
        g_stGdcParam[i].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

        g_stGdcParam[i].u8LdEn = 1;
        g_stGdcParam[i].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
        g_stGdcParam[i].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride
        g_stGdcParam[i].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[i][0] & 0xffffffff);
    }

    pthread_t tid;
    pthread_create(&tid, NULL, sample_pthread_gdc_get_stream, NULL);

    int fisrt_start = 1;

    // Gdc Config
    AR_U32 PreImgSize = 1920 * 1080 * 2;
    AR_U64 PreImgPa = {0};
    void *PreImgVa = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&PreImgPa, &PreImgVa, "PreImg", NULL, PreImgSize);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)PreImgVa, 0, PreImgSize);
    for (int i = 0; i < GDC_RUN_NUM; i++)
    {
        // gdc start, will hang at line32
        g_stGdcParam[i].stInBuffer.u32Width = 1920;
        g_stGdcParam[i].stInBuffer.u32Height = 1080;
        g_stGdcParam[i].stInBuffer.astChannels[0].u32Stride = 2048;
        g_stGdcParam[i].stInBuffer.astChannels[1].u32Stride = 1024;
        g_stGdcParam[i].stInBuffer.astChannels[2].u32Stride = 1024;
        // lb_lowdelay stride must 4096
        g_stGdcParam[i].stOutBuffer.u32Width = 1920;
        g_stGdcParam[i].stOutBuffer.u32Height = 1080;
        g_stGdcParam[i].stInBuffer.astChannels[0].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[0].u32AddrPhy = PreImgPa;
        g_stGdcParam[i].stInBuffer.astChannels[1].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[1].u32AddrPhy = PreImgPa;
        g_stGdcParam[i].stInBuffer.astChannels[2].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[2].u32AddrPhy = PreImgPa;

        g_stGdcParam[i].stLdParam.stOutLowdelay.enFormat = PIXEL_FORMAT_YVU_PLANAR_420;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(1920);
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(1920 / 2);
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(1920 / 2);

        s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[i]);
        if (s32Ret < 0)
        {
            printf("AR_MPI_GDC_ADV_Process fail\r\n");
            return -1;
        }
        AR_GDC_ADV_LINE_PARAMS_S line_params = {i, 128};
        s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&line_params);
        if (s32Ret < 0)
        {
            BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Config_CPUBp_Line fail\r\n");
            pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &s_FrameInfo);
            assert(0);
        }
    }

    // DP START
    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    while (1)
    {
        BOARD_LOG_INFO("test61 sleeping");
        sleep(1);
    }

    printf("exit process loop ! \r\n");
    pfn_AR_MPI_VO_SubscribeDisable(0);
    pfn_AR_MPI_VO_SubscribeDisable(1);

    VoLayer = 0;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoLayer = 0x10;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    VoDev = 0;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoDev = 1;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    printf("stop vo done ! \r\n");

    pfn_AR_MPI_ISP_Exit(ViPipe);
    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    VI_DEV_PROP_S Prop = {0};
    pfn_AR_MPI_VIN_CloseDev(&Prop);
    printf("stop vi done ! \r\n");

    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[0].s32CoreId);
    printf("stop gdc[0] done ! \r\n");
    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[1].s32CoreId);
    printf("stop gdc[1] done ! \r\n");

    SAMPLE_COMM_SYS_Exit();

    pfn_ar_hal_sys_mmz_free(Pa, Va);
    pfn_ar_hal_sys_mmz_free(PreImgPa, PreImgVa);

    printf("free mmz done ! \r\n");

    return s32Ret;
}

AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST7(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    return 0;
}

// dp trigger vo start,  DP in normal mode   ( Can acquire DP buffer[n] only when dp frame[n] done)
AR_S32 test7_dp_gdc_de_freerun_ld_warp_process(AR_VOID)
{
    AR_S32 s32Ret;

    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_rx";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(1, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    default_attr.stPubAttr.stTiming.hblank = 280;
    default_attr.stPubAttr.stTiming.vblank = 45;
    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);

    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_ar_hal_dp_rx_set_edid(&edid_groups[1]); // 1080p60
    unsigned int hpd_status = 0;
    if (pfn_ar_hal_dp_rx_get_hpd_status(&hpd_status) < 0)
    {
        printf("get hpd_status failed!\n");
        return -1;
    }
    if (hpd_status)
    {
        int ret = pfn_ar_hal_dp_rx_set_hpd_status(0);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);

        ret = pfn_ar_hal_dp_rx_set_hpd_status(1);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);
    }

    // pfn_AR_MPI_ISP_Init(ViPipe);
    // pfn_AR_MPI_ISP_Run(ViPipe);

    //====================================================================================================
    // cfg VO
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST7;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);

    /** gdc lines_64_enable=1, start vo line_cnts < 64;
     *	gdc lines_64_enable=0, start vo line_cnts < 32;
     */

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    VO_USER_INTFSYNC_INFO_S stUserInfo = {0};
    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    //====================================================================================================
    // Gdc Config
    AR_U32 PreImgSize = 1920 * 1080 * 2;
    AR_U64 PreImgPa = {0};
    void *PreImgVa = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&PreImgPa, &PreImgVa, "PreImg", NULL, PreImgSize);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)PreImgVa, 0, PreImgSize);

    g_stGdcParam[0].stOutBuffer.astChannels[0].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[1].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[2].u32Stride = 4096;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

    g_stGdcParam[0].s32CoreId = 0;
    g_stGdcParam[0].s32NonBlock = 1;
    g_stGdcParam[0].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8FrameStart = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8StartMode = 3;   // free-run mode
    g_stGdcParam[0].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshMode = 0;
    g_stGdcParam[0].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

    g_stGdcParam[0].u8LdEn = 1;
    g_stGdcParam[0].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
    g_stGdcParam[0].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "/mnt/dptest/mesh1080p_b32f32_hm.dat");

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride

    GDC_Load_Warp_Data_Array(pu64WarpParaPa[0], ppvWarpParaVa[0], WARP_FILE_NUM, s8WarpFiles);
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][0] & 0xffffffff);

    // gdc start, will hang at line32
    g_stGdcParam[0].stInBuffer.u32Width = 1920;
    g_stGdcParam[0].stInBuffer.u32Height = 1080;
    g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = 2048;
    g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = 1024;
    g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = 1024;
    // lb_lowdelay stride must 4096
    g_stGdcParam[0].stOutBuffer.u32Width = 1920;
    g_stGdcParam[0].stOutBuffer.u32Height = 1080;
    g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = PreImgPa;
    g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = PreImgPa;
    g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = PreImgPa;

    g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(1920);
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(1920 / 2);
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(1920 / 2);

    s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
    if (s32Ret < 0)
    {
        printf("AR_MPI_GDC_ADV_Process fail\r\n");
        return -1;
    }

    // DP START
    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    AR_U64 frm_cnt = 0;
    VIDEO_FRAME_INFO_S FrameInfo = {0};
    while (1)
    {
        if (quit)
        {
            printf("process exit \r\n");
            break;
        }

        s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
            continue;
        }

        g_stGdcParam[0].stInBuffer.u32IsVB = 1;
        g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
        g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
        g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
        g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
        g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
        // lb_lowdelay stride must 4096
        g_stGdcParam[0].stOutBuffer.u32Width = FrameInfo.stVFrame.u32Width;
        g_stGdcParam[0].stOutBuffer.u32Height = FrameInfo.stVFrame.u32Height;
        g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
        g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
        g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
        g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
        g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
        g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

        g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
        ;
        g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
        g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
        g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);

        g_stGdcParam[0].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][frm_cnt++ % (WARP_FILE_NUM - 1)] & 0xffffffff);
        g_stGdcParam[0].stGdcParam.stStartCfg.u8FrameStart = 0;
        s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
        if (s32Ret < 0)
        {
            printf("AR_MPI_GDC_ADV_Process fail\r\n");
            pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            assert(0);
        }

        s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
            return -1;
        }
    }

    printf("exit process loop ! \r\n");
    pfn_AR_MPI_VO_SubscribeDisable(0);
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    printf("stop vo done ! \r\n");

    pfn_AR_MPI_ISP_Exit(ViPipe);

    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);
    VI_DEV_PROP_S Prop = {0};
    pfn_AR_MPI_VIN_CloseDev(&Prop);
    printf("stop vi done ! \r\n");

    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[0].s32CoreId);
    printf("stop gdc done ! \r\n");

    SAMPLE_COMM_SYS_Exit();

    pfn_ar_hal_sys_mmz_free(Pa, Va);
    pfn_ar_hal_sys_mmz_free(PreImgPa, PreImgVa);

    printf("free buffer done ! \r\n");

    return s32Ret;
}

AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST8(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    AR_S32 s32Ret = 0;
    static int frm_cnt = 0;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    static VIDEO_FRAME_INFO_S FrameInfo = {0};

    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {
        // AR_LOG_RAW("dev_id:%d irq_type:%02x Block:%lu \r\n", dev_id,
        //                        sub_info->u32IrqType,
        //                        sub_info->stExtpara.stIntervalLinePara.u32CurBlockID);
        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
            g_stGdcParam[0].stInBuffer.u32IsVB = 1;
            g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            // AR_LOG_RAW("dp phy %x %x %x \r\n",FrameInfo.stVFrame.u64PhyAddr[0],FrameInfo.stVFrame.u64PhyAddr[1],FrameInfo.stVFrame.u64PhyAddr[2]);
            g_stGdcParam[0].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][frm_cnt++ % (WARP_FILE_NUM - 1)] & 0xffffffff);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
            if (s32Ret < 0)
            {
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }

            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
        }
    }
    return 0;
}

// dp trigger vo start,  DP in soft-lowdelay mode ( Can acquire DP buffer[n] when dp frame[n] output >= 1 line)
AR_S32 test8_dp_gdc_de_shadow_ld_warp_process(AR_VOID)
{
    AR_S32 s32Ret;
    ISP_SNS_OBJ_S *p_obj = NULL;

    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_rx";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 6;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 6;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    // SAMPLE_AR_MPI_VIN_OpenDev_Local(0, isp_fre,vif_fre,isp_fre,pcs_fre);
    SAMPLE_AR_MPI_VIN_OpenDev_Local(1, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);

    // dp output buffer after 1 line, only one buffer
    VI_CHN_EXT_ATTR_S stChnExtAttr = {0};
    stChnExtAttr.enLowDelayMode = VI_CH_LOW_DELAY_DDR;
    stChnExtAttr.stSoftLowdelayAttr.s32SofLowdeayEn = 0;
    pfn_AR_MPI_VI_SetChnExtAttr(ViPipe, ViChn, &stChnExtAttr);

    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    default_attr.stPubAttr.stTiming.hblank = 280;
    default_attr.stPubAttr.stTiming.vblank = 45;
    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_ar_hal_dp_rx_set_edid(&edid_groups[1]); // 1080p60
    unsigned int hpd_status = 0;
    if (pfn_ar_hal_dp_rx_get_hpd_status(&hpd_status) < 0)
    {
        printf("get hpd_status failed!\n");
        return -1;
    }
    if (hpd_status)
    {
        int ret = pfn_ar_hal_dp_rx_set_hpd_status(0);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);

        ret = pfn_ar_hal_dp_rx_set_hpd_status(1);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);
    }

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    AR_FLOAT Dpfps = 0;
    s32Ret = pfn_AR_MPI_VI_GetHightPricisionPipeFPS(ViPipe, ViChn, &Dpfps, 0xB01);
    if (AR_SUCCESS != s32Ret)
    {
        printf("AR_MPI_VI_GetHightPricisionPipeFPS failed with %d!\n", s32Ret);
        return s32Ret;
    }
    printf("=======AR_MPI_VI_GetHightPricisionPipeFPS:Dpfps %f \r\n", Dpfps);
    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);

    //====================================================================================================
    // cfg VO
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST8;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, Dpfps);

    /** gdc lines_64_enable=1, start vo line_cnts < 64;
     *	gdc lines_64_enable=0, start vo line_cnts < 32;
     */

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;

    stLowdelayAttr.stHardwareLowdelayInfo.statsSrc = AR_SYS_HARDWARE_STATS_SRC_DP_ISP;
    stLowdelayAttr.stHardwareLowdelayInfo.u32ThresholdLineCnt = 50;
    stLowdelayAttr.stHardwareLowdelayInfo.floatStepLineCnt = 0.057;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    VO_USER_INTFSYNC_INFO_S stUserInfo = {0};
    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    //====================================================================================================
    // Gdc Config
    AR_U32 PreImgSize = 1920 * 1080 * 2;
    AR_U64 PreImgPa = {0};
    void *PreImgVa = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&PreImgPa, &PreImgVa, "PreImg", NULL, PreImgSize);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)PreImgVa, 0, PreImgSize);

    g_stGdcParam[0].stOutBuffer.astChannels[0].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[1].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[2].u32Stride = 4096;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

    g_stGdcParam[0].s32CoreId = 0;
    g_stGdcParam[0].s32NonBlock = 1;
    g_stGdcParam[0].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8FrameStart = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8StartMode = 2;   // shadow mode
    g_stGdcParam[0].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshMode = 0;
    g_stGdcParam[0].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

    g_stGdcParam[0].u8LdEn = 1;
    g_stGdcParam[0].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
    g_stGdcParam[0].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "/mnt/dptest/mesh1080p_b32f32_hm.dat");

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride

    GDC_Load_Warp_Data_Array(pu64WarpParaPa[0], ppvWarpParaVa[0], WARP_FILE_NUM, s8WarpFiles);
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][0] & 0xffffffff);

    // gdc start
    g_stGdcParam[0].stInBuffer.u32Width = 1920;
    g_stGdcParam[0].stInBuffer.u32Height = 1080;
    g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = 2048;
    g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = 1024;
    g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = 1024;
    // lb_lowdelay stride must 4096
    g_stGdcParam[0].stOutBuffer.u32Width = 1920;
    g_stGdcParam[0].stOutBuffer.u32Height = 1080;
    g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = PreImgPa;
    g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = PreImgPa;
    g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = PreImgPa;

    g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(1920);
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(1920 / 2);
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(1920 / 2);

    s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
    if (s32Ret < 0)
    {
        printf("AR_MPI_GDC_ADV_Process fail\r\n");
        return -1;
    }

    // DP START
    // pfn_AR_MPI_ISP_Init(ViPipe);
    // pfn_AR_MPI_ISP_Run(ViPipe);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    VIDEO_FRAME_INFO_S FrameInfo = {0};
    while (1)
    {
        if (quit)
        {
            printf("process exit \r\n");
            break;
        }
        sleep(1);
    }

    printf("exit process loop ! \r\n");
    pfn_AR_MPI_VO_SubscribeDisable(0);
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    printf("stop vo done! \r\n");

    pfn_AR_MPI_ISP_Exit(ViPipe);

    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    VI_DEV_PROP_S Prop = {0};
    pfn_AR_MPI_VIN_CloseDev(&Prop);
    printf("stop vi done ! \r\n");

    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[0].s32CoreId);
    printf("stop gdc done ! \r\n");

    SAMPLE_COMM_SYS_Exit();

    pfn_ar_hal_sys_mmz_free(Pa, Va);
    pfn_ar_hal_sys_mmz_free(PreImgPa, PreImgVa);
    printf("free buffer done! \r\n");

    return s32Ret;
}

AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST9(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    return 0;
}

// dp trigger vo start,  DP in normal mode   ( Can acquire DP buffer[n] only when dp frame[n] done)
AR_S32 test9_dual_dp_gdc_de_freerun_ld_warp_process(AR_VOID)
{
    AR_S32 s32Ret;

    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_rx";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(1, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    default_attr.stPubAttr.stTiming.hblank = 280;
    default_attr.stPubAttr.stTiming.vblank = 45;

    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_ar_hal_dp_rx_set_edid(&edid_groups[1]); // 1080p60
    unsigned int hpd_status = 0;
    if (pfn_ar_hal_dp_rx_get_hpd_status(&hpd_status) < 0)
    {
        printf("get hpd_status failed!\n");
        return -1;
    }
    if (hpd_status)
    {
        int ret = pfn_ar_hal_dp_rx_set_hpd_status(0);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);

        ret = pfn_ar_hal_dp_rx_set_hpd_status(1);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);
    }

    // pfn_AR_MPI_ISP_Init(ViPipe);
    // pfn_AR_MPI_ISP_Run(ViPipe);

    // de0 config=============================
    // init display
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST9;

    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    VO_USER_INTFSYNC_INFO_S stUserInfo = {0};
    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // de1 config=================
    // init display
    VoDev = 1;
    VoLayer = VO_LAYER_ID_VIDEO_1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr1 = {0};
    stIrqAttr1.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr1.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr1), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr1 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr1.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr1.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST9;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr1), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);

    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // Gdc0/1 Config
    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "/mnt/dptest/mesh1080p_b32f32_hm.dat");
    GDC_Load_Warp_Data_Array(pu64WarpParaPa[0], ppvWarpParaVa[0], WARP_FILE_NUM, s8WarpFiles);

    for (int i = 0; i < GDC_RUN_NUM; i++)
    {
        g_stGdcParam[i].stOutBuffer.astChannels[0].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[1].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[2].u32Stride = 4096;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

        g_stGdcParam[i].s32CoreId = i;
        g_stGdcParam[i].s32NonBlock = 1;

        g_stGdcParam[i].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8FrameStart = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8StartMode = 3;   // free-run mode
        g_stGdcParam[i].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshMode = 0;
        g_stGdcParam[i].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

        g_stGdcParam[i].u8LdEn = 1;
        g_stGdcParam[i].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
        g_stGdcParam[i].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride
        g_stGdcParam[i].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][0] & 0xffffffff);
    }

    // Gdc Config
    AR_U32 PreImgSize = 1920 * 1080 * 2;
    AR_U64 PreImgPa = {0};
    void *PreImgVa = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&PreImgPa, &PreImgVa, "PreImg", NULL, PreImgSize);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)PreImgVa, 0, PreImgSize);
    for (int i = 0; i < GDC_RUN_NUM; i++)
    {
        // gdc start, will hang at line32
        g_stGdcParam[i].stInBuffer.u32Width = 1920;
        g_stGdcParam[i].stInBuffer.u32Height = 1080;
        g_stGdcParam[i].stInBuffer.astChannels[0].u32Stride = 2048;
        g_stGdcParam[i].stInBuffer.astChannels[1].u32Stride = 1024;
        g_stGdcParam[i].stInBuffer.astChannels[2].u32Stride = 1024;
        // lb_lowdelay stride must 4096
        g_stGdcParam[i].stOutBuffer.u32Width = 1920;
        g_stGdcParam[i].stOutBuffer.u32Height = 1080;
        g_stGdcParam[i].stInBuffer.astChannels[0].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[0].u32AddrPhy = PreImgPa;
        g_stGdcParam[i].stInBuffer.astChannels[1].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[1].u32AddrPhy = PreImgPa;
        g_stGdcParam[i].stInBuffer.astChannels[2].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[2].u32AddrPhy = PreImgPa;

        g_stGdcParam[i].stLdParam.stOutLowdelay.enFormat = PIXEL_FORMAT_YVU_PLANAR_420;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(1920);
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(1920 / 2);
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(1920 / 2);

        s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[i]);
        if (s32Ret < 0)
        {
            printf("AR_MPI_GDC_ADV_Process fail\r\n");
            return -1;
        }
    }

    // DP START
    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    AR_U64 frm_cnt[GDC_RUN_NUM] = {0};
    VIDEO_FRAME_INFO_S FrameInfo = {0};
    while (1)
    {
        if (quit)
        {
            printf("process exit \r\n");
            break;
        }

        s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
            continue;
        }

        for (int i = 0; i < GDC_RUN_NUM; i++)
        {
            g_stGdcParam[i].stInBuffer.u32IsVB = 1;
            g_stGdcParam[i].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[i].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[i].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[i].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[i].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[i].stOutBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[i].stOutBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[i].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[i].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[i].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[i].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[i].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[i].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[i].stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
            ;
            g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
            g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
            g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
            g_stGdcParam[i].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][frm_cnt[i]++ % (WARP_FILE_NUM - 1)] & 0xffffffff);

            g_stGdcParam[i].stGdcParam.stStartCfg.u8FrameStart = 0;
            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[i]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }
        }

        s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
            return -1;
        }
    }

    printf("exit process loop ! \r\n");
    pfn_AR_MPI_VO_SubscribeDisable(0);
    pfn_AR_MPI_VO_SubscribeDisable(1);

    VoLayer = 0;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoLayer = 0x10;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    VoDev = 0;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoDev = 1;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    printf("stop vo done ! \r\n");

    pfn_AR_MPI_ISP_Exit(ViPipe);
    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    VI_DEV_PROP_S Prop = {0};
    pfn_AR_MPI_VIN_CloseDev(&Prop);
    printf("stop vi done ! \r\n");

    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[0].s32CoreId);
    printf("stop gdc[0] done ! \r\n");
    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[1].s32CoreId);
    printf("stop gdc[1] done ! \r\n");

    SAMPLE_COMM_SYS_Exit();

    pfn_ar_hal_sys_mmz_free(Pa, Va);
    pfn_ar_hal_sys_mmz_free(PreImgPa, PreImgVa);

    printf("free mmz done ! \r\n");

    return s32Ret;
}

AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST10(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    AR_S32 s32Ret = 0;
    static AR_S32 frm_cnt[2] = {0};

    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    VIDEO_FRAME_INFO_S FrameInfo = {0};

    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {
        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            s32Ret = pfn_ar_queue_pop_timeout(g_sample_gdc_bf_queue[dev_id], &FrameInfo, NULL, -1);
            if (s32Ret < 0)
            {
                assert(0);
            }
            g_stGdcParam[dev_id].stInBuffer.u32IsVB = 1;
            g_stGdcParam[dev_id].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[dev_id].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            // lb_lowdelay stride must 4096
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[dev_id].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][frm_cnt[dev_id]++ % 499] & 0xffffffff);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[dev_id]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }

            if (dev_id == 0) // or dev_id==1  only can release once!!!
            {
                s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                if (AR_SUCCESS != s32Ret)
                {
                    SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                    return -1;
                }
            }
        }
    }

    return 0;
}

// dp trigger vo start,  DP in soft-lowdelay mode ( Can acquire DP buffer[n] when dp frame[n] output >= 1 line)
AR_S32 test10_dual_dp_gdc_de_shadow_ld_warp_process(AR_VOID)
{
    AR_S32 s32Ret;

    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_rx";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(1, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;
    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);

    // dp output buffer after 1 line, only one buffer
    VI_CHN_EXT_ATTR_S stChnExtAttr = {0};
    stChnExtAttr.enLowDelayMode = VI_CH_LOW_DELAY_DDR;
    stChnExtAttr.stSoftLowdelayAttr.s32SofLowdeayEn = 0;
    pfn_AR_MPI_VI_SetChnExtAttr(ViPipe, ViChn, &stChnExtAttr);

    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    default_attr.stPubAttr.stTiming.hblank = 280;
    default_attr.stPubAttr.stTiming.vblank = 45;

    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_ar_hal_dp_rx_set_edid(&edid_groups[1]); // 1080p60
    unsigned int hpd_status = 0;
    if (pfn_ar_hal_dp_rx_get_hpd_status(&hpd_status) < 0)
    {
        printf("get hpd_status failed!\n");
        return -1;
    }
    if (hpd_status)
    {
        int ret = pfn_ar_hal_dp_rx_set_hpd_status(0);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);

        ret = pfn_ar_hal_dp_rx_set_hpd_status(1);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);
    }

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    AR_FLOAT Dpfps = 0;
    s32Ret = pfn_AR_MPI_VI_GetHightPricisionPipeFPS(ViPipe, ViChn, &Dpfps, 0xB01);
    if (AR_SUCCESS != s32Ret)
    {
        printf("AR_MPI_VI_GetHightPricisionPipeFPS failed with %d!\n", s32Ret);
        return s32Ret;
    }
    printf("=======AR_MPI_VI_GetHightPricisionPipeFPS:Dpfps %f \r\n", Dpfps);
    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);

    // de0 config=============================
    // init display
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST10;

    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, Dpfps);

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;

    stLowdelayAttr.stHardwareLowdelayInfo.statsSrc = AR_SYS_HARDWARE_STATS_SRC_DP_ISP;
    stLowdelayAttr.stHardwareLowdelayInfo.u32ThresholdLineCnt = 50;
    stLowdelayAttr.stHardwareLowdelayInfo.floatStepLineCnt = 0.057;

    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    VO_USER_INTFSYNC_INFO_S stUserInfo = {0};
    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // de1 config=================
    // init display
    VoDev = 1;
    VoLayer = VO_LAYER_ID_VIDEO_1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr1 = {0};
    stIrqAttr1.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr1.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr1), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr1 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr1.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr1.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST10;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr1), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, Dpfps);

    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // Gdc0/1 Config
    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "/mnt/dptest/mesh1080p_b32f32_hm.dat");
    GDC_Load_Warp_Data_Array(pu64WarpParaPa[0], ppvWarpParaVa[0], WARP_FILE_NUM, s8WarpFiles);

    for (int i = 0; i < GDC_RUN_NUM; i++)
    {
        g_stGdcParam[i].stOutBuffer.astChannels[0].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[1].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[2].u32Stride = 4096;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

        g_stGdcParam[i].s32CoreId = i;
        g_stGdcParam[i].s32NonBlock = 1;

        g_stGdcParam[i].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8FrameStart = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8StartMode = 2;   // shadow mode
        g_stGdcParam[i].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshMode = 0;
        g_stGdcParam[i].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

        g_stGdcParam[i].u8LdEn = 1;
        g_stGdcParam[i].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
        g_stGdcParam[i].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride
        g_stGdcParam[i].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][0] & 0xffffffff);
    }

    // Gdc Config
    AR_U32 PreImgSize = 1920 * 1080 * 2;
    AR_U64 PreImgPa = {0};
    void *PreImgVa = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&PreImgPa, &PreImgVa, "PreImg", NULL, PreImgSize);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)PreImgVa, 0, PreImgSize);
    for (int i = 0; i < GDC_RUN_NUM; i++)
    {
        // gdc start, will hang at line32
        g_stGdcParam[i].stInBuffer.u32Width = 1920;
        g_stGdcParam[i].stInBuffer.u32Height = 1080;
        g_stGdcParam[i].stInBuffer.astChannels[0].u32Stride = 2048;
        g_stGdcParam[i].stInBuffer.astChannels[1].u32Stride = 1024;
        g_stGdcParam[i].stInBuffer.astChannels[2].u32Stride = 1024;
        // lb_lowdelay stride must 4096
        g_stGdcParam[i].stOutBuffer.u32Width = 1920;
        g_stGdcParam[i].stOutBuffer.u32Height = 1080;
        g_stGdcParam[i].stInBuffer.astChannels[0].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[0].u32AddrPhy = PreImgPa;
        g_stGdcParam[i].stInBuffer.astChannels[1].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[1].u32AddrPhy = PreImgPa;
        g_stGdcParam[i].stInBuffer.astChannels[2].uptrAddrVirt = PreImgVa;
        g_stGdcParam[i].stInBuffer.astChannels[2].u32AddrPhy = PreImgPa;

        g_stGdcParam[i].stLdParam.stOutLowdelay.enFormat = PIXEL_FORMAT_YVU_PLANAR_420;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(1920);
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(1920 / 2);
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(1920 / 2);

        s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[i]);
        if (s32Ret < 0)
        {
            printf("AR_MPI_GDC_ADV_Process fail\r\n");
            return -1;
        }
    }

    g_sample_gdc_bf_queue[0] = pfn_ar_queue_create(4, sizeof(VIDEO_FRAME_INFO_S), NULL);
    g_sample_gdc_bf_queue[1] = pfn_ar_queue_create(4, sizeof(VIDEO_FRAME_INFO_S), NULL);
    pthread_t tid;
    pthread_create(&tid, NULL, sample_pthread_gdc_get_stream, NULL);

    // DP START
    // pfn_AR_MPI_ISP_Init(ViPipe);
    // pfn_AR_MPI_ISP_Run(ViPipe);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    while (1)
    {
        if (quit)
        {
            printf("process exit \r\n");
            break;
        }
        sleep(1);
    }
    printf("exit process loop ! \r\n");
    pfn_AR_MPI_VO_SubscribeDisable(0);
    pfn_AR_MPI_VO_SubscribeDisable(1);
    VoLayer = 0;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoLayer = 0x10;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    VoDev = 0;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoDev = 1;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    printf("stop vo done ! \r\n");

    pfn_AR_MPI_ISP_Exit(ViPipe);
    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    VI_DEV_PROP_S Prop = {0};
    pfn_AR_MPI_VIN_CloseDev(&Prop);
    printf("stop vi done ! \r\n");

    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[0].s32CoreId);
    printf("stop gdc[0] done ! \r\n");
    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[1].s32CoreId);
    printf("stop gdc[1] done ! \r\n");

    SAMPLE_COMM_SYS_Exit();

    pfn_ar_hal_sys_mmz_free(Pa, Va);
    pfn_ar_hal_sys_mmz_free(PreImgPa, PreImgVa);
    printf("free buffer done ! \r\n");

    return s32Ret;
}

// dp trigger vo start,  DP in normal mode   ( Can acquire DP buffer[n] only when dp frame[n] done)
// dp->cf50enc------->cf50dec->gdc
AR_S32 test11_dp_gdc_de_freerun_ld_warp_cf50_process(AR_VOID)
{
    AR_S32 s32Ret;

    ISP_SNS_OBJ_S *p_obj = NULL;
    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_rx";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(1, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);

    // DP+CF50

    default_attr.stChnAttr.enCompressMode = COMPRESS_MODE_SEG;
    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    VI_CH_CF50_CMP_ATTR_T pstChnCmpAttr = {0};
    AR_S32 CmpRate = 0;
    if (CmpRate == 0)
    {
        pstChnCmpAttr.stChCmpAttr.enMode = CF50_CMP_MODE_LOSSLESS;
    }
    else if (CmpRate == 1) // 75p
    {
        pstChnCmpAttr.stChCmpAttr.enMode = CF50_CMP_MODE_LOSS;
        pstChnCmpAttr.stChCmpAttr.enCmpRate = 96;
    }
    else if (CmpRate == 2) // 50p
    {
        pstChnCmpAttr.stChCmpAttr.enMode = CF50_CMP_MODE_LOSS;
        pstChnCmpAttr.stChCmpAttr.enCmpRate = 64;
    }
    else if (CmpRate == 3) // 25p
    {
        pstChnCmpAttr.stChCmpAttr.enMode = CF50_CMP_MODE_LOSS;
        pstChnCmpAttr.stChCmpAttr.enCmpRate = 32;
    }
    printf("pstChnCmpAttr.stChCmpAttr.enMode = %d", pstChnCmpAttr.stChCmpAttr.enMode);
    pfn_AR_MPI_VI_SetChnCmpAttr(ViPipe, ViChn, &pstChnCmpAttr);

    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    default_attr.stPubAttr.stTiming.hblank = 280;
    default_attr.stPubAttr.stTiming.vblank = 45;
    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);

    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_ar_hal_dp_rx_set_edid(&edid_groups[1]); // 1080p60
    unsigned int hpd_status = 0;
    if (pfn_ar_hal_dp_rx_get_hpd_status(&hpd_status) < 0)
    {
        printf("get hpd_status failed!\n");
        return -1;
    }
    if (hpd_status)
    {
        int ret = pfn_ar_hal_dp_rx_set_hpd_status(0);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);

        ret = pfn_ar_hal_dp_rx_set_hpd_status(1);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);
    }

    // pfn_AR_MPI_ISP_Init(ViPipe);
    // pfn_AR_MPI_ISP_Run(ViPipe);

    //====================================================================================================
    // cfg VO
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST7;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    stPubAttr.enIntfType = VO_INTF_BT1120;
    stPubAttr.enIntfSync = VO_OUTPUT_USER; // VO_OUTPUT_1080P60;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;  // AR_FALSE;  //sync internal

    stPubAttr.stSyncInfo.u8Intfb = 0;

    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;

    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;

    stPubAttr.stSyncInfo.bIop = iop;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);

    /** gdc lines_64_enable=1, start vo line_cnts < 64;
     *	gdc lines_64_enable=0, start vo line_cnts < 32;
     */

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_DP;
    stStartAttr.stAutoAttr.u32InitLineCnt = 100;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    VO_USER_INTFSYNC_INFO_S stUserInfo = {0};
    stUserInfo.enClkControl = VO_CLK_MANUAL;
    stUserInfo.stUserIntfSyncAttr.enClkSource = VO_CLK_SOURCE_PLL0;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetUserIntfSyncInfo(VoDev, &stUserInfo), "AR_MPI_VO_SetUserIntfSyncInfo");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    //====================================================================================================
    // Gdc Config
    AR_U32 PreImgSize = 1920 * 1080 * 2;
    AR_U64 PreImgPa = {0};
    void *PreImgVa = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&PreImgPa, &PreImgVa, "PreImg", NULL, PreImgSize);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)PreImgVa, 0, PreImgSize);

    g_stGdcParam[0].stOutBuffer.astChannels[0].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[1].u32Stride = 4096;
    g_stGdcParam[0].stOutBuffer.astChannels[2].u32Stride = 4096;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
    g_stGdcParam[0].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

    g_stGdcParam[0].s32CoreId = 0;
    g_stGdcParam[0].s32NonBlock = 1;
    g_stGdcParam[0].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8FrameStart = 1;
    g_stGdcParam[0].stGdcParam.stStartCfg.u8StartMode = 3;   // free-run mode
    g_stGdcParam[0].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshMode = 0;
    g_stGdcParam[0].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
    g_stGdcParam[0].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

    g_stGdcParam[0].u8LdEn = 1;
    g_stGdcParam[0].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
    g_stGdcParam[0].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "/mnt/dptest/mesh1080p_b32f32_hm.dat");

    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
    g_stGdcParam[0].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride

    GDC_Load_Warp_Data_Array(pu64WarpParaPa[0], ppvWarpParaVa[0], WARP_FILE_NUM, s8WarpFiles);
    g_stGdcParam[0].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][0] & 0xffffffff);

    // gdc start, will hang at line32
    g_stGdcParam[0].stInBuffer.u32Width = 1920;
    g_stGdcParam[0].stInBuffer.u32Height = 1080;
    g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = 2048;
    g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = 1024;
    g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = 1024;
    // lb_lowdelay stride must 4096
    g_stGdcParam[0].stOutBuffer.u32Width = 1920;
    g_stGdcParam[0].stOutBuffer.u32Height = 1080;
    g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = PreImgPa;
    g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = PreImgPa;
    g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = PreImgVa;
    g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = PreImgPa;

    g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(1920);
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(1920 / 2);
    g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(1920 / 2);

    s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
    if (s32Ret < 0)
    {
        printf("AR_MPI_GDC_ADV_Process fail\r\n");
        return -1;
    }

    // DP START
    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    AR_U64 frm_cnt = 0;
    VIDEO_FRAME_INFO_S FrameInfo = {0};
    while (1)
    {
        if (quit)
        {
            printf("process exit \r\n");
            break;
        }
        // printf("============================003\r\n");
        s32Ret = pfn_AR_MPI_VI_GetChnFrame(ViPipe, ViChn, &FrameInfo, 0xffffffff);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi get chn frame failed. s32Ret: 0x%x\n", s32Ret);
            continue;
        }
        // printf("============================004\r\n");

        // gdc+cf50 dec
        g_stGdcParam[0].u8Cf50En = 1;
        g_stGdcParam[0].stCF50Param.enCF50Mode = AR_GDC_CF50_MODE_DECODE;
        g_stGdcParam[0].stCF50Param.stCF50Decoder.enFormat = AR_SYS_PIXEL_FORMAT_YVU_PLANAR_420;
        g_stGdcParam[0].stCF50Param.stCF50Decoder.u32Align = 32;
        g_stGdcParam[0].stCF50Param.stCF50Decoder.u32Width = FrameInfo.stVFrame.u32Width;
        g_stGdcParam[0].stCF50Param.stCF50Decoder.u32Height = FrameInfo.stVFrame.u32Height;

        g_stGdcParam[0].stCF50Param.stCF50Decoder.bLossy = AR_TRUE;
        if (CmpRate == 0)
        {
            g_stGdcParam[0].stCF50Param.stCF50Decoder.enRatioTo = CF50_COMPRESS_RATIO_TO_100P;
            g_stGdcParam[0].stCF50Param.stCF50Decoder.bLossy = AR_FALSE;
        }
        else if (CmpRate == 1) // 75p
        {
            g_stGdcParam[0].stCF50Param.stCF50Decoder.enRatioTo = CF50_COMPRESS_RATIO_TO_75P;
        }
        else if (CmpRate == 2) // 50p
        {
            g_stGdcParam[0].stCF50Param.stCF50Decoder.enRatioTo = CF50_COMPRESS_RATIO_TO_50P;
        }
        else if (CmpRate == 3) // 25p
        {
            g_stGdcParam[0].stCF50Param.stCF50Decoder.enRatioTo = CF50_COMPRESS_RATIO_TO_25P;
        }

        g_stGdcParam[0].stCF50Param.stCF50Decoder.u32Cf50HeaderPhy[0] = FrameInfo.stVFrame.u64HeaderPhyAddr[0];
        g_stGdcParam[0].stCF50Param.stCF50Decoder.uptrCf50HeaderVirt[0] = (AR_UINTPTR)FrameInfo.stVFrame.u64HeaderVirAddr[0];
        g_stGdcParam[0].stCF50Param.stCF50Decoder.u32Cf50HeaderPhy[1] = FrameInfo.stVFrame.u64HeaderPhyAddr[1];
        g_stGdcParam[0].stCF50Param.stCF50Decoder.uptrCf50HeaderVirt[1] = (AR_UINTPTR)FrameInfo.stVFrame.u64HeaderVirAddr[1];
        g_stGdcParam[0].stCF50Param.stCF50Decoder.u32Cf50HeaderPhy[2] = FrameInfo.stVFrame.u64HeaderPhyAddr[2];
        g_stGdcParam[0].stCF50Param.stCF50Decoder.uptrCf50HeaderVirt[2] = (AR_UINTPTR)FrameInfo.stVFrame.u64HeaderVirAddr[2];

        g_stGdcParam[0].stInBuffer.u32IsVB = 1;
        g_stGdcParam[0].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
        g_stGdcParam[0].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
        g_stGdcParam[0].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
        g_stGdcParam[0].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
        g_stGdcParam[0].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
        // lb_lowdelay stride must 4096
        g_stGdcParam[0].stOutBuffer.u32Width = FrameInfo.stVFrame.u32Width;
        g_stGdcParam[0].stOutBuffer.u32Height = FrameInfo.stVFrame.u32Height;
        g_stGdcParam[0].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
        g_stGdcParam[0].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
        g_stGdcParam[0].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
        g_stGdcParam[0].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
        g_stGdcParam[0].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
        g_stGdcParam[0].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

        g_stGdcParam[0].stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
        ;
        g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
        g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
        g_stGdcParam[0].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);

        g_stGdcParam[0].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[0][frm_cnt % (WARP_FILE_NUM - 1)] & 0xffffffff);
        g_stGdcParam[0].stGdcParam.stStartCfg.u8FrameStart = 0;
        s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[0]);
        if (s32Ret < 0)
        {
            printf("AR_MPI_GDC_ADV_Process fail\r\n");
            pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            assert(0);
        }

        s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
        if (AR_SUCCESS != s32Ret)
        {
            SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
            return -1;
        }
    }

    printf("exit process loop ! \r\n");
    pfn_AR_MPI_VO_SubscribeDisable(0);
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    printf("stop vo done ! \r\n");

    pfn_AR_MPI_ISP_Exit(ViPipe);

    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);
    VI_DEV_PROP_S Prop = {0};
    pfn_AR_MPI_VIN_CloseDev(&Prop);
    printf("stop vi done ! \r\n");

    pfn_AR_MPI_GDC_ADV_Stop(g_stGdcParam[0].s32CoreId);
    printf("stop gdc done ! \r\n");

    SAMPLE_COMM_SYS_Exit();

    pfn_ar_hal_sys_mmz_free(Pa, Va);
    pfn_ar_hal_sys_mmz_free(PreImgPa, PreImgVa);

    printf("free buffer done ! \r\n");

    return s32Ret;
}

static CallCounter s_test12_vo_callback_counter[DPU_ID_COUNT]{
    CallCounter("dpu0", DPU_FPS_SAMPLE_SIZE),
    CallCounter("dpu1", DPU_FPS_SAMPLE_SIZE)};

#define BLOCKALL_WARP_SIZE (9 * 4 * 35)
#define BLOCK32X4_WARP_SIZE (9 * 4 * 4)
#define BLOCK32X1_WARP_SIZE (9 * 4)

AR_S32 SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST12(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info)
{
    AR_S32 s32Ret = 0;
    static AR_S32 frm_cnt[2] = {0};

    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    VIDEO_FRAME_INFO_S FrameInfo = {0};

#if 1
    if (sub_info->u32IrqType & IRQ_TYPE_VSYNC)
    {
        s_test12_vo_callback_counter[dev_id].Update();
        char *pWarpCur = (char *)ppvWarpParaVa[dev_id][128 % (WARP_FILE_NUM - 1)];
        char *pWarpNext = (char *)ppvWarpParaVa[dev_id][frm_cnt[dev_id]++ % (WARP_FILE_NUM - 1)];
        memcpy(pWarpCur + 4 * 9 * 4, pWarpNext + 4 * 9 * 4, BLOCK32X4_WARP_SIZE); // memset(128-256)

        AR_GDC_ADV_LINE_PARAMS_S stLineParam = {0};
        stLineParam.s32CoreId = dev_id;
        stLineParam.s32LineNum = 256;

        s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&stLineParam);
        if (s32Ret < 0)
        {
            AR_LOG_RAW("AR_MPI_GDC_ADV_Config_Line fail\r\n");
        }
    }

    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {

        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 0)
        {
            char *pWarpCur = (char *)ppvWarpParaVa[dev_id][128 % (WARP_FILE_NUM - 1)];
            char *pWarpNext = (char *)ppvWarpParaVa[dev_id][frm_cnt[dev_id]++ % (WARP_FILE_NUM - 1)];
            memcpy(pWarpCur + 4 * 9 * 8, pWarpNext + 4 * 9 * 8, BLOCK32X4_WARP_SIZE); // memset(256-384)

            AR_GDC_ADV_LINE_PARAMS_S stLineParam = {0};
            stLineParam.s32CoreId = dev_id;
            stLineParam.s32LineNum = 384;

            s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&stLineParam);
            if (s32Ret < 0)
            {
                AR_LOG_RAW("AR_MPI_GDC_ADV_Config_Line fail\r\n");
            }
        }

        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 1)
        {
            char *pWarpCur = (char *)ppvWarpParaVa[dev_id][128 % (WARP_FILE_NUM - 1)];
            char *pWarpNext = (char *)ppvWarpParaVa[dev_id][frm_cnt[dev_id]++ % (WARP_FILE_NUM - 1)];
            memcpy(pWarpCur + 4 * 9 * 12, pWarpNext + 4 * 9 * 12, BLOCK32X4_WARP_SIZE); // memset(384-512)

            AR_GDC_ADV_LINE_PARAMS_S stLineParam = {0};
            stLineParam.s32CoreId = dev_id;
            stLineParam.s32LineNum = 512;

            s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&stLineParam);
            if (s32Ret < 0)
            {
                AR_LOG_RAW("AR_MPI_GDC_ADV_Config_Line fail\r\n");
            }
        }

        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            char *pWarpCur = (char *)ppvWarpParaVa[dev_id][128 % (WARP_FILE_NUM - 1)];
            char *pWarpNext = (char *)ppvWarpParaVa[dev_id][frm_cnt[dev_id]++ % (WARP_FILE_NUM - 1)];
            memcpy(pWarpCur + 4 * 9 * 16, pWarpNext + 4 * 9 * 16, BLOCK32X4_WARP_SIZE); // memset(512-640)

            AR_GDC_ADV_LINE_PARAMS_S stLineParam = {0};
            stLineParam.s32CoreId = dev_id;
            stLineParam.s32LineNum = 640;

            s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&stLineParam);
            if (s32Ret < 0)
            {
                AR_LOG_RAW("AR_MPI_GDC_ADV_Config_Line fail\r\n");
            }
        }

        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 3)
        {
            char *pWarpCur = (char *)ppvWarpParaVa[dev_id][128 % (WARP_FILE_NUM - 1)];
            char *pWarpNext = (char *)ppvWarpParaVa[dev_id][frm_cnt[dev_id] % (WARP_FILE_NUM - 1)];
            memcpy(pWarpCur + 4 * 9 * 20, pWarpNext + 4 * 9 * 20, BLOCK32X4_WARP_SIZE); // memset(640-768)

            AR_GDC_ADV_LINE_PARAMS_S stLineParam = {0};
            stLineParam.s32CoreId = dev_id;
            stLineParam.s32LineNum = 768;

            s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&stLineParam);
            if (s32Ret < 0)
            {
                AR_LOG_RAW("AR_MPI_GDC_ADV_Config_Line fail\r\n");
            }
        }

        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 4)
        {
            char *pWarpCur = (char *)ppvWarpParaVa[dev_id][128 % (WARP_FILE_NUM - 1)];
            char *pWarpNext = (char *)ppvWarpParaVa[dev_id][frm_cnt[dev_id] % (WARP_FILE_NUM - 1)];
            memcpy(pWarpCur + 4 * 9 * 24, pWarpNext + 4 * 9 * 24, BLOCK32X4_WARP_SIZE); // memset(768-896)

            AR_GDC_ADV_LINE_PARAMS_S stLineParam = {0};
            stLineParam.s32CoreId = dev_id;
            stLineParam.s32LineNum = 896;

            s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&stLineParam);
            if (s32Ret < 0)
            {
                AR_LOG_RAW("AR_MPI_GDC_ADV_Config_Line fail\r\n");
            }
        }

        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 5)
        {
            char *pWarpCur = (char *)ppvWarpParaVa[dev_id][128 % (WARP_FILE_NUM - 1)];
            char *pWarpNext = (char *)ppvWarpParaVa[dev_id][frm_cnt[dev_id] % (WARP_FILE_NUM - 1)];
            memcpy(pWarpCur + 4 * 9 * 28, pWarpNext + 4 * 9 * 28, BLOCK32X4_WARP_SIZE); // memset(896-1024)

            AR_GDC_ADV_LINE_PARAMS_S stLineParam = {0};
            stLineParam.s32CoreId = dev_id;
            stLineParam.s32LineNum = 1088;

            s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&stLineParam);
            if (s32Ret < 0)
            {
                AR_LOG_RAW("AR_MPI_GDC_ADV_Config_Line fail\r\n");
            }
        }
    }

    if (sub_info->u32IrqType & IRQ_TYPE_FRAME_DONE)
    {
        AR_GDC_ADV_LINE_PARAMS_S stLineParam = {0};
        stLineParam.s32CoreId = dev_id;
        stLineParam.s32LineNum = 128;

        s32Ret = pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(&stLineParam);
        if (s32Ret < 0)
        {
            AR_LOG_RAW("AR_MPI_GDC_ADV_Config_Line fail\r\n");
        }
    }

#endif

    if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
    {
        if (sub_info->stExtpara.stIntervalLinePara.u32CurBlockID == 2)
        {
            s32Ret = pfn_ar_queue_pop_timeout(g_sample_gdc_bf_queue[dev_id], &FrameInfo, NULL, -1);
            if (s32Ret < 0)
            {
                assert(0);
            }

            g_stGdcParam[dev_id].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
            g_stGdcParam[dev_id].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
            g_stGdcParam[dev_id].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

            g_stGdcParam[dev_id].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[dev_id][128 % (WARP_FILE_NUM - 1)] & 0xffffffff);

            s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[dev_id]);
            if (s32Ret < 0)
            {
                printf("AR_MPI_GDC_ADV_Process fail\r\n");
                pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                assert(0);
            }

            if (dev_id == 0) // or dev_id==1  only can release once!!!
            {
                s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                if (AR_SUCCESS != s32Ret)
                {
                    SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                    return -1;
                }
            }
        }
    }
    return 0;
}

AR_S32 test12_dual_dp_gdc_de_shadow_ld_warp_cpubp_process(AR_VOID)
{
    AR_S32 s32Ret;
    ISP_SNS_OBJ_S *p_obj = NULL;

    SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
    SIZE_S stSize;
    VB_CONFIG_S stVbConf;
    AR_U32 u32BlkSize;
    SIZE_S stSize_ch;

    AR_S32 s32ViCnt = 1;
    VI_DEV ViDev = 0;
    VI_PIPE ViPipe = ViDev;
    VI_CHN ViChn = 0;
    AR_S32 mipi_index = 0;
    AR_S8 s8I2cDev = 2;

    VO_CHN VoChn = 0;
    // SAMPLE_VO_CONFIG_S_S stVoConfig;
    int sensor_mode = 0x80;

    AR_CHAR *sensor_name = "dp_rx";
    void *handle = pfn_AR_MPI_VIN_GetSensorObj(sensor_name, &p_obj);
    if (!handle || !p_obj)
    {
        printf("no %s driver , do nothing !!!\n", sensor_name);
        if (handle)
        {
            pfn_AR_MPI_VIN_CloseSensorObj(handle);
        }
        return 0;
    }

    if (p_obj->pfnGetDefaultAttr)
    {
        p_obj->pfnGetDefaultAttr(sensor_mode, &default_attr);
    }
    else
    {
        ar_err("pfnGetDefaultAttr is null, exit the test");
        return -1;
    }
    stSize = default_attr.stPubAttr.stSnsSize;
    stSize_ch = default_attr.stChnAttr.stSize;

    /*config vb*/
    pfn_ar_memset(&stVbConf, sizeof(VB_CONFIG_S), 0, sizeof(VB_CONFIG_S));
    stVbConf.u32MaxPoolCnt = 2;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[0].u32BlkCnt = 10;

    u32BlkSize = COMMON_GetPicBufferSize(stSize.u32Width, stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
    stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
    stVbConf.astCommPool[1].u32BlkCnt = 10;

    s32Ret = SAMPLE_COMM_SYS_Init(&stVbConf);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("system init failed with %d!\n", s32Ret);
        return s32Ret;
    }

    int isp_fre = 300000000;
    int vif_fre = 300000000;
    int pcs_fre = 100000000;
    SAMPLE_AR_MPI_VIN_OpenDev_Local(1, isp_fre, vif_fre, isp_fre, pcs_fre);

    /*start vi*/
    pfn_AR_MPI_VI_SetMipiBindDev(ViDev, mipi_index);
    default_attr.stComboAttr.devno = mipi_index;
    pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
    pfn_AR_MPI_VI_SetDevAttr(ViDev, &default_attr.stDevAttr);
    pfn_AR_MPI_VI_EnableDev(ViDev);
    VI_DEV_BIND_PIPE_S stDevBindPipe;
    stDevBindPipe.u32Num = 1;
    stDevBindPipe.PipeId[0] = ViPipe;
    pfn_AR_MPI_VI_SetDevBindPipe(ViDev, &stDevBindPipe);
    pfn_AR_MPI_VI_CreatePipe(ViPipe, &default_attr.stPipeAttr);
    pfn_AR_MPI_VI_StartPipe(ViPipe);

    // set ch format to raw8
    default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
    // default_attr.stChnAttr.enPixelFormat=PIXEL_FORMAT_BGR_888_PLANAR;
    default_attr.stChnAttr.stSize = stSize_ch;

    pfn_AR_MPI_VI_SetChnAttr(ViPipe, ViChn, &default_attr.stChnAttr);
    pfn_AR_MPI_VI_EnableChn(ViPipe, ViChn);

    pfn_AR_MPI_VIN_PipeBindSensor(ViPipe, p_obj, s8I2cDev);

    pfn_AR_MPI_ISP_MemInit(ViPipe);
    default_attr.stPubAttr.u8SnsMode = sensor_mode;
    default_attr.stPubAttr.stTiming.hblank = 280;
    default_attr.stPubAttr.stTiming.vblank = 45;

    pfn_AR_MPI_ISP_SetPubAttr(ViPipe, &default_attr.stPubAttr);
    VI_PIPE_EXT_ATTR_S stPipeAttr;
    pfn_AR_MPI_VI_GetPipeExtAttr(ViPipe, &stPipeAttr);
    stPipeAttr.bFoucs = 0;
    pfn_AR_MPI_VI_SetPipeExtAttr(ViPipe, &stPipeAttr);

    pfn_ar_hal_dp_rx_set_edid(&edid_groups[1]); // 1080p60
    unsigned int hpd_status = 0;
    if (pfn_ar_hal_dp_rx_get_hpd_status(&hpd_status) < 0)
    {
        printf("get hpd_status failed!\n");
        return -1;
    }
    if (hpd_status)
    {
        int ret = pfn_ar_hal_dp_rx_set_hpd_status(0);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);

        ret = pfn_ar_hal_dp_rx_set_hpd_status(1);
        if (ret)
            printf("ar_hal_dp_rx_set_hpd_status failed %x\n", ret);
    }

    pfn_AR_MPI_ISP_Init(ViPipe);
    pfn_AR_MPI_ISP_Run(ViPipe);

    // de0 config=============================
    // init display
    AR_S32 VoDev = 0;
    AR_S32 VoLayer = 0;
    AR_S32 VoChnNum = 1;
    VO_PUB_ATTR_S stPubAttr = {0};
    VO_VIDEO_LAYER_ATTR_S stLayerAttr = {0};
    SIZE_S stDevSize = {0};
    int iop = 1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr0 = {0};
    stIrqAttr0.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr0.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr0), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr0 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr0.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr0.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST12;

    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr0), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    /* USER SET VO DEV SYNC INFO */
    stPubAttr.enIntfSync = VO_OUTPUT_USER;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;
    stPubAttr.stSyncInfo.bIop = AR_TRUE;
    stPubAttr.stSyncInfo.u8Intfb = 0;
    stPubAttr.stSyncInfo.u16Hpw = 60;
    stPubAttr.stSyncInfo.u16Hbb = 200;
    stPubAttr.stSyncInfo.u16Hact = 1920;
    stPubAttr.stSyncInfo.u16Hfb = 120;
    stPubAttr.stSyncInfo.u16Vpw = 2;
    stPubAttr.stSyncInfo.u16Vbb = 14;
    stPubAttr.stSyncInfo.u16Vact = 1080;
    stPubAttr.stSyncInfo.u16Vfb = 16;
    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;
    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;
    stPubAttr.enIntfType = VO_INTF_MIPI;

    int ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);

    VO_START_ATTR_S stStartAttr = {0};
    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_GDC0;
    /** gdc lines_64_enable=1, start vo line_cnts < 64;
     *	gdc lines_64_enable=0, start vo line_cnts < 32;
     */
    stStartAttr.stAutoAttr.u32InitLineCnt = 1;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    VO_LOWDELAY_ATTR_S stLowdelayAttr = {0};
    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    // s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_60Hz);
    s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_90Hz);
    s32Ret |= pfn_AR_MPI_VO_Dsi_Enable(VoDev);
    if (s32Ret)
    {
        BOARD_LOG_ERROR("VO_{} set dsi failed with {:x}", VoDev, s32Ret);
        return s32Ret;
    }

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // de1 config=================
    // init display
    VoDev = 1;
    VoLayer = VO_LAYER_ID_VIDEO_1;

    // Vo Config
    /* SET IRQ, ENABLE SubscribeEnable */
    VO_IRQ_ATTR_S stIrqAttr1 = {0};
    stIrqAttr1.enIrqType = IRQ_TYPE_INTERVAL_LINE;
    stIrqAttr1.stIrqAttr.stLineIrqPara.u32LineCount = 128;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetIrqAttr(VoDev, &stIrqAttr1), "AR_MPI_VO_SetIrqAttr");

    VO_SUBSCRIBE_ATTR_S stSubAttr1 = {0};
    // stSubAttr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_FIX_LINE | IRQ_TYPE_FRAME_DONE | IRQ_TYPE_VSYNC;
    stSubAttr1.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
    stSubAttr1.subscribe_call_back = SAMPLE_SHADOW_LOWDELAY_SUBSCRIBE_CALL_BACK_TEST12;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SubscribeEnable(VoDev, &stSubAttr1), "AR_MPI_VO_SubscribeEnable");

    /* SET VO PUB ATTR OF USER TYPE */
    SAMPLE_VO_GetUserPubBaseAttr(&stPubAttr);

    /* USER SET VO DEV SYNC INFO */
    stPubAttr.enIntfSync = VO_OUTPUT_USER;
    stPubAttr.stSyncInfo.bSynm = AR_TRUE;
    stPubAttr.stSyncInfo.bIop = AR_TRUE;
    stPubAttr.stSyncInfo.u8Intfb = 0;
    stPubAttr.stSyncInfo.u16Hpw = 60;
    stPubAttr.stSyncInfo.u16Hbb = 200;
    stPubAttr.stSyncInfo.u16Hact = 1920;
    stPubAttr.stSyncInfo.u16Hfb = 120;
    stPubAttr.stSyncInfo.u16Vpw = 2;
    stPubAttr.stSyncInfo.u16Vbb = 14;
    stPubAttr.stSyncInfo.u16Vact = 1080;
    stPubAttr.stSyncInfo.u16Vfb = 16;
    stPubAttr.stSyncInfo.u16Hmid = 0;
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;
    stPubAttr.stSyncInfo.bIdv = AR_FALSE;
    stPubAttr.stSyncInfo.bIhs = AR_FALSE;
    stPubAttr.stSyncInfo.bIvs = AR_FALSE;
    stPubAttr.enIntfType = VO_INTF_MIPI;

    ret = pfn_AR_MPI_VO_SetPubAttr(VoDev, &stPubAttr);
    // ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 60);
    ret = pfn_AR_MPI_VO_SetDevFrameRate(VoDev, 90);

    stStartAttr.enMode = VO_START_AUTO;
    stStartAttr.stAutoAttr.stSrcSel = AR_SYS_HARDWARE_SRC_GDC1;
    /** gdc lines_64_enable=1, start vo line_cnts < 64;
     *	gdc lines_64_enable=0, start vo line_cnts < 32;
     */
    stStartAttr.stAutoAttr.u32InitLineCnt = 1;
    pfn_AR_MPI_VO_SetStartAttr(VoDev, &stStartAttr);

    stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
    stLowdelayAttr.layerId = 0;
    stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
    SAMPLE_CHECK_RET(pfn_AR_MPI_VO_SetLowdelayAttr(VoDev, &stLowdelayAttr), "AR_MPI_VO_SetLowdelayAttr");

    /* ENABLE VO DEV */
    ret = pfn_AR_MPI_VO_Enable(VoDev);

    s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_60Hz);
    // s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(VoDev, &pstDsiCfg_90Hz);
    s32Ret |= pfn_AR_MPI_VO_Dsi_Enable(VoDev);
    if (s32Ret)
    {
        BOARD_LOG_ERROR("VO_{} set dsi failed with {:x}", VoDev, s32Ret);
        return s32Ret;
    }

    /*SET VO LAYER ATTR*/
    stDevSize.u32Width = 1920;
    stDevSize.u32Height = 1080;

    stLayerAttr.bClusterMode = AR_FALSE;
    stLayerAttr.bDoubleFrame = AR_FALSE;
    stLayerAttr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    stLayerAttr.enPixFormat = default_attr.stChnAttr.enPixelFormat;

    stLayerAttr.stDispRect.s32X = 0;
    stLayerAttr.stDispRect.s32Y = 0;
    stLayerAttr.stDispRect.u32Height = stDevSize.u32Height;
    stLayerAttr.stDispRect.u32Width = stDevSize.u32Width;

    stLayerAttr.stImageSize.u32Height = stDevSize.u32Height;
    stLayerAttr.stImageSize.u32Width = stDevSize.u32Width;

    stLayerAttr.u32Stride[0] = 4096; // linebuffer
    stLayerAttr.u32Stride[1] = 4096;
    stLayerAttr.u32Stride[2] = 4096;

    stLayerAttr.u32DispFrmRt = 30;
    CHECK_RET(pfn_AR_MPI_VO_SetVideoLayerAttr(VoLayer, &stLayerAttr), "AR_MPI_VO_SetVideoLayerAttr");
    CHECK_RET(pfn_AR_MPI_VO_EnableVideoLayer(VoLayer), "AR_MPI_VO_EnableVideoLayer");

    // Gdc0 Config
    int Size = 32 * 1024;
    AR_U64 Pa = {0};
    void *Va = {NULL};
    s32Ret = pfn_ar_hal_sys_mmz_alloc(&Pa, &Va, "Mesh", NULL, Size);
    if (s32Ret)
    {
        printf(" get Mesh addr error!\r\n");
        return AR_NULL;
    }
    memset((AR_CHAR *)Va, 0, Size);
    SAMPLE_GDC_Load_Params_General_Local(Va, "mesh_cv.dat");

    for (int i = 0; i < GDC_RUN_NUM; i++)
    {
        GDC_Load_Warp_Data_Array(pu64WarpParaPa[i], ppvWarpParaVa[i], WARP_FILE_NUM, s8WarpFiles);

        g_stGdcParam[i].stOutBuffer.astChannels[0].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[1].u32Stride = 4096;
        g_stGdcParam[i].stOutBuffer.astChannels[2].u32Stride = 4096;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
        g_stGdcParam[i].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

        g_stGdcParam[i].s32CoreId = i;
        g_stGdcParam[i].s32NonBlock = 1;

        g_stGdcParam[i].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8FrameStart = 1;
        g_stGdcParam[i].stGdcParam.stStartCfg.u8StartMode = 2;   // shadow mode
        g_stGdcParam[i].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshMode = 0;
        g_stGdcParam[i].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = 0; // inner bilinear
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = 0;       // warpdat
        g_stGdcParam[i].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = 61;

        g_stGdcParam[i].stGdcParam.stLdCfg.stCpuBp.u8CpuBpEn = 1;     // cpu bp enable
        g_stGdcParam[i].stGdcParam.stLdCfg.stCpuBp.u16CpuBpLines = 0; // cpu bp current line num

        g_stGdcParam[i].u8LdEn = 1;
        g_stGdcParam[i].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT; // gdc as src
        g_stGdcParam[i].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32Lines64Enable = 0;
        g_stGdcParam[i].stLdParam.stOutLowdelay.u32PlanarNum = 3; // yuv420

        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshAddr = (AR_U32)(Pa & 0xffffffff);
        g_stGdcParam[i].stGdcParam.stMeshCfg.u32MeshStride = ceil((stDevSize.u32Width + 32 - 1) * 1.0 / 32) * 2 * 4; // 32x32 blocking mesh stride
    }

    g_sample_gdc_bf_queue[0] = pfn_ar_queue_create(4, sizeof(VIDEO_FRAME_INFO_S), NULL);
    g_sample_gdc_bf_queue[1] = pfn_ar_queue_create(4, sizeof(VIDEO_FRAME_INFO_S), NULL);

    pthread_t tid;
    pthread_create(&tid, NULL, sample_pthread_gdc_get_stream, NULL);

    int fisrt_start = 1;
    AR_S32 frm_cnt[2] = {0};
    VIDEO_FRAME_INFO_S FrameInfo = {0};
    while (1)
    {
        if (fisrt_start)
        {
            fisrt_start = 0;
            for (int i = 0; i < GDC_RUN_NUM; i++)
            {
                ret = pfn_ar_queue_pop_timeout(g_sample_gdc_bf_queue[i], &FrameInfo, NULL, -1);
                if (ret < 0)
                {
                    assert(0);
                }
                g_stGdcParam[i].stInBuffer.u32IsVB = 1;
                g_stGdcParam[i].stInBuffer.u32Width = FrameInfo.stVFrame.u32Width;
                g_stGdcParam[i].stInBuffer.u32Height = FrameInfo.stVFrame.u32Height;
                g_stGdcParam[i].stInBuffer.astChannels[0].u32Stride = FrameInfo.stVFrame.u32Stride[0];
                g_stGdcParam[i].stInBuffer.astChannels[1].u32Stride = FrameInfo.stVFrame.u32Stride[1];
                g_stGdcParam[i].stInBuffer.astChannels[2].u32Stride = FrameInfo.stVFrame.u32Stride[2];

                // lb_lowdelay stride must 4096
                g_stGdcParam[i].stOutBuffer.u32Width = FrameInfo.stVFrame.u32Width;
                g_stGdcParam[i].stOutBuffer.u32Height = FrameInfo.stVFrame.u32Height;
                g_stGdcParam[i].stInBuffer.astChannels[0].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[0];
                g_stGdcParam[i].stInBuffer.astChannels[0].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[0];
                g_stGdcParam[i].stInBuffer.astChannels[1].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[1];
                g_stGdcParam[i].stInBuffer.astChannels[1].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[1];
                g_stGdcParam[i].stInBuffer.astChannels[2].uptrAddrVirt = FrameInfo.stVFrame.u64VirAddr[2];
                g_stGdcParam[i].stInBuffer.astChannels[2].u32AddrPhy = FrameInfo.stVFrame.u64PhyAddr[2];

                g_stGdcParam[i].stGdcParam.stWarpCfg.u32WarpAddr = (AR_U32)(pu64WarpParaPa[i][frm_cnt[i]++ % (WARP_FILE_NUM - 1)] & 0xffffffff);

                g_stGdcParam[i].stLdParam.stOutLowdelay.enFormat = FrameInfo.stVFrame.enPixelFormat;
                ;
                g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(FrameInfo.stVFrame.u32Width);
                g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);
                g_stGdcParam[i].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(FrameInfo.stVFrame.u32Width / 2);

                s32Ret = pfn_AR_MPI_GDC_ADV_Process(&g_stGdcParam[i]);
                if (s32Ret < 0)
                {
                    printf("AR_MPI_GDC_ADV_Process fail\r\n");
                    pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
                    assert(0);
                }
            }

            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &FrameInfo);
            if (AR_SUCCESS != s32Ret)
            {
                SAMPLE_PRT("vi release frame failed. s32Ret: 0x%x\n", s32Ret);
                return -1;
            }
        }
        sleep(1);
    }

    VoLayer = 0;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoLayer = 0x10;
    s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable layer failed. s32Ret: 0x%x\n", s32Ret);
    }

    VoDev = 0;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }
    VoDev = 1;
    s32Ret = pfn_AR_MPI_VO_Disable(VoDev);
    if (AR_SUCCESS != s32Ret)
    {
        SAMPLE_PRT("vo disable failed. s32Ret: 0x%x\n", s32Ret);
    }

    pfn_AR_MPI_ISP_Exit(ViPipe);
    pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
    pfn_AR_MPI_VI_StopPipe(ViPipe);
    pfn_AR_MPI_VI_DestroyPipe(ViPipe);
    pfn_AR_MPI_VI_DisableDev(ViDev);

    SAMPLE_COMM_SYS_Exit();

    return s32Ret;
}

int main(int argc, char **argv)
{
    if (!LoadBoardSymbols())
    {
        BOARD_LOG_ERROR("load symbol error");
        exit(1);
    }
    pfn_ar_log_init();

    int ret = 0;
    int opt = -1;
    int tc_id = 1;

    if (argc < 2)
    {
        exit(1);
    }

    while ((opt = getopt(argc, argv, "i:a:r:h")) != -1)
    {
        switch (opt)
        {
        case 'i':
            tc_id = atoi(optarg);
            break;
        case 'h':
        default:
            exit(1);
        }
    }

    switch (tc_id)
    {
    default:
        break;
    case 1:
        ret = test1_dp_de();
        break;
    case 2:
        ret = test2_dp_gdc_de_freerun_ld();
        break;
    case 3:
        ret = test3_dp_gdc_de_shadow_ld();
        break;
    case 4:
        ret = test4_dp_gdc_de_shadow_ld_mesh_resize();
        break;
    case 5:
        ret = test5_dp_gdc_de_shadow_ld_warp_process();
        break;
    case 6:
        ret = test6_dual_dp_gdc_de_shadow_ld_warp_process();
        break;
    case 7:
        ret = test7_dp_gdc_de_freerun_ld_warp_process();
        break;
    case 8:
        ret = test8_dp_gdc_de_shadow_ld_warp_process();
        break;
    case 9:
        ret = test9_dual_dp_gdc_de_freerun_ld_warp_process();
        break;
    case 10:
        ret = test10_dual_dp_gdc_de_shadow_ld_warp_process();
        break;
    case 11:
        ret = test11_dp_gdc_de_freerun_ld_warp_cf50_process();
        break;
    case 12:
        ret = test12_dual_dp_gdc_de_shadow_ld_warp_cpubp_process();
        break;
    case 61:
        ret = test61_dual_dp_gdc_de_dp_trigger_gdc();
        break;
    }

    return ret;
}
