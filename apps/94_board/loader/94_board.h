#ifndef __94_BOARD_H__
#define __94_BOARD_H__

#include <ar_comm_video.h>
#include <hal_sys.h>
#include <hal_scaler_api.h>
#include <mpi_scaler_api.h>
#include "ar_common.h"
#include "ar_buffer.h"
#include "ar_comm_sys.h"
#include "ar_comm_vb.h"
#include "ar_comm_isp.h"
#include "ar_comm_vi.h"
#include "ar_comm_vo.h"
#include "ar_comm_venc.h"
#include "ar_comm_vdec.h"
#include "ar_comm_vpss.h"
#include "ar_comm_region.h"
#include "ar_comm_adec.h"
#include "ar_comm_aenc.h"
#include "ar_comm_ai.h"
#include "ar_comm_ao.h"
#include "ar_comm_aio.h"
#include "ar_defines.h"
#include "ar_comm_hdmi.h"
#include "ar_combo_dev.h"

#include "mpi_sys.h"
#include "mpi_vb.h"
#include "mpi_vi.h"
#include "mpi_vo.h"
#include "mpi_venc.h"
#include "mpi_vdec.h"
#include "mpi_vpss.h"
#include "mpi_region.h"
#include "mpi_audio.h"
#include "mpi_isp.h"
#include "mpi_ae.h"
#include "mpi_awb.h"
#include "mpi_ifc_api.h"
#include "ar_math.h"
#include "ar_sns_ctrl.h"
#include "mpi_hdmi.h"
#include "mpi_gdc_api.h"

#define AR_DPRX_EDID_SIZE (256)

#define AR_API_ABORT_ON_ERROR(express, name)                                                               \
    {                                                                                                      \
        AR_S32 Ret;                                                                                        \
        Ret = express;                                                                                     \
        if (Ret != AR_SUCCESS)                                                                             \
        {                                                                                                  \
            BOARD_LOG_ERROR("{} failed at {} : LINE: {} with {:x}!\n", name, __FUNCTION__, __LINE__, Ret); \
            usleep(500);                                                                                   \
            std::abort();                                                                                  \
        }                                                                                                  \
    }

#define AR_ALIGN128(_x) (((_x) + 0x7f) & ~0x7f)

#define COLOR_RGB_RED 0xFF0000
#define COLOR_RGB_GREEN 0x00FF00
#define COLOR_RGB_BLUE 0x0000FF
#define COLOR_RGB_BLACK 0x000000
#define COLOR_RGB_YELLOW 0xFFFF00
#define COLOR_RGB_CYN 0x00ffff
#define COLOR_RGB_WHITE 0xffffff

typedef AR_S32 (*PFN_AR_MPI_GDC_ADV_Query)(AR_GDC_ADV_TRANSFORM_S *pstGdcParams);
typedef AR_S32 (*PFN_AR_MPI_GDC_ADV_Process)(AR_GDC_ADV_TRANSFORM_S *pstParam);
typedef AR_S32 (*PFN_AR_MPI_GDC_ADV_Config)(AR_GDC_ADV_TRANSFORM_S *pstParam);
typedef AR_S32 (*PFN_AR_MPI_GDC_ADV_Start)(AR_GDC_ADV_TRANSFORM_S *pstParam);
typedef AR_S32 (*PFN_AR_MPI_GDC_ADV_Stop)(AR_S32 s32CoreId);
typedef AR_S32 (*PFN_AR_MPI_GDC_ADV_Config_CPUBp_Line)(AR_GDC_ADV_LINE_PARAMS_S *pstLineParam);
typedef AR_S32 (*PFN_AR_MPI_GDC_ADV_Get_FrmPts)(AR_GDC_ADV_FRMPTS_PARAMS_S *pstGdcFrmPtsParams);

typedef AR_VOID (*PFN_AR_MPI_VIN_OpenDev)(VI_DEV_PROP_S *pProp);
typedef AR_VOID (*PFN_AR_MPI_VIN_CloseDev)(VI_DEV_PROP_S *pProp);
typedef void *(*PFN_AR_MPI_VIN_GetSensorObj)(AR_CHAR *sensor, ISP_SNS_OBJ_S **obj);
typedef AR_S32 (*PFN_AR_MPI_VIN_PipeBindSensor)(VI_PIPE ViPipe, ISP_SNS_OBJ_S *p_obj, AR_S32 bus_id);
typedef void (*PFN_AR_MPI_VIN_CloseSensorObj)(void *handle);

typedef AR_S32 (*PFN_AR_MPI_SYS_Init)();
typedef AR_S32 (*PFN_AR_MPI_SYS_Exit)();
typedef AR_VOID *(*PFN_AR_MPI_SYS_Mmap)(AR_U64 u64PhyAddr, AR_U32 u32Size);

typedef AR_S32 (*PFN_AR_MPI_VB_Init)();
typedef AR_S32 (*PFN_AR_MPI_VB_Exit)();
typedef AR_S32 (*PFN_AR_MPI_VB_SetConfig)(const VB_CONFIG_S *pstVbConfig);
typedef AR_S32 (*PFN_AR_MPI_VB_ExitModCommPool)(VB_UID_E enVbUid);
typedef VB_POOL (*PFN_AR_MPI_VB_CreatePool)(VB_POOL_CONFIG_S *pstVbPoolCfg);
typedef VB_BLK (*PFN_AR_MPI_VB_GetBlock)(VB_POOL Pool, AR_U64 u64BlkSize, const AR_CHAR *pcMmzName);
typedef AR_S32 (*PFN_AR_MPI_VB_ReleaseBlock)(VB_BLK Block);
typedef VB_POOL (*PFN_AR_MPI_VB_Handle2PoolId)(VB_BLK Block);
typedef AR_U64 (*PFN_AR_MPI_VB_Handle2PhysAddr)(VB_BLK Block);
typedef VB_BLK (*PFN_AR_MPI_VB_PhysAddr2Handle)(AR_U64 u64PhyAddr);
typedef AR_S32 (*PFN_AR_MPI_VB_MmapPool)(VB_POOL Pool);
typedef AR_S32 (*PFN_AR_MPI_VB_MunmapPool)(VB_POOL Pool);
typedef AR_S32 (*PFN_AR_MPI_VB_GetBlockVirAddr)(VB_POOL Pool, AR_U64 u64PhyAddr, AR_VOID **ppVirAddr);

typedef AR_S32 (*PFN_AR_MPI_VI_SetMipiBindDev)(VI_DEV ViDev, MIPI_DEV MipiDev);
typedef int (*PFN_AR_MPI_VI_SetComboDevAttr)(const STRU_COMBO_DEV_ATTR_T *pstDevAttr);
typedef AR_S32 (*PFN_AR_MPI_VI_SetDevAttr)(VI_DEV ViDev, const VI_DEV_ATTR_S *pstDevAttr);
typedef AR_S32 (*PFN_AR_MPI_VI_SetDevBindPipe)(VI_DEV ViDev, const VI_DEV_BIND_PIPE_S *pstDevBindPipe);
typedef AR_S32 (*PFN_AR_MPI_VI_EnableDev)(VI_DEV ViDev);
typedef AR_S32 (*PFN_AR_MPI_VI_DisableDev)(VI_DEV ViDev);
typedef AR_S32 (*PFN_AR_MPI_VI_EnableChn)(VI_PIPE ViPipe, VI_CHN ViChn);
typedef AR_S32 (*PFN_AR_MPI_VI_DisableChn)(VI_PIPE ViPipe, VI_CHN ViChn);
typedef AR_S32 (*PFN_AR_MPI_VI_SetChnAttr)(VI_PIPE ViPipe, VI_CHN ViChn, const VI_CHN_ATTR_S *pstChnAttr);
typedef AR_S32 (*PFN_AR_MPI_VI_SetChnExtAttr)(VI_PIPE ViPipe, VI_CHN ViChn, const VI_CHN_EXT_ATTR_S *pstChnAttr);
typedef AR_S32 (*PFN_AR_MPI_VI_SetChnCmpAttr)(VI_PIPE ViPipe, VI_CHN ViChn, VI_CH_CF50_CMP_ATTR_T *pstChnCmpAttr);
typedef AR_S32 (*PFN_AR_MPI_VI_GetPipeExtAttr)(VI_PIPE ViPipe, VI_PIPE_EXT_ATTR_S *pstPipeAttr);
typedef AR_S32 (*PFN_AR_MPI_VI_SetPipeExtAttr)(VI_PIPE ViPipe, const VI_PIPE_EXT_ATTR_S *pstPipeAttr);
typedef AR_S32 (*PFN_AR_MPI_VI_CreatePipe)(VI_PIPE ViPipe, const VI_PIPE_ATTR_S *pstPipeAttr);
typedef AR_S32 (*PFN_AR_MPI_VI_DestroyPipe)(VI_PIPE ViPipe);
typedef AR_S32 (*PFN_AR_MPI_VI_StartPipe)(VI_PIPE ViPipe);
typedef AR_S32 (*PFN_AR_MPI_VI_StopPipe)(VI_PIPE ViPipe);
typedef AR_S32 (*PFN_AR_MPI_VI_GetChnFrame)(VI_PIPE ViPipe, VI_CHN ViChn, VIDEO_FRAME_INFO_S *pstFrameInfo, AR_S32 s32MilliSec);
typedef AR_S32 (*PFN_AR_MPI_VI_ReleaseChnFrame)(VI_PIPE ViPipe, VI_CHN ViChn, const VIDEO_FRAME_INFO_S *pstFrameInfo);

typedef AR_S32 (*PFN_AR_MPI_VI_GetHightPricisionPipeFPS)(VI_PIPE ViPipe, AR_S32 ViChn, AR_FLOAT *fps,int flag);
typedef AR_S32 (*PFN_AR_MPI_VI_ReShapeChExt)(VI_PIPE ViPipe, VI_CHN ViChn, SIZE_S stSize,RECT_S stCrop,RECT_S stPostCrop);

typedef AR_S32 (*PFN_AR_MPI_VO_Disable)(VO_DEV VoDev);
typedef AR_S32 (*PFN_AR_MPI_VO_DisableVideoLayer)(VO_LAYER VoLayer);
typedef AR_S32 (*PFN_AR_MPI_VO_Enable)(VO_DEV VoDev);
typedef AR_S32 (*PFN_AR_MPI_VO_EnableVideoLayer)(VO_LAYER VoLayer);
typedef AR_S32 (*PFN_AR_MPI_VO_SetVideoLayerAttr)(VO_LAYER VoLayer, const VO_VIDEO_LAYER_ATTR_S *pstLayerAttr);
typedef AR_S32 (*PFN_AR_MPI_VO_SetVideoLayerPos)(VO_LAYER VoLayer, POINT_S *pos);
typedef AR_S32 (*PFN_AR_MPI_VO_EnableChn)(VO_LAYER VoLayer, VO_CHN VoChn);
typedef AR_S32 (*PFN_AR_MPI_VO_DisableChn)(VO_LAYER VoLayer, VO_CHN VoChn);
typedef AR_S32 (*PFN_AR_MPI_VO_SetChnAttr)(VO_LAYER VoLayer, VO_CHN VoChn, const VO_CHN_ATTR_S *pstChnAttr);
typedef AR_S32 (*PFN_AR_MPI_VO_SetUserIntfSyncInfo)(VO_DEV VoDev, VO_USER_INTFSYNC_INFO_S *pstUserInfo);
typedef AR_S32 (*PFN_AR_MPI_VO_SetIrqAttr)(VO_DEV VoDev, const VO_IRQ_ATTR_S *pstIrqAttr);
typedef AR_S32 (*PFN_AR_MPI_VO_SubscribeEnable)(VO_DEV VoDev, const VO_SUBSCRIBE_ATTR_S *pstSubAttr);
typedef AR_S32 (*PFN_AR_MPI_VO_SubscribeDisable)(VO_DEV VoDev);

typedef AR_S32 (*PFN_AR_MPI_VO_SetPubAttr)(VO_DEV VoDev, const VO_PUB_ATTR_S *pstPubAttr);
typedef AR_S32 (*PFN_AR_MPI_VO_SetDevFrameRate)(VO_DEV VoDev, AR_FLOAT u32FrameRate);
typedef AR_S32 (*PFN_AR_MPI_VO_SetStartAttr)(VO_DEV VoDev, const VO_START_ATTR_S *pstStartAttr);
typedef AR_S32 (*PFN_AR_MPI_VO_SetLowdelayAttr)(VO_DEV VoDev, const VO_LOWDELAY_ATTR_S *pstLowdelayAttr);
typedef AR_S32 (*PFN_AR_MPI_VO_Dsi_SetAttr)(VO_DEV VoDev, VO_DSI_ATTR_S *pstAttr);
typedef AR_S32 (*PFN_AR_MPI_VO_Dsi_Enable)(VO_DEV VoDev);
typedef AR_S32 (*PFN_AR_MPI_VO_SendFrame)(VO_LAYER VoLayer, VO_CHN VoChn, VIDEO_FRAME_INFO_S *pstVFrame, AR_S32 s32MilliSec);
 
typedef AR_S32 (*PFN_AR_MPI_VO_EnableLineBuffer)(VO_DEV VoDev);
typedef AR_S32 (*PFN_AR_MPI_VO_DisableLineBuffer)(VO_DEV VoDev);
typedef AR_S32 (*PFN_AR_MPI_VO_ResetLineBufferPrs)(VO_DEV VoDev);


typedef AR_S32 (*PFN_AR_MPI_ISP_Exit)(VI_PIPE ViPipe);
typedef AR_S32 (*PFN_AR_MPI_ISP_SetPubAttr)(VI_PIPE ViPipe, const ISP_PUB_ATTR_S *pstPubAttr);
typedef AR_S32 (*PFN_AR_MPI_ISP_Init)(VI_PIPE ViPipe);
typedef AR_S32 (*PFN_AR_MPI_ISP_Run)(VI_PIPE ViPipe);
typedef AR_S32 (*PFN_AR_MPI_ISP_MemInit)(VI_PIPE ViPipe);

typedef AR_S32 (*PFN_ar_hal_dp_rx_set_edid)(AR_U8 (*edid)[AR_DPRX_EDID_SIZE]);
typedef AR_S32 (*PFN_ar_hal_dp_rx_get_hpd_status)(AR_U32 *hpd_status);
typedef AR_S32 (*PFN_ar_hal_dp_rx_set_hpd_status)(AR_U32 hpd_status);
typedef AR_S32 (*PFN_ar_hal_sys_mmz_alloc)(AR_U64 *pu64_phy_addr, AR_VOID **p_vir_addr,
                                           const AR_CHAR *pstr_mmb, const AR_CHAR *pstr_zone, AR_U32 u32_len);
typedef AR_S32 (*PFN_ar_hal_sys_mmz_alloc_cached)(AR_U64 *pu64_phy_addr, AR_VOID **p_vir_addr,
                                           const AR_CHAR *pstr_mmb, const AR_CHAR *pstr_zone, AR_U32 u32_len);
typedef AR_S32 (*PFN_ar_hal_sys_mmz_flush_cache)(AR_U64 u64_phy_addr, AR_VOID *p_vir_addr, AR_U32 u32_size);
typedef AR_S32 (*PFN_ar_hal_sys_mmz_free)(AR_U64 u64_phy_addr, AR_VOID *p_vir_addr);

typedef void (*PFN_ar_log_func)(uint8_t level, int tag_id, const char *file, const char *func,
						const int line, const char *format, ...);
typedef void (*PFN_ar_log_func_raw)(const char *format, ...);
typedef int (*PFN_ar_log_init)();

typedef int (*PFN_ar_memset)(void* dest, size_t destMax, int c, size_t count);

typedef ar_queue_id_t (*PFN_ar_queue_create)(uint32_t count, uint32_t size, ar_queue_attr_t* attr);
typedef ar_status_t   (*PFN_ar_queue_pop_timeout)(ar_queue_id_t id, ar_data_t element, void* padding, uint32_t timeout);
typedef ar_status_t   (*PFN_ar_queue_push_force)(ar_queue_id_t id, ar_const_data_t element, ar_data_t old_element);

#endif /* End of #ifndef __94_BOARD_H__*/
