#pragma once
#include <framework/util/log.h>

#include <framework/util/singleton.h>

class Logger : public framework::util::Singleton<Logger>
{
public:
    bool IsLogAllLevel() const { return log_all_level_; }
    void SetLogAllLevel(bool value)
    {
        log_all_level_ = value;
        if (log_all_level_)
        {
            GetLogger()->set_level(framework::util::log::LogLevel::trace);
            GetLogger()->flush_on(framework::util::log::LogLevel::trace);
        }
    }
    framework::util::log::LoggerPtr GetLogger() { return framework::util::log::Logger::defaultLogger(section_); }
    void SetSection(const std::string &section) { section_ = section; }

private:
    bool log_all_level_{false};
    std::string section_{"AR94"};
};

#define BOARD_LOG_TRACE(format, ...)                      \
    if (Logger::GetInstance()->IsLogAllLevel())           \
        Logger::GetInstance()->GetLogger()->trace(format, \
                                                  ##__VA_ARGS__);
#define BOARD_LOG_DEBUG(format, ...)                      \
    if (Logger::GetInstance()->IsLogAllLevel())           \
        Logger::GetInstance()->GetLogger()->debug(format, \
                                                  ##__VA_ARGS__);
#define BOARD_LOG_TRACE2(format1, format2, ...)                                   \
    if (Logger::GetInstance()->IsLogAllLevel())                                   \
    {                                                                             \
        std::string format = format1;                                             \
        format += format2;                                                        \
        Logger::GetInstance()->GetLogger()->trace(format.c_str(), ##__VA_ARGS__); \
    }

#define BOARD_LOG_INFO(format, ...) \
    Logger::GetInstance()->GetLogger()->info(format, ##__VA_ARGS__);
#define BOARD_LOG_WARN(format, ...) \
    Logger::GetInstance()->GetLogger()->warn(format, ##__VA_ARGS__);
#define BOARD_LOG_ERROR(format, ...) \
    Logger::GetInstance()->GetLogger()->error(format, ##__VA_ARGS__);
#define BOARD_LOG_FATAL(format, ...) \
    Logger::GetInstance()->GetLogger()->fatal(format, ##__VA_ARGS__);
