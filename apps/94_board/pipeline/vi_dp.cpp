#include <94_board.h>

#include <pipeline/vi_dp.h>
#include <log.h>

// TODO: board_context now only supports "DP-in" case.
// "File-in" also need to be implemented


extern PFN_AR_MPI_VIN_OpenDev pfn_AR_MPI_VIN_OpenDev;
extern PFN_AR_MPI_VIN_PipeBindSensor pfn_AR_MPI_VIN_PipeBindSensor;

extern PFN_AR_MPI_SYS_Init pfn_AR_MPI_SYS_Init;
extern PFN_AR_MPI_SYS_Exit pfn_AR_MPI_SYS_Exit;

extern PFN_AR_MPI_VB_Init pfn_AR_MPI_VB_Init;
extern PFN_AR_MPI_VB_Exit pfn_AR_MPI_VB_Exit;
extern PFN_AR_MPI_VB_SetConfig pfn_AR_MPI_VB_SetConfig;

extern PFN_AR_MPI_ISP_Init pfn_AR_MPI_ISP_Init;
extern PFN_AR_MPI_ISP_Run pfn_AR_MPI_ISP_Run;
extern PFN_AR_MPI_ISP_SetPubAttr pfn_AR_MPI_ISP_SetPubAttr;
extern PFN_AR_MPI_ISP_MemInit pfn_AR_MPI_ISP_MemInit;

extern PFN_AR_MPI_VI_EnableChn pfn_AR_MPI_VI_EnableChn;
extern PFN_AR_MPI_VI_SetChnAttr pfn_AR_MPI_VI_SetChnAttr;
extern PFN_AR_MPI_VI_SetMipiBindDev pfn_AR_MPI_VI_SetMipiBindDev;
extern PFN_AR_MPI_VI_SetComboDevAttr pfn_AR_MPI_VI_SetComboDevAttr;
extern PFN_AR_MPI_VI_EnableDev pfn_AR_MPI_VI_EnableDev;
extern PFN_AR_MPI_VI_SetDevAttr pfn_AR_MPI_VI_SetDevAttr;
extern PFN_AR_MPI_VI_CreatePipe pfn_AR_MPI_VI_CreatePipe;
extern PFN_AR_MPI_VI_StartPipe pfn_AR_MPI_VI_StartPipe;
extern PFN_AR_MPI_VI_SetDevBindPipe pfn_AR_MPI_VI_SetDevBindPipe;
extern PFN_AR_MPI_VI_GetPipeExtAttr pfn_AR_MPI_VI_GetPipeExtAttr;
extern PFN_AR_MPI_VI_SetPipeExtAttr pfn_AR_MPI_VI_SetPipeExtAttr;

extern PFN_ar_hal_dp_rx_set_edid pfn_ar_hal_dp_rx_set_edid;
extern PFN_ar_hal_dp_rx_get_hpd_status pfn_ar_hal_dp_rx_get_hpd_status;
extern PFN_ar_hal_dp_rx_set_hpd_status pfn_ar_hal_dp_rx_set_hpd_status;
/******************************************************************************
* function : vb init & MPI system init
******************************************************************************/
static AR_S32 SAMPLE_COMM_SYS_Init(VB_CONFIG_S* pstVbConfig)
{
    AR_S32 s32Ret = AR_FAILURE;

    pfn_AR_MPI_SYS_Exit();
    pfn_AR_MPI_VB_Exit();

    if (NULL == pstVbConfig)
    {
        BOARD_LOG_ERROR("input parameter is null, it is invaild!");
        return AR_FAILURE;
    }

    s32Ret = pfn_AR_MPI_VB_SetConfig(pstVbConfig);

    if (AR_SUCCESS != s32Ret)
    {
        BOARD_LOG_ERROR("AR_MPI_VB_SetConf failed!");
        return AR_FAILURE;
    }

    s32Ret = pfn_AR_MPI_VB_Init();

    if (AR_SUCCESS != s32Ret)
    {
        BOARD_LOG_ERROR("AR_MPI_VB_Init failed!");
        return AR_FAILURE;
    }

    s32Ret = pfn_AR_MPI_SYS_Init();

    if (AR_SUCCESS != s32Ret)
    {
        BOARD_LOG_ERROR("AR_MPI_SYS_Init failed!");
        pfn_AR_MPI_VB_Exit();
        return AR_FAILURE;
    }

    return AR_SUCCESS;
}

static AR_U8 edid_groups[][AR_DPRX_EDID_SIZE] = {

    //1080p 30
    {
        0x00,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x36,0x47,0x32,0x31,0x00,0x88,0x88,0x88,
        0x13,0x21,0x01,0x03,0x80,0x0C,0x07,0x78,0x0A,0x9E,0xE0,0xA7,0x54,0x48,0x99,0x23,
        0x10,0x50,0x54,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,
        0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x1D,0x80,0x18,0x71,0x38,0x2D,0x40,0x58,0x2C,
        0x45,0x00,0x80,0x38,0x74,0x00,0x00,0x06,0x00,0x00,0x00,0xFE,0x00,0x41,0x72,0x74,
        0x6F,0x73,0x79,0x6E,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,0xFD,0x00,0x32,
        0x82,0x14,0x3C,0x3C,0x00,0x0A,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,0xFC,
        0x00,0x6E,0x72,0x65,0x61,0x6C,0x20,0x61,0x69,0x72,0x0A,0x20,0x20,0x20,0x00,0xB0,
    },
    //1080p 60
    {
        0x00,0xff,0xff,0xff,0xff,0xff,0xff,0x00,0x26,0x85,0x02,0x66,0x21,0x60,0x00,0x00,
        0x00,0x17,0x01,0x03,0x80,0x73,0x41,0x78,0x2a,0x7c,0x11,0x9e,0x59,0x47,0x9b,0x27,
        0x10,0x50,0x54,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,
        0x01,0x01,0x01,0x01,0x01,0x01,0x02,0x3a,0x80,0x18,0x71,0x38,0x2d,0x40,0x58,0x2c,
        0x45,0x00,0x10,0x09,0x00,0x00,0x00,0x1e,0x00,0x00,0x00,0xfc,0x00,0x0a,0x20,0x20,
        0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,0xfc,0x00,0x49,
        0x54,0x45,0x36,0x38,0x30,0x32,0x0a,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,0xfd,
        0x00,0x30,0x7a,0x0f,0x50,0x10,0x00,0x0a,0x20,0x20,0x20,0x20,0x20,0x20,0x01,0x87,

        0x02,0x03,0x14,0x41,0x41,0x90,0x23,0x09,0x07,0x07,0x83,0x01,0x00,0x00,0x65,0x03,
        0x0c,0x00,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x93,
    },
    //3840*1080 15
    {
        0x00,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x36,0x47,0x32,0x31,0x00,0x88,0x88,0x88,
        0x13,0x21,0x01,0x03,0x80,0x0C,0x07,0x78,0x0A,0x9E,0xE0,0xA7,0x54,0x48,0x99,0x23,
        0x10,0x50,0x54,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,
        0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x1D,0x00,0x30,0xF2,0x38,0x2D,0x40,0x40,0x20,
        0x95,0x00,0x00,0x38,0xF4,0x00,0x00,0x06,0x00,0x00,0x00,0xFE,0x00,0x41,0x72,0x74,
        0x6F,0x73,0x79,0x6E,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,0xFD,0x00,0x32,
        0x82,0x14,0x3C,0x3C,0x00,0x0A,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,0xFC,
        0x00,0x6E,0x72,0x65,0x61,0x6C,0x20,0x61,0x69,0x72,0x0A,0x20,0x20,0x20,0x00,0x6B,
    },
    //4k 30
    {   
    	0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x05, 0xD7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    	0xFF, 0x22, 0x01, 0x04, 0xA5, 0x32, 0x1F, 0x78, 0x07, 0xEE, 0x95, 0xA3, 0x54, 0x4C, 0x99, 0x26,
    	0x0F, 0x50, 0x54, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
    	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0x74, 0x00, 0x30, 0xF2, 0x70, 0x5A, 0x80, 0xB0, 0x58,
    	0x8A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xFD, 0x00, 0x17, 0xF0, 0x0F,
    	0xFF, 0x1E, 0x00, 0x0A, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x55,
    	0x48, 0x44, 0x54, 0x56, 0x20, 0x32, 0x31, 0x36, 0x30, 0x70, 0x0A, 0x20, 0x00, 0x00, 0x00, 0x00,
    	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xAE,
    	0x02, 0x03, 0x0A, 0x40, 0x23, 0x09, 0x06, 0x07, 0x41, 0xDF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58
    }
};

static AR_VOID SAMPLE_AR_MPI_VIN_OpenDev_Local(AR_S32 mode, AR_S32 hdr_freq, AR_S32 vif_freq, AR_S32 isp_freq, AR_S32 mipi_freq)
{
    VI_DEV_PROP_S Prop;
    memset(&Prop, 0, sizeof(VI_DEV_PROP_S));

    // for no need here now, move to sys init

    if (mode == 0)
    {
        Prop.cam_mode = VIN_CAMERA_NORMAL;
    }
    else if (mode == 1)
    {
        Prop.cam_mode = VIN_CAMERA_OFFLINE;
    }
    else if (mode == 2)
    {
        Prop.cam_mode = VIN_CMAERA_MULTI_MODE;
    }
    else
    {
        Prop.cam_mode = VIN_CAMERA_OFFLINE;
    }
    // cfg the fre
    Prop.hdr_fre_mod = 1;
    Prop.hdr_fre_hz = hdr_freq;
    Prop.vif_fre_mod = 1;
    Prop.vif_fre_hz = vif_freq;
    Prop.isp_fre_mod = 1;
    Prop.isp_fre_hz = isp_freq;
    Prop.vif_fre_mod = 1;
    Prop.vif_fre_hz = mipi_freq;
    Prop.cvisp0_fre_hz = isp_freq;
    Prop.cvisp1_fre_hz = isp_freq;
    Prop.dpvif_fre_hz = vif_freq;
    Prop.cvvif0_fre_hz = isp_freq;
    pfn_AR_MPI_VIN_OpenDev(&Prop);
    return;
}

extern SRTU_SENSOR_DEFAULT_ATTR_T default_attr;
extern ISP_SNS_OBJ_S *p_obj;

namespace app
{

    bool VIDP::Init(AR94::BoardConfig &config)
    {
        if (!initVIConfig(config))
        {
            return false;
        }
        initFrameConfig(config);
        return true;
    }

    bool VIDP::Start(AR94::BoardConfig &config)
    {
        if (!startVI(config))
        {
            return false;
        }
        return true;
    }

    bool VIDP::Stop(AR94::BoardConfig &config)
    {
        config.Stop(6);
        return true;
    }

    void VIDP::initFrameConfig(AR94::BoardConfig &config) {}

    bool VIDP::initVIConfig(AR94::BoardConfig &config)
    {
        BOARD_LOG_INFO("VIDP INITIALIZING!");
        /* config vi */
        /**
        * XREAL_QUESTION 6
        * isp_fre,vif_fre,pcs_fre, cam_mode的具体含义
        ==》isp_fre isp的像素时钟频率
        vif_fre，vif的像素时钟频率，pcs_fre mipi pcs 的像素时钟频率。
        cam_mode 0 行存模式，指的是vif和isp使用linebuffer 模式。 注：XREAL用的就是cam_mode = 0: 行存模式
        1：ddr 模式，指的是vif和isp 之间使用ddr 模式，所谓ddr 模式，
        指的是数据经过vif 后写入ddr，然后isp从ddr 读取，
        linebufer 模式指的是vif 不写ddr，直接送入isp
        2：多camera模式，isp分时复用
        */

        if (!initVBConfig(config))
        {
            BOARD_LOG_ERROR("initVIConfig failed when init VBConfig.");
            return false;
        }
        BOARD_LOG_INFO("{} {} stSize.h:{} w:{} u32MaxPoolCnt:{}", __FUNCTION__, __LINE__, config.stSize.u32Height, config.stSize.u32Width, config.stVbConf.u32MaxPoolCnt);

        config.isp_fre = 300000000;
        config.vif_fre = 300000000;
        config.pcs_fre = 100000000;

        SAMPLE_AR_MPI_VIN_OpenDev_Local(1, config.isp_fre, config.vif_fre, config.isp_fre, config.pcs_fre);

        BOARD_LOG_INFO("VIDP INITIALIZED!");
        return true;
    }

    bool VIDP::initVBConfig(AR94::BoardConfig &config)
    {
        AR_S32 s32Ret = AR_SUCCESS;
        /*config vb*/
        /**
         * XREAL_QUESTION 5:
         * 解释结构体 VB_CONFIG_S
         * astCommPool, u64BlkSize, u32BlkCnt分别是什么
         ==》astCommPool：公共内存池的数组；
            u64BlkSize：该公共池每一块buf的大小；
            u32BlkCnt：该公共池buf的数量。
            获取公共池的buf可参考AR_MPI_VB_GetBlock API的说明。
            mpi_vb.h 中的说明：
            \brief      用户态获取一个缓存块.
            \param[in]  pool_id 缓存池ID 号，取值范围：[0, VB_MAX_POOLS).
            \param[in]  u64_blk_size 缓存块大小，取值范围：数据类型全范围，以byte为单位.
            \param[in]  pstr_mmz_name 缓存池所在DDR 的名字.
            \retvall    非VB_INVALID_HANDLE 有效的缓存块句柄, VB_INVALID_HANDLE 获取缓存块失败，
            \remarks    用户可以在创建一个缓存池之后，调用本接口从该缓存池中获取一个缓存块；即
                        将第1个参数pool_id设置为创建的缓存池ID；第2个参数u64_blk_size 须小于或等
                        于创建该缓存池时指定的缓存块大小。从指定缓存池获取缓存块时，参数
                        pstr_mmz_name 无效
            \remarks    如果用户需要从任意一个公共缓存池中获取一块指定大小的缓存块，则可以将第1
                        个参数pool_id设置为无效ID 号（VB_INVALID_POOLID），将第2个参数
                        u64_blk_size 设置为需要的缓存块大小，并指定要从哪个DDR上的公共缓存池获取
                        缓存块。如果指定的DDR上并没有公共缓存池，那么将获取不到缓存块。如果
                        pstr_mmz_name 等于NULL，则表示在没有命名的DDR上的公共缓存池获取缓存
                        块
        */
        memset(&config.stVbConf, 0, sizeof(VB_CONFIG_S));
        config.stVbConf.u32MaxPoolCnt = 2;
        AR_U32 u32BlkSize;
        u32BlkSize = COMMON_GetPicBufferSize(config.stDevSize.u32Width, config.stDevSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_420, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
        config.stVbConf.astCommPool[0].u64BlkSize = u32BlkSize;
        config.stVbConf.astCommPool[0].u32BlkCnt = 10;
        // XXXX: config for astCommPool[1]
        u32BlkSize = COMMON_GetPicBufferSize(config.stSize.u32Width, config.stSize.u32Height, PIXEL_FORMAT_YVU_PLANAR_444, DATA_BITWIDTH_8, COMPRESS_MODE_NONE, 512);
        config.stVbConf.astCommPool[1].u64BlkSize = u32BlkSize;
        config.stVbConf.astCommPool[1].u32BlkCnt = 10;

        s32Ret = SAMPLE_COMM_SYS_Init(&config.stVbConf);
        if (AR_SUCCESS != s32Ret)
        {
            BOARD_LOG_ERROR("system init failed with {}!", s32Ret);
            return false;
        }
        BOARD_LOG_INFO("BlkCnt: {}", config.stVbConf.astCommPool[0].u32BlkCnt);
        return true;
    }

    /**
     * XREAL_QUESTION 12:
     * start vi 步骤当中的每一行都需要解释一下：
    ==》 参考mpi_vin.h头文件说明
    @brief 通过 MIPI Rx等接口接收视频数据。
    将接收到的数据存入到指定的内存区域，在此过程中，VI 可以对接收到的原始视频图像数据进行处理，实现视频数据的采集。

    vin的启动流程api调用如下
    AR_MPI_VI_SetMipiBindDev， 把具体的接口索引,比如mipi 或者dvp 和dev 号绑定，这样dev 号就代表了这些接口，也代表了连接到接口的具体的hw，比如sensor等
    AR_MPI_VI_SetComboDevAttr  配置具体接口及和接口连接的hw的一些属性，比如mipi 属性，电源，时钟等,AR_MPI_VI_SetComboDevAttr中的devno 正是MipiDev
    AR_MPI_VI_SetDevAttr       进一步配置设备的属性，也就是配置mipi 或者dvp的属性
    AR_MPI_VI_EnableDev        使能设备
    AR_MPI_VI_SetDevBindPipe   把设备和pipe 关联起来，可以关联多个pipe,这里关联的pipe 数目和pipe 号，决定着后续pipe api操作使用的pipe 号。也决定着后续可以操作的
                               所有的pipe

    以下的操作针对每个pipe，pipe 由AR_MPI_VI_SetDevBindPipe 传入
    AR_MPI_VI_CreatePipe       把AR_MPI_VI_SetDevBindPipe 关联的所有pipe 创建出来，并用pipe属性进行配置
    AR_MPI_VI_SetPipeVCNumber AR_MPI_VI_SetPipeDTNumber 如果存在虚拟通道的情况配置每个pipe使用的虚拟通道号和数据类型
    AR_MPI_VI_StartPipe        启动每个pipe
    AR_MPI_VI_SetChnAttr AR_MPI_VI_EnableChn 配置每个pipe 下的通道并使能，每个pipe 可能不止1个通道
    AR_MPI_ISP_SensorRegCallBack AR_MPI_AE_SensorRegCallBack AR_MPI_AWB_SensorRegCallBack 向pipe 注册sensor aec awb 注册回到，关联驱动的操作函数比如sensor的分辨率设置，
    曝光等。 完成pipe 和驱动的绑定
    AR_MPI_ISP_RegisterNotifyCallBack 初始化isp 之前，可以注册一些isp通知，比如aec update的回调。
    AR_MPI_ISP_MemInit
    AR_MPI_ISP_SetPubAttr 设置公共属性，包括sensor的wh 等
    AR_MPI_VI_SetPipeExtAttr 这里可以设置一些通道的扩展属性，主要修改驱动的一些参数，比如hmax，vmax，效果参数等，这里修改后，驱动的默认值会被置换掉。
    AR_MPI_ISP_Init       初始化isp
    AR_MPI_ISP_Run        启动isp，接下来可以通过api 取帧，或通过其他api 设置效果参数等，需要注意的是，isp 相关api 必须调用，不管数据是否真正的过isp。

    AR_MPI_VI_SetChnAttr AR_MPI_VI_EnableChn，这里可以继续使能上面没有使能的通道

    通道和isp 启动到这里，一些动态设置属性的api 可以使用了。典型的AR_MPI_VI_ReShapeCh，动态调整分辨率，也可以成对的使用AR_MPI_VI_PauseChn 和 AR_MPI_VI_RecoverChn，
    AR_MPI_VI_PauseChn 和 AR_MPI_VI_RecoverChn 这两个api 提供了快速的关闭和开启通道的方法。
    退出流程
    AR_MPI_VI_DisableChn 关闭AR_MPI_ISP_Run 启动后使能的通道
    AR_MPI_ISP_Exit 退出isp
    AR_MPI_ISP_SensorUnRegCallBack AR_MPI_AE_SensorUnRegCallBack AR_MPI_AWB_SensorUnRegCallBack AR_MPI_ISP_UnRegisterNotifyCallBack
    AR_MPI_VI_DisableChn 关闭AR_MPI_ISP_Run 启动前使能的通道
    AR_MPI_VI_StopPipe
    AR_MPI_VI_DestroyPipe
    AR_MPI_VI_DisableDev
    * Dev的语义
* Pipe的语义
    * Channel的语义
    ==》
                --------------------|------> chn0
                |		管道        |
        dev ----|    MIPI  VIF  ISP |------> chn1
                |					|
                --------------------|------> chn2 (raw)
*/
    bool VIDP::startVI(AR94::BoardConfig &config)
    {
        BOARD_LOG_INFO("VIDP STARTINIG!");
        AR_S32 s32Ret = AR_SUCCESS;
        /*start vi*/
        pfn_AR_MPI_VI_SetMipiBindDev(config.ViDev, config.mipi_index);
        default_attr.stComboAttr.devno = config.mipi_index;
        pfn_AR_MPI_VI_SetComboDevAttr(&default_attr.stComboAttr);
        pfn_AR_MPI_VI_SetDevAttr(config.ViDev, &default_attr.stDevAttr);
        pfn_AR_MPI_VI_EnableDev(config.ViDev);
        VI_DEV_BIND_PIPE_S stDevBindPipe;
        stDevBindPipe.u32Num = 1;
        stDevBindPipe.PipeId[0] = config.ViPipe;
        pfn_AR_MPI_VI_SetDevBindPipe(config.ViDev, &stDevBindPipe);
        pfn_AR_MPI_VI_CreatePipe(config.ViPipe, &default_attr.stPipeAttr);
        pfn_AR_MPI_VI_StartPipe(config.ViPipe);

        default_attr.stChnAttr.enPixelFormat = PIXEL_FORMAT_YVU_PLANAR_420;
        // default_attr.stChnAttr.stSize.u32Width = (AR_U32)(config.stDevSize.u32Width * DebugManager::GetInstance()->debug_config.vidp_src_scale_factor);
        // default_attr.stChnAttr.stSize.u32Height = (AR_U32)(config.stDevSize.u32Height * DebugManager::GetInstance()->debug_config.vidp_src_scale_factor);
        default_attr.stChnAttr.stSize = config.stSize_ch;
        default_attr.stChnAttr.u32BufCount = 8;

        pfn_AR_MPI_VI_SetChnAttr(config.ViPipe, config.ViChn, &default_attr.stChnAttr);
        pfn_AR_MPI_VI_EnableChn(config.ViPipe, config.ViChn);

        pfn_AR_MPI_VIN_PipeBindSensor(config.ViPipe, p_obj, config.s8I2cDev);

        pfn_AR_MPI_ISP_MemInit(config.ViPipe);
        default_attr.stPubAttr.u8SnsMode = config.sensor_mode;
        default_attr.stPubAttr.stTiming.hblank=280;
    	default_attr.stPubAttr.stTiming.vblank=45;
        pfn_AR_MPI_ISP_SetPubAttr(config.ViPipe, &default_attr.stPubAttr);
        VI_PIPE_EXT_ATTR_S stPipeAttr;
        pfn_AR_MPI_VI_GetPipeExtAttr(config.ViPipe, &stPipeAttr);
        stPipeAttr.bFoucs = (AR_BOOL)0;
        pfn_AR_MPI_VI_SetPipeExtAttr(config.ViPipe, &stPipeAttr);

//        pfn_ar_hal_dp_rx_set_edid(&edid_groups[1]); //1080p60
//        unsigned int hpd_status = 0;
//        if (pfn_ar_hal_dp_rx_get_hpd_status(&hpd_status) < 0) {
//            BOARD_LOG_ERROR("get hpd_status failed!");
//            return -1;
//        }
//        if (hpd_status) {
//            int ret = pfn_ar_hal_dp_rx_set_hpd_status(0);
//            if (ret)
//                BOARD_LOG_ERROR("ar_hal_dp_rx_set_hpd_status failed {:x}", ret);
//
//            ret = pfn_ar_hal_dp_rx_set_hpd_status(1);
//            if (ret)
//                BOARD_LOG_ERROR("ar_hal_dp_rx_set_hpd_status failed {:x}", ret);
//        }

        pfn_AR_MPI_ISP_Init(config.ViPipe);
        pfn_AR_MPI_ISP_Run(config.ViPipe);
        BOARD_LOG_INFO("VIDP STARTED!");
        return true;
    }
}