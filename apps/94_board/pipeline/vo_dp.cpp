#include <94_board.h>

#include <mpi_vo.h>
#include <pipeline/vo_dp.h>
#include <log.h>

extern PFN_AR_MPI_SYS_Exit pfn_AR_MPI_SYS_Exit;
extern PFN_AR_MPI_VB_Exit pfn_AR_MPI_VB_Exit;
extern PFN_AR_MPI_VO_Enable pfn_AR_MPI_VO_Enable;
extern PFN_AR_MPI_VO_EnableVideoLayer pfn_AR_MPI_VO_EnableVideoLayer;
extern PFN_AR_MPI_VO_SetVideoLayerAttr pfn_AR_MPI_VO_SetVideoLayerAttr;
extern PFN_AR_MPI_VO_SetIrqAttr pfn_AR_MPI_VO_SetIrqAttr;
extern PFN_AR_MPI_VO_SubscribeEnable pfn_AR_MPI_VO_SubscribeEnable;
extern PFN_AR_MPI_VO_SetPubAttr pfn_AR_MPI_VO_SetPubAttr;
extern PFN_AR_MPI_VO_SetDevFrameRate pfn_AR_MPI_VO_SetDevFrameRate;
extern PFN_AR_MPI_VO_SetStartAttr pfn_AR_MPI_VO_SetStartAttr;
extern PFN_AR_MPI_VO_SetLowdelayAttr pfn_AR_MPI_VO_SetLowdelayAttr;
extern PFN_AR_MPI_VO_Dsi_SetAttr pfn_AR_MPI_VO_Dsi_SetAttr;
extern PFN_AR_MPI_VO_Dsi_Enable pfn_AR_MPI_VO_Dsi_Enable;

extern SRTU_SENSOR_DEFAULT_ATTR_T default_attr;

static AR_VOID SAMPLE_VOU_SYS_Exit(void)
{
    pfn_AR_MPI_SYS_Exit();
    pfn_AR_MPI_VB_Exit();
}

static VO_DSI_ATTR_S pstDsiCfg_60Hz =
    {
        .u32Lane = {1, 1, 1, 1},
        .u32BitsPerPixel = 24,
        .stSyncInfo = {
            .u32Hsa = 60,
            .u32Hbp = 200,
            .u32Hact = 1920,
            .u32Hfp = 120,

            .u32Vsa = 2,
            .u32Vbp = 14,
            .u32Vact = 1080,
            .u32Vfp = 16},
        .fDphyClkMhz = 920.736,
};

static VO_DSI_ATTR_S pstDsiCfg_90Hz =
    {
        .u32Lane = {1, 1, 1, 1},
        .u32BitsPerPixel = 24,
        .stSyncInfo = {
            .u32Hsa = 60,
            .u32Hbp = 200,
            .u32Hact = 1920,
            .u32Hfp = 120,

            .u32Vsa = 2,
            .u32Vbp = 14,
            .u32Vact = 1080,
            .u32Vfp = 16},
        .fDphyClkMhz = 1381.104,
};

static ENMU_SYS_HARDWARE_SRC VO_LOWDEALY_SRC[2] = {AR_SYS_HARDWARE_SRC_GDC0, AR_SYS_HARDWARE_SRC_GDC1};

static AR_VOID SAMPLE_VO_GetUserPubBaseAttr(VO_PUB_ATTR_S *pstPubAttr)
{
    pstPubAttr->u32BgColor = COLOR_RGB_BLUE;
    pstPubAttr->enIntfSync = VO_OUTPUT_USER;
    pstPubAttr->stSyncInfo.bSynm = (AR_BOOL)0;
    pstPubAttr->stSyncInfo.u8Intfb = 0;
    pstPubAttr->stSyncInfo.bIop = (AR_BOOL)1;

    pstPubAttr->stSyncInfo.u16Hmid = 1;
    pstPubAttr->stSyncInfo.u16Bvact = 1;
    pstPubAttr->stSyncInfo.u16Bvbb = 1;
    pstPubAttr->stSyncInfo.u16Bvfb = 1;

    pstPubAttr->stSyncInfo.bIdv = (AR_BOOL)0;
    pstPubAttr->stSyncInfo.bIhs = (AR_BOOL)0;
    pstPubAttr->stSyncInfo.bIvs = (AR_BOOL)0;
}

AR_VOID SAMPLE_VO_GetUserLayerAttr(VO_VIDEO_LAYER_ATTR_S *pstLayerAttr, SIZE_S *pstDevSize)
{
    pstLayerAttr->bClusterMode = AR_FALSE;
    pstLayerAttr->bDoubleFrame = AR_FALSE;
    pstLayerAttr->enDstDynamicRange = DYNAMIC_RANGE_SDR8;
    pstLayerAttr->enPixFormat = PIXEL_FORMAT_YVU_PLANAR_420;

    pstLayerAttr->stDispRect.s32X = 0;
    pstLayerAttr->stDispRect.s32Y = 0;
    pstLayerAttr->stDispRect.u32Height = pstDevSize->u32Height;
    pstLayerAttr->stDispRect.u32Width = pstDevSize->u32Width;

    pstLayerAttr->stImageSize.u32Height = pstDevSize->u32Height;
    pstLayerAttr->stImageSize.u32Width = pstDevSize->u32Width;

    return;
}

namespace app
{

    bool VODP::Init(AR94::BoardConfig &config)
    {
        BOARD_LOG_INFO("VODP INITIALIZING!");
        if (!initVOConfig(config))
        {
            return false;
        }
        BOARD_LOG_INFO("VODP INITIALIZED!");
        return true;
    }

    bool VODP::initVOConfig(AR94::BoardConfig &config)
    {
        AR_S32 s32Ret = AR_SUCCESS;
        for (int i = 0; i < 2; i++)
        {
            /* SET IRQ, ENABLE SubscribeEnable */
            config.stIrqAttr[i].enIrqType = IRQ_TYPE_INTERVAL_LINE;
            config.stIrqAttr[i].stIrqAttr.stLineIrqPara.u32LineCount = 4 * BLOCK_LINE_CNT;
            s32Ret = pfn_AR_MPI_VO_SetIrqAttr(config.VoDev[i], &config.stIrqAttr[i]);
            if (s32Ret != AR_SUCCESS)
            {
                BOARD_LOG_ERROR("VO_{} set irq failed with {:x}", i, s32Ret);
                config.Stop(3);
                return false;
            }

            config.stSubAttr[i].u32SubscribeType = IRQ_TYPE_INTERVAL_LINE | IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
            config.stSubAttr[i].subscribe_call_back = config.vo_subscribe_call_back;
            s32Ret = pfn_AR_MPI_VO_SubscribeEnable(config.VoDev[i], &config.stSubAttr[i]);
            if (s32Ret != AR_SUCCESS)
            {
                BOARD_LOG_ERROR("VO_{} subscribe enable failed with {:x}", i, s32Ret);
                config.Stop(3);
                return false;
            }

            /* enable and subscribe IRQ in DpuManager */
            /*config, start vo*/
            /* 获取app中预置的VO参数*/
            SAMPLE_VO_GetUserPubBaseAttr(&config.stPubAttr[i]);

            /**
            * XREAL_QUESTION 7:
            * 7.1 FPGA 仿真环境中VO的enIntfSync频率是真实的么？ 看现在的Enum，可以支持到60Hz么？
                ==》仿真中提供固定的的pixel_clock为 74.25MHz, 只支持1080P30。软件是支持60Hz，需FPGA 仿真环境也支持，
            * 7.2 FPGA 仿真环境中，GDC可以工作到的频率是多高（15Hz？），假如我把VO配成30Hz，并且准备好
            * 一些输入图像队列，我看到的输出画面会是什么样的？
                ==》 FPGA 仿真环境中，GDC固定工作频率为75MHz。该case gdc为bypass模式，输出的是输入的图像。
            */

            /* USER SET VO DEV SYNC INFO */
            config.stPubAttr[i].enIntfSync = VO_OUTPUT_USER;
            config.stPubAttr[i].stSyncInfo.bSynm = AR_TRUE;
            config.stPubAttr[i].stSyncInfo.bIop = AR_TRUE;
            config.stPubAttr[i].stSyncInfo.u8Intfb = 0;
            config.stPubAttr[i].stSyncInfo.u16Hpw = 60;
            config.stPubAttr[i].stSyncInfo.u16Hbb = 200;
            config.stPubAttr[i].stSyncInfo.u16Hact = 1920;
            config.stPubAttr[i].stSyncInfo.u16Hfb = 120;
            config.stPubAttr[i].stSyncInfo.u16Vpw = 2;
            config.stPubAttr[i].stSyncInfo.u16Vbb = 14;
            config.stPubAttr[i].stSyncInfo.u16Vact = 1080;
            config.stPubAttr[i].stSyncInfo.u16Vfb = 16;
            config.stPubAttr[i].stSyncInfo.u16Hmid = 0;
            config.stPubAttr[i].stSyncInfo.u16Bvact = 0;
            config.stPubAttr[i].stSyncInfo.u16Bvbb = 0;
            config.stPubAttr[i].stSyncInfo.u16Bvfb = 0;
            config.stPubAttr[i].stSyncInfo.bIdv = AR_FALSE;
            config.stPubAttr[i].stSyncInfo.bIhs = AR_FALSE;
            config.stPubAttr[i].stSyncInfo.bIvs = AR_FALSE;
            config.stPubAttr[i].enIntfType = VO_INTF_MIPI;

            /**
             * XREAL_QUESTION 18:
             * @brief 得到7.2 答案之后的追问
             * 18.1 我需要在此case的基础上把GDC也接进来，有哪些TODO？
                ==> gdc本身配置成non-bypass模式，直接修改GDC相关模式的寄存器即可。
             */

            AR_S32 ret = pfn_AR_MPI_VO_SetPubAttr(config.VoDev[i], &config.stPubAttr[i]);
            ret = pfn_AR_MPI_VO_SetDevFrameRate(config.VoDev[i], 90);
            if (ret)
            {
                BOARD_LOG_ERROR("VO_{} set output with mipi failed, err {:x}", i, ret);
                return false;
            }

            VO_START_ATTR_S stStartAttr;
            memset(&stStartAttr, 0, sizeof(VO_START_ATTR_S));
            stStartAttr.enMode = VO_START_AUTO;
            stStartAttr.stAutoAttr.stSrcSel = VO_LOWDEALY_SRC[i];
            /** gdc lines_64_enable=1, start vo line_cnts < 64;
             *	gdc lines_64_enable=0, start vo line_cnts < 32;
             */
            stStartAttr.stAutoAttr.u32InitLineCnt = 1;
            pfn_AR_MPI_VO_SetStartAttr(config.VoDev[i], &stStartAttr);

            VO_LOWDELAY_ATTR_S stLowdelayAttr;
            memset(&stLowdelayAttr, 0, sizeof(VO_LOWDELAY_ATTR_S));
            stLowdelayAttr.enMode = VO_LOWDELAY_MODE_HARDWARE;
            stLowdelayAttr.layerId = 0;
            stLowdelayAttr.stHardwareLowdelayInfo.lowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER;
            stLowdelayAttr.stHardwareLowdelayInfo.u32Lines64Enable = 0;
            pfn_AR_MPI_VO_SetLowdelayAttr(config.VoDev[i], &stLowdelayAttr);

            s32Ret = pfn_AR_MPI_VO_Enable(config.VoDev[i]);
            if (s32Ret != AR_SUCCESS)
            {
                BOARD_LOG_ERROR("VO_{} enable failed with {:x}", i, s32Ret);
                config.Stop(3);
                return false;
            }

            /* SET MIPI TIMING & ENABLE MIPI DSI */
            s32Ret = pfn_AR_MPI_VO_Dsi_SetAttr(config.VoDev[i], &pstDsiCfg_90Hz);
            s32Ret |= pfn_AR_MPI_VO_Dsi_Enable(config.VoDev[i]);
            if (s32Ret)
            {
                BOARD_LOG_ERROR("VO_{} set dsi failed with {:x}", i, s32Ret);
                return s32Ret;
            }

            /**
             * XREAL_QUESTION 8:
             * 8.1 如何理解LayerAttr，Layer是什么语义？
                ==》 VO分为 dev（设备）、layer（图层）、chn（通道层，软件概念）。
                    一个dev最多支持3个图层混色叠加，每个图层可以由多个通道组成，每个通道可以通过指定位置和大小，按通道id的顺序叠图到layer上(内部通过外部scaler实现)。
                    lowdelay模式仅仅支持第0个图层。
             * 8.2 这里的bEnable是BackPressure么？这里的BP是‘谁’压‘谁’
                ==》这是使能vo lowdelay的意思。b代表数据类型bool。
                XREAL: 也就是与反压无关

             * 8.3 这里的bEnable 与
                AR_GDC_LD_CTRL_S stLdCfg;			//对应GDC-REG40-42
                有联系么？ (QUESTION 4中提到的)
                ==》 这里只是配置VO lowdelay相关的信息，GDC需要同步配置成匹配的lowdelay信息才能正常工作。
                XREAL: 也就是无关
            */

            config.stLayerAttr[i].bClusterMode = AR_FALSE;
            config.stLayerAttr[i].bDoubleFrame = AR_FALSE;
            config.stLayerAttr[i].enDstDynamicRange = DYNAMIC_RANGE_SDR8;
            config.stLayerAttr[i].enPixFormat = default_attr.stChnAttr.enPixelFormat;

            config.stLayerAttr[i].stDispRect.s32X = 0;
            config.stLayerAttr[i].stDispRect.s32Y = 0;
            config.stLayerAttr[i].stDispRect.u32Height = config.stDevSize.u32Height;
            config.stLayerAttr[i].stDispRect.u32Width = config.stDevSize.u32Width;

            config.stLayerAttr[i].stImageSize.u32Height = config.stDevSize.u32Height;
            config.stLayerAttr[i].stImageSize.u32Width = config.stDevSize.u32Width;

            config.stLayerAttr[i].u32Stride[0] = 4096; // linebuffer
            config.stLayerAttr[i].u32Stride[1] = 4096;
            config.stLayerAttr[i].u32Stride[2] = 4096;

            config.stLayerAttr[i].u32DispFrmRt = 30; // XXXX: ? Disp FrmRt is not 30 Fps... how does it work?

            config.stLayerAttr[i].memMode = VO_MEMORY_MODE_LOW;
            s32Ret = pfn_AR_MPI_VO_SetVideoLayerAttr(config.VoLayer[i], &config.stLayerAttr[i]);
            if (s32Ret != AR_SUCCESS)
            {
                BOARD_LOG_ERROR("VO_{} SetVideoLayerAttr failed with {:x}", i, s32Ret);
                config.Stop(3);
                return false;
            }

            s32Ret = pfn_AR_MPI_VO_EnableVideoLayer(config.VoLayer[i]);
            if (s32Ret != AR_SUCCESS)
            {
                BOARD_LOG_ERROR("VO_{} EnableVideoLayer failed with {:x}", i, s32Ret);
                config.Stop(4);
                return false;
            }

            BOARD_LOG_INFO("VODP{} video layer initialized.", i);

            BOARD_LOG_INFO("VODP{} initialized.", i);
        }
        return true;
    }

    bool VODP::Start(AR94::BoardConfig &config)
    {
        BOARD_LOG_INFO("VODP STARTING! noting to be done, just printing");
        BOARD_LOG_INFO("VODP STARTED! noting done, just printing");
        return true;
    }

}
