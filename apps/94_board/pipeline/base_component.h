#ifndef BASE_COMPONENT
#define BASE_COMPONENT

#include <string>

#include <board_context/board_config.h>

namespace app {

class BaseComponent {
public:
    BaseComponent() {}
    virtual ~BaseComponent() {}

    virtual bool Init(AR94::BoardConfig& config) {return true;}
    virtual bool Start(AR94::BoardConfig& config) {return true;}
    virtual bool Stop(AR94::BoardConfig& config) {return true;}
    virtual bool Release(AR94::BoardConfig& config) {return true;}

    std::string GetName() const {return name_;}
protected:
    std::string name_ = "pipeline base component";

};

}

#endif