#ifndef VO_DP
#define VO_DP

#include <pipeline/result_presenter.h>

namespace app
{

    class VODP : public ResultPresenter
    {
    public:
        VODP() { name_ = "VODP"; }
        ~VODP() {}

        bool Init(AR94::BoardConfig &config) override;
        bool Start(AR94::BoardConfig &config) override;


    private:
        /*初始化板子VO相关参数*/
        bool initVOConfig(AR94::BoardConfig &config);
    };
}

#endif // VO_DP
