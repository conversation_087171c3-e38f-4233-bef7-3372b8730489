#ifndef VI_DP
#define VI_DP

#include <pipeline/src_provider.h>

namespace app {

class VIDP : public SrcProvider {
public:
    VIDP() {name_ = "VIDP";}
    ~VIDP() {}

    bool Init(AR94::BoardConfig& config) override;
    bool Start(AR94::BoardConfig& config) override;
    bool Stop(AR94::BoardConfig& config) override;

private:
    /*初始化板子Frame相关参数*/
    void initFrameConfig(AR94::BoardConfig& config);

    /*初始化板子VI相关参数*/
    bool initVIConfig(AR94::BoardConfig& config);

    /*初始化板子VB相关参数*/
    bool initVBConfig(AR94::BoardConfig& config);

   /*启动VI*/
    bool startVI(AR94::BoardConfig& config);

};

}

#endif // VI_DP