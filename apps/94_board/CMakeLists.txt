set( TARGET_NAME ar94_api )

set( TARGET_INCLUDE_PATH "${CMAKE_CURRENT_SOURCE_DIR}" )
set( TARGET_SOURCE_PATH "${CMAKE_CURRENT_SOURCE_DIR}" )

add_library( board INTERFACE )

file( GLOB BOARD_PIPELINE_FILES
	"${TARGET_SOURCE_PATH}/pipeline/*.cc"
	"${TARGET_SOURCE_PATH}/pipeline/*.cpp")
file( GLOB BOARD_CONTEXT_FILES
	"${TARGET_SOURCE_PATH}/board_context/*.cc"
	"${TARGET_SOURCE_PATH}/board_context/*.cpp")
file( GLOB BOARD_UTIL_FILES
	"${TARGET_SOURCE_PATH}/util/*.cc"
	"${TARGET_SOURCE_PATH}/util/*.cpp")
target_sources( board
	INTERFACE
	${BOARD_PIPELINE_FILES}
	${BOARD_CONTEXT_FILES}
	${BOARD_UTIL_FILES}
    ${TARGET_SOURCE_PATH}/loader/94_board_api_loader.cpp
	)

target_include_directories( board
	INTERFACE
	"${TARGET_INCLUDE_PATH}"
	"${TARGET_INCLUDE_PATH}/loader"
	"${TARGET_INCLUDE_PATH}/15.1.02.360_20250324/hal/inc"
	"${TARGET_INCLUDE_PATH}/15.1.02.360_20250324/kmodule/inc"
	"${TARGET_INCLUDE_PATH}/15.1.02.360_20250324/middlewares/inc"
	"${TARGET_INCLUDE_PATH}/15.1.02.360_20250324/mpp/inc"
	"${PROJECT_SOURCE_DIR}"
	"${PROJECT_SOURCE_DIR}/heron"
	"${PROJECT_SOURCE_DIR}/heron/interface/include/common"
	"${PROJECT_SOURCE_DIR}/heron/interface/include/public"
	)

target_link_libraries( board
	INTERFACE
	framework::framework
	)

#only build for Xrlinux
if(Xrlinux)
    APP_INSTALL_LIBRARY_WITH_STRIP(${TARGET_NAME} 94_board_sdk_api_provider.cpp board)
    #APP_TARGET_WITH_STRIP(test_mpp_dp sample_dp.cpp board)
endif()

# stub so
set( TARGET_NAME ar94_api_stub )

add_library( board_stub INTERFACE )

set( TARGET_INCLUDE_PATH "${CMAKE_CURRENT_SOURCE_DIR}" )
set( TARGET_SOURCE_PATH "${CMAKE_CURRENT_SOURCE_DIR}" )

target_include_directories( board_stub
	INTERFACE
	"${TARGET_INCLUDE_PATH}"
	"${TARGET_INCLUDE_PATH}/15.1.02.360_20250324/hal/inc"
	"${TARGET_INCLUDE_PATH}/15.1.02.360_20250324/kmodule/inc"
	"${TARGET_INCLUDE_PATH}/15.1.02.360_20250324/middlewares/inc"
	"${TARGET_INCLUDE_PATH}/15.1.02.360_20250324/mpp/inc"
	"${PROJECT_SOURCE_DIR}"
	"${PROJECT_SOURCE_DIR}/heron"
	"${PROJECT_SOURCE_DIR}/heron/interface/include/common"
	"${PROJECT_SOURCE_DIR}/heron/interface/include/public"
	)

target_link_libraries( board_stub
    INTERFACE
    framework::framework
    )

APP_INSTALL_LIBRARY_WITH_STRIP(${TARGET_NAME} 94_board_sdk_api_stub_provider.cpp board_stub)