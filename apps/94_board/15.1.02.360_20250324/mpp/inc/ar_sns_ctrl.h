#ifndef __AR_SNS_CTRL_H__
#define __AR_SNS_CTRL_H__

#include "hal_type.h"
#include "ar_comm_3a.h"
#include "ar_comm_vi.h"
#include "hal_vin_log.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#if defined(AR9341)
#define SENSOR_CLK_ID	(CLK_SENSOR0)
#elif defined(PROXIMA)
#define SENSOR_CLK_SRC	 (CGU_OSCIN_CLK)
#define SENSOR_CLK_SRC1	 (PIXEL_PLL_CLK) 
#define SENSOR_CLK_ID	(CGU_SENSOR_MCLK0)
#else
#define SENSOR_CLK_SRC	 (KUIPER_CGU_OSCIN_CLK)
#define SENSOR_CLK_SRC1	 (KUIPER_PIXEL_PLL0)
#define SENSOR_CLK_ID	(KUIPER_CGU_SENSOR_MCLK0)

#endif


typedef struct arISP_SNS_STATE_S
{
    AR_BOOL     bInit;                  /* AR_TRUE: Sensor init */
    AR_BOOL     bSyncInit;              /* AR_TRUE: Sync Reg init */
    AR_U8       u8ImgMode;
    AR_U8       u8Hdr;               /* AR_TRUE: HDR enbale */
    WDR_MODE_E  enWDRMode;

    ISP_SNS_REGS_INFO_S astRegsInfo[2]; /* [0]: Sensor reg info of cur-frame; [1]: Sensor reg info of pre-frame ; */

    AR_U32      au32FL[2];              /* [0]: FullLines of cur-frame; [1]: Pre FullLines of pre-frame */
    AR_U32      u32FLStd;               /* FullLines std */
    AR_U32      u32FLMid;
    AR_U32      u32FLShort;
    AR_U32      au32WDRIntTime[4];	
	AR_S32   focus;
} ISP_SNS_STATE_S;

typedef enum arISP_SNS_MIRRORFLIP_TYPE_E
{
    ISP_SNS_NORMAL      = 0,
    ISP_SNS_MIRROR      = 1,
    ISP_SNS_FLIP        = 2,
    ISP_SNS_MIRROR_FLIP = 3,
    ISP_SNS_BUTT
}ISP_SNS_MIRRORFLIP_TYPE_E;

/**< 定义sensor的默认属性，用户可以直接从驱动获取这些属性*/
typedef struct
{
   STRU_COMBO_DEV_ATTR_T stComboAttr;
   VI_DEV_ATTR_S stDevAttr;
   VI_PIPE_ATTR_S stPipeAttr;
   ISP_PUB_ATTR_S stPubAttr;
   VI_CHN_ATTR_S  stChnAttr;
   VI_IR_AUTOCALIATTR_S stCaliAttr;
   AR_VOID * pPrivData;
}SRTU_SENSOR_DEFAULT_ATTR_T;

typedef struct arISP_SNS_OBJ_S
{
    AR_S32  (*pfnRegisterCallback)(VI_PIPE ViPipe, ALG_LIB_S *pstAeLib, ALG_LIB_S *pstAwbLib);/**<向vin驱动注册sensor 回调函数和 aec 回调函数*/
    AR_S32  (*pfnUnRegisterCallback)(VI_PIPE ViPipe, ALG_LIB_S *pstAeLib, ALG_LIB_S *pstAwbLib);/**<向vin驱动注销sensor 回调函数和 aec 回调函数*/
    AR_S32  (*pfnSetBusInfo)(VI_PIPE ViPipe, ISP_SNS_COMMBUS_U unSNSBusInfo);/**<向sensor 驱动程序设置i2c id*/
    AR_VOID (*pfnStandby)(VI_PIPE ViPipe);/**< 不使用 */
    AR_VOID (*pfnRestart)(VI_PIPE ViPipe);/**< 不使用 */
    AR_VOID (*pfnMirrorFlip)(VI_PIPE ViPipe, ISP_SNS_MIRRORFLIP_TYPE_E eSnsMirrorFlip);/**< 不使用 */
    AR_S32  (*pfnWriteReg)(VI_PIPE ViPipe, AR_S32 s32Addr, AR_S32 s32Data);/**< 不使用 */
    AR_S32  (*pfnReadReg)(VI_PIPE ViPipe, AR_S32 s32Addr);/**< 不使用 */
    AR_S32  (*pfnSetInit)(VI_PIPE ViPipe, ISP_INIT_ATTR_S *pstInitAttr);	/**< 不使用 */
    AR_S32  (*pfnSetFocus)(VI_PIPE ViPipe, AR_S32 focus);  /**< 不使用 */
    AR_S32  (*pfnGetDefaultAttr)(AR_U8 u8SnsMode,SRTU_SENSOR_DEFAULT_ATTR_T *pstDefaultAttr); /**<通过sensor 模式获取到默认的属性参数，
    u8SnsModed的低8bit的值和ISP_PUB_ATTR_S  中的u8SnsMode一一对应，驱动实现的时候注意配合，获取到的属性用户针对需求进行修改，然后设置， 8-15 这8个bit保留给用户使用，用户可以告诉sensor 驱动一些特殊的模式
    16-24 这8个bit 指示这个驱动工作在那个具体的sensor上，典型的应该是一个驱动多个sensor 公用，比如imx307 在evb 上有4个，这8个bit 指示了不同的sensor，可以认为是接口索引 25-31 这8个bit 保留后续扩展
    返回0 表示获取成功，返回小于0 表示获取失败*/
    AR_S32  (*pfnProcSensorRawCallback)(VI_PIPE ViPipe, VIDEO_FRAME_INFO_S *pstSrcFrmInfo,VIDEO_FRAME_INFO_S *pstDstFrmInfo);	
    AR_S32  (*pfnSensorCtl)(VI_PIPE ViPipe, AR_S32 ctl_code,void* buffer, AR_S32 size);
} ISP_SNS_OBJ_S;

typedef struct __STRU_SEN_IF_BOARD_INFO_S__
{
   AR_S32 i2c_index_valid;
   AR_S32 i2c_index;
   AR_S32 rst_valid;
   AR_S32 rst[3];
   AR_S32 power_valid;
   AR_S32 power[3];   
   AR_S32 power1_valid;
   AR_S32 power1[3];    
   AR_S32 power2_valid;
   AR_S32 power2[3];   
   AR_S32 power3_valid;
   AR_S32 power3[3];   
   AR_S32 stand_by_valid;
   AR_S32 stand_by[3];
   AR_S32 mcld_valid;
   AR_S32 mck_src_id;
   AR_S32 mck_src_k;
   AR_S32 mclk_id;
   AR_S32 mclk_k;
   AR_S32 shutter_valid;
   AR_S32 shutter0;   
   AR_S32 shutter1;
}STRU_SEN_IF_BOARD_INFO_S;


#define SensorObjDef(type, name)                \
    type stSns##name##Obj
#define SensorObj(sensor)                       \
    SensorObjDef(ISP_SNS_OBJ_S, sensor)
void *AR_MPI_VIN_GetSensorObj(AR_CHAR *sensor,ISP_SNS_OBJ_S **obj);
void AR_MPI_VIN_CloseSensorObj(void *handle);
AR_S32 AR_MPI_VIN_PipeBindSensor(VI_PIPE ViPipe,ISP_SNS_OBJ_S *p_obj,AR_S32 bus_id);
AR_S32 AR_MPI_VIN_PipeUnBindSensor(VI_PIPE ViPipe,ISP_SNS_OBJ_S *p_obj);
AR_S32 AR_MPI_VIN_GetSensorBoardInterfaceInfo(AR_CHAR *bard_cfg_name,AR_CHAR *interface_name,STRU_SEN_IF_BOARD_INFO_S *p_info);


#define CMOS_CHECK_POINTER(ptr)\
    do {\
        if (AR_NULL == ptr)\
        {\
            ISP_TRACE(AR_DBG_ERR, "Null Pointer!\n");\
            return AR_ERR_ISP_NULL_PTR;\
        }\
    }while(0)

#define CMOS_CHECK_POINTER_VOID(ptr)\
    do {\
        if (AR_NULL == ptr)\
        {\
            ISP_TRACE(AR_DBG_ERR, "Null Pointer!\n");\
            return;\
        }\
    }while(0)

#define SENSOR_FREE(ptr)\
    do{\
        if (AR_NULL != ptr)\
        {\
            free(ptr);\
            ptr = AR_NULL;\
        }\
    } while (0)


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */

#endif /* __AR_SNS_CTRL_H__ */
