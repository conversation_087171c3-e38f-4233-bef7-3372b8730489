#ifndef __MPI_PROFILE_H__
#define __MPI_PROFILE_H__

#ifdef __cplusplus
#if __cplusplus
	extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#include "mpi_type.h"
#include "hal_profile.h"

/**
* @brief  在用户态释放profile.
* @retval 0 成功 , 其它 失败.
* @note   用户态使用profile进行时间统计时需调用主动释放profile
*/

AR_S32 AR_MPI_Profile_Exit();
/**
* @brief  在用户态统计profile起始时间.
* @param  enUid 当前模块所在，core：PROFILE_UID_CORE，hal：PROFILE_UID_HAL， mpp：PROFILE_UID_MPP， app：PROFILE_UID_APP.
* @param  pchMoudle 时间统计函数所在模块.
* @param  pchFuncName 时间统计函数名称.
* @retval 0 成功 , 其它 失败.
* @note   与ar_hal_profile_end 函数配合使用
*/

AR_S32 AR_MPI_Profile_Start(AR_HAL_PROFILE_UID_E enUid, const AR_CHAR * pchMoudle, const AR_CHAR * pchFuncName);
/**
* @brief  在用户态统计profile终止时间.
* @param  enUid 当前模块所在，core：PROFILE_UID_CORE，hal：PROFILE_UID_HAL， mpp：PROFILE_UID_MPP， app：PROFILE_UID_APP.
* @param  pchMoudle 时间统计函数所在模块.
* @param  pchFuncName 时间统计函数名称.
* @retval 0 成功 , 其它 失败.
* @note   与ar_hal_profile_start 函数配合使用
* @note   单进程模式下使用bdcmd dump_profile --help获取统计信息
* @note   多进程模式下使用bdcmd dump_profile_core --help 获取core层统计信息，使用bdcmd dump_profile --help获取hal/mpp/app层统计信息
*/

AR_S32 AR_MPI_Profile_End(AR_HAL_PROFILE_UID_E enUid, const AR_CHAR * pchMoudle, const AR_CHAR * pchFuncName);

#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */

#endif /* __MPI_PROFILE_H__ */
