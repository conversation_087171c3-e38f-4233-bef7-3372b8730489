/**
 * \file
 * \brief 描述双光融合相关接口
 */

#ifndef __MPI_INFUSION_H__
#define __MPI_INFUSION_H__

#include "ar_comm_video.h"
#include "hal_infusion.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

/******************************* API declaration *****************************/
/**
\addtogroup MPI_INFUSION
* @brief 双光融合功能模块
 * @{
*/


/**
   \brief  获取预设参数
   \param[in]  emMode       预设参数号。取值范围：[0, INFUSION_WORK_MODE_NUM)
   \param[out] pstTuning    预设参数
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_GetPresetTuning(ENUM_AR_INFUSION_WORK_MODE_T emMode, STRU_AR_INFUSION_TUNING_T *pstTuning);

/**
   \brief  打开infusion设备
   \retval ::infusion设备号 :   infusion设备号
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_Open();

/**
   \brief  关闭infusion设备
   \param[in]  s32Fd        infusion设备号
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_VOID AR_MPI_INFUSION_Close(AR_S32 s32Fd);

/**
   \brief  设置infusion输入输出帧信息
   \param[in]  s32Fd        infusion设备号
   \param[in]  pstFrameInfo 帧信息
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_SetFrame(AR_S32 s32Fd, STRU_AR_INFUSION_FRAME_INFO_T *pstFrameInfo);

/**
   \brief  获取infusion输入输出帧信息
   \param[in]  s32Fd        infusion设备号
   \param[out] pstFrameInfo 帧信息
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_GetFrame(AR_S32 s32Fd, STRU_AR_INFUSION_FRAME_INFO_T *pstFrameInfo);

/**
   \brief  开始融合
   \param[in]  s32Fd        infusion设备号
   \param[in] pstWorkMode   工作模式，只接受NULL
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_Start(AR_S32 s32Fd, STRU_AR_INFUSION_WORK_MODE_T *pstWorkMode);

/**
   \brief  融合完成后调用, 与AR_MPI_INFUSION_Start配对使用
   \param[in]  s32Fd        infusion设备号
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_Stop(AR_S32 s32Fd);

/**
   \brief  设置融合参数
   \param[in]  s32Fd        infusion设备号
   \param[in]  pstTuning    融合参数
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_SetTuning(AR_S32 s32Fd, STRU_AR_INFUSION_TUNING_T *pstTuning);

/**
   \brief  获取融合参数
   \param[in]  s32Fd        infusion设备号
   \param[out] pstTuning    融合参数
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_GetTuning(AR_S32 s32Fd, STRU_AR_INFUSION_TUNING_T *pstTuning);

/**
   \brief  获取融合完成通知(阻塞式)
   \param[in]  s32Fd         infusion设备号
   \param[out] pu32FrameDone 融合完成状态
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_GetFrameDone(AR_S32 s32Fd, AR_U32 *pu32FrameDone);

/**
   \brief  获取融合开始通知(阻塞式)
   \param[in]  s32Fd         infusion设备号
   \param[out] pu32WlineDone 融合开始状态
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_GetWlineDone(AR_S32 s32Fd, AR_U32 *pu32WlineDone);

/**
   \brief  设置融合时的工作频率
   \param[in]  s32Fd         infusion设备号
   \param[in]  u32FreqMHz    工作频率
   \retval ::AR_SUCCESS     :   成功
   \retval ::AR_FAILURE     :   失败
*/
AR_S32 AR_MPI_INFUSION_SetFreq(AR_S32 s32Fd, AR_U32 u32FreqMHz);

/** @} */  /** <!-- ==== API declaration end ==== */

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */


#endif //__MPI_INFUSION_H__

