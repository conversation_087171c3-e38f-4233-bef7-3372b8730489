/**
 * \file
 * \brief 描述图像匹配相关的数据结构和接口.
 */

#ifndef  __MPI_DPU_MATCH_H__
#define  __MPI_DPU_MATCH_H__

#include "ar_comm_dpu_match.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */
/********************************Macro Definition********************************/

/**< 错误码：通道 ID 超出合法范围 */
#define AR_ERR_MATCH_INVALID_GRPID    AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_CHNID)
/**< 错误码：参数超出合法范围  */
#define AR_ERR_MATCH_ILLEGAL_PARAM     AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_ILLEGAL_PARAM)
/**< 错误码：试图申请或者创建已经存在的设备、通道或者资源 */
#define AR_ERR_MATCH_EXIST             AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_EXIST)
/**< 错误码：试图使用或者销毁不存在的设备、通道或者资源 */
#define AR_ERR_MATCH_UNEXIST           AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_UNEXIST)
/**< 错误码：函数参数中有空指针 */
#define AR_ERR_MATCH_NULL_PTR          AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NULL_PTR)
/**< 错误码：使用前未初始化系统，设备或通道。*/
#define AR_ERR_MATCH_NOT_CONFIG        AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_CONFIG)
/**< 错误码：不支持的操作或者功能 */
#define AR_ERR_MATCH_NOT_SUPPORT       AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_SUPPORT)
/**< 错误码： 该操作不允许，如试图修改静态配置参数 */
#define AR_ERR_MATCH_NOT_PERM          AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_PERM)
/**< 错误码：分配内存失败，如系统内存不足 */
#define AR_ERR_MATCH_NOMEM             AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOMEM)
/**< 错误码：分配缓存失败，如申请的数据缓存去太大 */
#define AR_ERR_MATCH_NOBUF             AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOBUF)
/**< 错误码：缓冲区中无数据 */
#define AR_ERR_MATCH_BUF_EMPTY         AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUF_EMPTY)
/**< 错误码：缓冲区中数据满 */
#define AR_ERR_MATCH_BUF_FULL          AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUF_FULL)
/**< 错误码：系统没有初始化或没有加载相应模块 */
#define AR_ERR_MATCH_SYS_NOTREADY      AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_SYS_NOTREADY)
/**< 错误码：MATCH系统忙 */
#define AR_ERR_MATCH_BUSY              AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUSY)

/********************************API Definition********************************/
/**
\addtogroup MPI_DPU
 * @brief DPU(Disparity Process Unit)对输入的校正后的左图像和右图像进行匹配计算得出深度图。
 * DPU只能对校正后的左右图像进行匹配；对于未校正的图像，需先通过GDC校正，再进行匹配。
 * @{
*/

/**
\brief 获取所有group共享的辅助内存字节数。
\param[in]  u32DstWidth    :   输出的图像宽, [128, 1920]
\param[in]  u32DstHeight   :   输出的图像高, [16, 1080]
\param[out] pu32Size       :   辅助内存字节数。不能为空。
\retval ::0                :   成功。
\retval ::non-zero         :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_GetAssistBufSize(AR_U32 u32DstWidth,
                                         AR_U32 u32DstHeight,
                                         AR_U32 *pu32Size);

/**
\brief 创建组。不支持重复创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\param[in] pstGrpAttr    :   组属性。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_CreateGrp(DPU_MATCH_GRP DpuMatchGrp,
                                  const DPU_MATCH_GRP_ATTR_S *pstGrpAttr);

/**
\brief 销毁组。组必须已创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_DestroyGrp(DPU_MATCH_GRP DpuMatchGrp);

/**
\brief 设置组属性。组必须已创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\param[in] pstGrpAttr    :   组属性。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_SetGrpAttr(DPU_MATCH_GRP DpuMatchGrp,
                                   const DPU_MATCH_GRP_ATTR_S *pstGrpAttr);

/**
\brief 获取组属性。组必须已创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\param[out] pstGrpAttr   :   组属性。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_GetGrpAttr(DPU_MATCH_GRP DpuMatchGrp,
                                   DPU_MATCH_GRP_ATTR_S *pstGrpAttr);

/**
\brief 启用组。组必须已创建。组必须已创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_StartGrp(DPU_MATCH_GRP DpuMatchGrp);

/**
\brief 禁用组。组必须已创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_StopGrp(DPU_MATCH_GRP DpuMatchGrp);

/**
\brief 设置通道属性。组必须已创建。
* @note    暂不支持
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\param[in] DpuMatchChn   :   通道号。取值范围：[0,1)。
\param[in] DpuMatchGrp   :   通道属性。不能为空。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_SetChnAttr(DPU_MATCH_GRP DpuMatchGrp,
                                   DPU_MATCH_CHN DpuMatchChn,
                                   const DPU_MATCH_CHN_ATTR_S *pstChnAttr);

/**
\brief 获取通道属性。组必须已创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\param[in] DpuMatchChn   :   通道号。取值范围：[0,1)。
\param[out] DpuMatchGrp  :   通道属性。不能为空。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_GetChnAttr(DPU_MATCH_GRP DpuMatchGrp,
                                   DPU_MATCH_CHN DpuMatchChn,
                                   DPU_MATCH_CHN_ATTR_S *pstChnAttr);

/**
\brief 启用通道。组必须已创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\param[in] DpuMatchChn   :   通道号。取值范围：[0,1)。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_EnableChn(DPU_MATCH_GRP DpuMatchGrp,
                                  DPU_MATCH_CHN DpuMatchChn);

/**
\brief 禁用通道。组必须已创建。
\param[in] DpuMatchGrp   :   组号。取值范围：[0, 8)。
\param[in] DpuMatchChn   :   通道号。取值范围：[0,1)。
\retval ::0              :   成功。
\retval ::non-zero       :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_DisableChn(DPU_MATCH_GRP DpuMatchGrp,
                                   DPU_MATCH_CHN DpuMatchChn);

/**
\brief 用户发送数据。组必须已创建。
\param[in] DpuMatchGrp     :   组号。取值范围：[0, 8)。
\param[in] pstLeftFrame    :   左图图像。不能为空。
\param[in] pstRightFrame   :   右图图像。不能为空。
\param[in] s32MilliSec     :   超时参数 s32MilliSec 设为-1 时，为阻塞接口；
                               0时为非阻塞接口；大于 0 时为超时等待时间，超时时间的单位为毫秒（ms）。
\retval ::0                :   成功。
\retval ::non-zero         :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_SendFrame(DPU_MATCH_GRP DpuMatchGrp,
                                  const VIDEO_FRAME_INFO_S *pstLeftFrame,
                                  const VIDEO_FRAME_INFO_S *pstRightFrame,
                                  AR_S32 s32MilliSec);

/**
\brief 用户从通道获取一帧处理完成的图像。组必须已创建。
\param[in] DpuMatchGrp      :   组号。取值范围：[0, 8)。
\param[out] pstLeftFrame    :   左图图像。不能为空。
\param[out] pstRightFrame   :   右图图像。不能为空。
\param[out] pstDstFrame     :   匹配后图像。不能为空。
                                若输出格式为PIXEL_FORMAT_S16C1，数据元素格式为S12Q4(1bit符号位 + 11bit整数部分 + 4bit小数部分)
                                若输出格式为PIXEL_FORMAT_YUV_400, 数据元素格式为8bit整数
\param[in] s32MilliSec      :   超时参数 s32MilliSec 设为-1 时，为阻塞接口；
                                0时为非阻塞接口；大于 0 时为超时等待时间，超时时间的单位为毫秒（ms）。
\retval ::0                 :   成功。
\retval ::non-zero          :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_GetFrame(DPU_MATCH_GRP DpuMatchGrp,
                                 VIDEO_FRAME_INFO_S *pstSrcLeftFrame,
                                 VIDEO_FRAME_INFO_S *pstSrcRightFrame,
                                 VIDEO_FRAME_INFO_S *pstDstFrame,
                                 AR_S32 s32MilliSec);

/**
\brief 用户释放一帧通道图像。组必须已创建。
\param[in] DpuMatchGrp     :   组号。取值范围：[0, 8)。
\param[in] pstLeftFrame    :   左图图像。不能为空。
\param[in] pstRightFrame   :   右图图像。不能为空。
\param[in] pstDstFrame     :   匹配后图像。不能为空。
\retval ::0                :   成功。
\retval ::non-zero         :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 AR_MPI_DPU_MATCH_ReleaseFrame(DPU_MATCH_GRP DpuMatchGrp,
                                     const VIDEO_FRAME_INFO_S *pstSrcLeftFrame,
                                     const VIDEO_FRAME_INFO_S *pstSrcRightFrame,
                                     const VIDEO_FRAME_INFO_S *pstDstFrame);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
/** @} */

#endif /* __MPI_DPU_MATCH_H__ */
