#ifndef __MPI_GDC_API_H__
#define __MPI_GDC_API_H__

#include "hal_npu_types.h"
#include "hal_type.h"
#include "hal_vps_gdc.h"
#include "ar_comm_video.h"
#include "ar_comm_vb.h"
#include "hal_vb.h"
#include "mpi_dbglog.h"


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */


//=========================93 Structs And Apis====================================================//

#define AR_GDC_OP_NUM_MAX 8
#define CHECK_GDC_WH_VALID(num)  			(0 < (num) && (num) <= 3840)

/* 5 action params*/
typedef enum
{
    AR_GDC_ROTATE_E = 1,
    AR_GDC_FLIP_E = 2,
    AR_GDC_MIRROR_E = 3,
    AR_GDC_LDC_E = 4,
    AR_GDC_EIS_E = 5,
    AR_GDC_USER_DEF_E = 6,
    AR_GDC_FEC_E = 7,
    AR_GDC_BUTT_E
} AR_GDC_OP_TYPE_E;

typedef struct
{
    AR_FLOAT fAngle; // 0 - 360; ONLY SUPPORT 0, 90, 180, 270 now.
} AR_GDC_ROTATE_ATTR_S;
typedef struct
{
    AR_BOOL bEnable;
} AR_GDC_FLIP_ATTR_S;
typedef struct
{
    AR_BOOL bEnable;
} AR_GDC_MIRROR_ATTR_S;
typedef struct
{
  AR_FLOAT k[9];
  AR_FLOAT ldc_k0 ;
  AR_FLOAT ldc_k1 ;
  AR_FLOAT ldc_k2 ;
} AR_GDC_LDC_ATTR_S;

typedef struct
{
  AR_FLOAT k[9];
  AR_FLOAT fec_k0 ;
  AR_FLOAT fec_k1 ;
  AR_FLOAT fec_k2 ;
  AR_FLOAT fec_k3 ;
} AR_GDC_FEC_ATTR_S;

typedef struct
{
  AR_U32 u32Reserved; //TO DO.
} AR_GDC_EIS_ATTR_S;

typedef struct
{
    AR_U64 u64LutVirtAddr;
	AR_U32 u32LutLen;
} AR_GDC_USER_ATTR_S;

typedef struct
{
    AR_U64 u64LutVirtAddr;
	AR_U64 u64LutPhyAddr;
	AR_U32 u32LutLen;
	void * pReserved;
} AR_GDC_USER_ATTR_EXT_S;

typedef struct
{
    AR_GDC_OP_TYPE_E enType;
    void * pAttr; //Points to attributes.
} AR_GDC_TRANS_PARAM_S;

typedef struct
{
	AR_GDC_BUFFER_S stInBuffer;
	AR_GDC_BUFFER_S stOutBuffer;
	AR_GDC_TRANS_PARAM_S stParams[AR_GDC_OP_NUM_MAX];
} AR_GDC_TRANSFORM_S;

typedef struct
{
	AR_GDC_TRANS_PARAM_S stParams[AR_GDC_OP_NUM_MAX];
} AR_GDC_TRANSFORM_ARRAY_S;

/**
* @brief 多操作合一接口，内部会按照0-AR_GDC_OP_NUM_MAX的顺序将操作合并，充分节省硬件资源
* @param
* @retval 0 成功 其他 失败
* @note  输入输出地址为有效物理地址
**/
AR_S32 AR_MPI_GDC_Transform(AR_GDC_TRANSFORM_S * pstParam);

/**
* @brief 多操作合一接口，内部会按照0-AR_GDC_OP_NUM_MAX的顺序将操作合并，充分节省硬件资源
* @param
* @retval 0 成功 其他 失败
* @note  输入输出地址为有效物理地址,支持USRMode下传入LUT物理地址，节省Lib内部Lut生成和拷贝操作
**/
AR_S32 AR_MPI_GDC_Transform_Ext(AR_GDC_TRANSFORM_S * pstParam);

/**
* @brief 多操作合一接口，内部会按照0-AR_GDC_OP_NUM_MAX的顺序将操作合并，充分节省硬件资源
* @param
* @retval 0 成功 其他 失败
* @note  输入输出地址为有效物理地址。为了在调用ifc/scaler时，不用做参数转换。
**/
AR_S32 AR_MPI_GDC_Transform_General(AR_IMG_S *pstImgIn, AR_IMG_S *pstImgOut, AR_GDC_TRANSFORM_ARRAY_S * pstParam);

/**
* @brief 多操作合一接口，内部会按照0-AR_GDC_OP_NUM_MAX的顺序将操作合并，充分节省硬件资源
* @param
* @retval 0 成功 其他 失败
* @note  输入输出地址为有效物理地址。为了在调用ifc/scaler时，不用做参数转换。,支持USRMode下传入LUT物理地址，节省Lib内部Lut生成和拷贝操作
**/
AR_S32 AR_MPI_GDC_Transform_General_Ext(AR_IMG_S *pstImgIn, AR_IMG_S *pstImgOut, AR_GDC_TRANSFORM_ARRAY_S * pstParam);
/**
* @brief 设置GDC频率
* @param
* @retval 0 成功 其他 失败
* @note  不建议GDC工作过程中动态修改频率
**/

AR_S32 AR_MPI_GDC_SetFrequency(AR_U32 u32FreqMHz);

/**
* @brief 获取GDC处理时间
* @param
* @retval 0 成功 其他 失败
* @note  
**/
AR_S32 AR_MPI_GDC_GetTime(AR_FLOAT *fpTime);

/**
* @brief  将GDC挂起，power off
* @param  NULL
* @retval 0 成功，非零值 失败.
* @note  在挂起之前请调用者自行保证没有正在运行的任务
*/
AR_S32 AR_MPI_GDC_Suspend(void);

/**
* @brief  将GDC唤醒，power on
* @param  NULL
* @retval 0 成功，非零值 失败.
* @note  唤醒后，原来由调用者自行设置的频率需要调用者负责恢复
*/
AR_S32 AR_MPI_GDC_Resume(void);


//=========================94 Structs And Apis====================================================//

#define CHECK_GDC_WH_VALID_9411(num)  			(0 < (num) && (num) <= 7680)

#if 0
#define GDC_ADV_MAX_GRP_NUM        8
#define GDC_ADV_MAX_CHN_NUM        2

typedef struct arDPU_RECT_CHN_ATTR_S 
{ 
 	AR_GDC_ADV_CFG_S gdc_cfg;
}DPU_RECT_CHN_ATTR_S; 

typedef enum arDPU_RECT_MODE_E 
{ 
	 DPU_RECT_MODE_SINGLE = 0x0, /* only channel 0 work */ 
	 DPU_RECT_MODE_DOUBLE = 0x1, /* two channel work */ 
	 DPU_RECT_MODE_BUTT 
}DPU_RECT_MODE_E;

typedef struct arDPU_RECT_GRP_ATTR_S 
{ 
	DPU_RECT_MODE_E enRectMode; 
	SIZE_S stLeftImageSize;
	SIZE_S stRightImageSize; 
	AR_S32 LeftLutId;  
	AR_S32 RightLutId; 
	AR_U32 u32Depth;
	AR_BOOL bNeedSrcFrame; 
	FRAME_RATE_CTRL_S stFrameRate; 
}DPU_RECT_GRP_ATTR_S;

typedef enum
{
	NODE_STOPPED,		/*group, channel处于stop状态,
						  此时相应的group或者channel的function没有正常工作（没有被创建，或者是suspend）*/
	NODE_RUNNING,		/*group, channel正在正常运行*/
	NODE_STOPPING,		/*正在停止group, group中资源并不释放 */
	NODE_DESTORYING,	/*正在释放group, 及其group中channel资源, */
} ENUM_NODE_STATUS;

typedef struct STRUCT_VPSS_NODE_t
{
	AR_S32				 receiver_cnt;
	AR_S32				 sender_cnt;

	ENUM_NODE_STATUS	 e_status;

	ar_queue_id_t		 in_q;
	ar_queue_id_t		 out_q;

	ar_thread_func_t	 p_node_fun;
	ar_thread_id_t		 node_thread;

	AR_S32     			 GdcGrp;
	AR_S32     			 GdcChn;

	DPU_RECT_CHN_ATTR_S  chn_attr;
	
	ar_lock_id_t		 p_node_lock;
} STRUCT_GDC_ADV_NODE;


typedef struct
{
	STRU_SYS_BIND_SENDER		 gdc_bind_sender;
	STRU_SYS_BIND_RECEIVER		 gdc_bind_receiver;

	DPU_RECT_GRP_ATTR_S			*p_grp_attr[GDC_ADV_MAX_GRP_NUM];
	STRUCT_GDC_ADV_NODE			*p_chn_node[GDC_ADV_MAX_GRP_NUM*GDC_ADV_MAX_CHN_NUM];

	ar_lock_id_t				 p_gdc_lock;
} STRUCT_GDC_ADV;

//////////////////////////binder struct/////////////////////////////////////////////////////
#endif
/**
* @brief  gdc执行接口 cfg+start      block api
* @param  pstParam 配置参数，包含输入输出meshwarp等各种配置信息.
* @retval 0:成功 其他：失败.
*/
AR_S32 AR_MPI_GDC_ADV_Process(AR_GDC_ADV_TRANSFORM_S * pstParam);

/**
* @brief  gdc配置接口
* @param  pstParam 配置参数，包含输入输出meshwarp等各种配置信息.
* @retval 0:成功 其他：失败.
* @note  !!! Will Abandon in Next Version. And AR_MPI_GDC_ADV_Process will Instead

*/
AR_S32 AR_MPI_GDC_ADV_Config(AR_GDC_ADV_TRANSFORM_S * pstParam);

/**
* @brief  gdc配置cpu_bp_lines
* @param  pstLineParam 配置参数，包含目标gdccoreid和linenum.
* @retval 0:成功 其他：失败.
*/
AR_S32 AR_MPI_GDC_ADV_Config_CPUBp_Line(AR_GDC_ADV_LINE_PARAMS_S *pstLineParam);

/**
* @brief  gdc获取frameid/pts
* @param  AR_GDC_ADV_FRMPTS_PARAMS_S 配置参数，包含目标gdccoreid和frameid/pts.
* @retval 0:成功 其他：失败.
*/
AR_S32 AR_MPI_GDC_ADV_Get_FrmPts(AR_GDC_ADV_FRMPTS_PARAMS_S *pstGdcFrmPtsParams);

/**
* @brief  gdc 开始配置接口
* @param  pstParam 开始配置参数.包含start模式等信息
* @retval 0:成功 其他：失败.
* @note !!! Will Abandon in Next Version. And AR_MPI_GDC_ADV_Process will Instead

*/
AR_S32 AR_MPI_GDC_ADV_Start(AR_GDC_ADV_TRANSFORM_S * pstParam);
/**
* @brief  gdc 停止配置接口
* @param  s32CoreId Gdc Core ID
* @retval 0:成功 其他：失败.
* @note 
*/
AR_S32 AR_MPI_GDC_ADV_Stop(AR_S32 s32CoreId);

/**
* @brief  gdc POWER OFF 配置接口
* @param  s32CoreId Gdc Core ID
* @retval 0:成功 其他：失败.
* @note 
*/
AR_S32 AR_MPI_GDC_ADV_PWROFF(AR_S32 s32CoreId);

/**
* @brief  gdc POWER ON 配置接口
* @param  s32CoreId Gdc Core ID
* @retval 0:成功 其他：失败.
* @note 
*/
AR_S32 AR_MPI_GDC_ADV_PWRON(AR_S32 s32CoreId);

/**
* @brief  gdc 查询接口
* @param  pstParam 配置参数.查询接口依靠frameid查找对应实例
* @retval 0:成功 其他：失败.
*/
AR_S32 AR_MPI_GDC_ADV_Query(AR_GDC_ADV_TRANSFORM_S *pstGdcParams);


#if 0
AR_S32 AR_MPI_DPU_RECT_CreateGrp(AR_S32 DpuRectGrp, const DPU_RECT_GRP_ATTR_S *pstGrpAttr);
AR_S32 AR_MPI_DPU_RECT_DestroyGrp(AR_S32 DpuRectGrp);
AR_S32 AR_MPI_DPU_RECT_SetGrpAttr(AR_S32 DpuRectGrp, const DPU_RECT_GRP_ATTR_S *pstGrpAttr);
AR_S32 AR_MPI_DPU_RECT_GetGrpAttr(AR_S32 DpuRectGrp, DPU_RECT_GRP_ATTR_S *pstGrpAttr);
AR_S32 AR_MPI_DPU_RECT_StartGrp(AR_S32 DpuRectGrp);
AR_S32 AR_MPI_DPU_RECT_StopGrp(AR_S32 DpuRectGrp);

AR_S32 AR_MPI_DPU_RECT_SetChnAttr(AR_S32 DpuRectGrp, AR_S32 DpuRectChn, const DPU_RECT_CHN_ATTR_S *pstChnAttr);
AR_S32 AR_MPI_DPU_RECT_GetChnAttr(AR_S32 DpuRectGrp, AR_S32 DpuRectChn, DPU_RECT_CHN_ATTR_S *pstChnAttr);
AR_S32 AR_MPI_DPU_RECT_EnableChn(AR_S32 DpuRectGrp, AR_S32 DpuRectChn);
AR_S32 AR_MPI_DPU_RECT_DisableChn(AR_S32 DpuRectGrp, AR_S32 DpuRectChn);
AR_S32 AR_MPI_DPU_RECT_SendFrame(AR_S32 DpuRectGrp, 
	const VIDEO_FRAME_INFO_S *pstLeftFrame, const VIDEO_FRAME_INFO_S *pstRightFrame, AR_S32 s32MilliSec);
AR_S32 AR_MPI_DPU_RECT_GetFrame(AR_S32 DpuRectGrp, 
	VIDEO_FRAME_INFO_S *pstSrcLeftFrame,VIDEO_FRAME_INFO_S *pstSrcRightFrame,
	VIDEO_FRAME_INFO_S *pstDstLeftFrame, VIDEO_FRAME_INFO_S *pstDstRightFrame, AR_S32 s32MilliSec);
AR_S32 AR_MPI_DPU_RECT_ReleaseFrame(AR_S32 DpuRectGrp, 
	const VIDEO_FRAME_INFO_S *pstSrcLeftFrame, const VIDEO_FRAME_INFO_S *pstSrcRightFrame, 
	const VIDEO_FRAME_INFO_S *pstDstLeftFrame, const VIDEO_FRAME_INFO_S *pstDstRightFrame);
AR_S32 AR_MPI_GDC_ADV_Init(AR_VOID);
AR_S32 AR_MPI_GDC_DeInit(AR_VOID);
#endif
AR_S32 AR_MPI_GDC_GetMMZMemory(AR_U64* u64PhyAddr, void ** pvVirtAddr, AR_CHAR* pstr_mmb, AR_U32 u32Size);
AR_VOID AR_MPI_GDC_ReleaseMMZMemory(AR_U64 u64PhyAddr, void* pvVirtAddr);



#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */


#endif
