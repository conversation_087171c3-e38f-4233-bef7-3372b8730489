#ifndef __MPI_MVSCALER_API_H__
#define __MPI_MVSCALER_API_H__

#include "hal_mvscaler_api.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

AR_S32 AR_MPI_MVSCALER_Init();
AR_VOID AR_MPI_MVSCALER_Exit();
AR_S32 AR_MPI_MVSCALER_CropResize(AR_HAL_MVSCALER_PARAMS_S *pstMvParams);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */

#endif //__MPI_SCALER_H__

