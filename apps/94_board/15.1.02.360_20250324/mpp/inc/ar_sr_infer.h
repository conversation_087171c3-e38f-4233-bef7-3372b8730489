#ifndef __AR_CNN_INFERE_H__
#define __AR_CNN_INFERE_H__

#ifdef __cplusplus
extern "C"{
#endif

#include "hal_npu_types.h"
#include "mpi_npu_api.h"
#include "mpi_sys.h"

#define AR_ALIGNY(_x, _y)            (((_x)+((_y)-1))&~((_y)-1))
#define AR_SR_NET_NUM_MAX        4
typedef struct
{
	void *handle[AR_SR_NET_NUM_MAX]; 
} AR_CNN_SR_HANDLE_S;

void *AR_CNN_SR_Init();
AR_S32 AR_CNN_SR_DeInit(AR_CNN_SR_HANDLE_S *Handle);
AR_S32 AR_CNN_SR_Forward(AR_CNN_SR_HANDLE_S *Handle, AR_S32 s32NetId, AR_IMG_SET_S *pstImg, AR_MEM_S *pstIn, AR_MEM_S *pstOut);
AR_S32 AR_CNN_SR_PostProcess(AR_CNN_SR_HANDLE_S *Handle, AR_S32 s32NetId, AR_MEM_S *pstIn, AR_CHAR *pOut, AR_S32 u32AlgnUnit);
AR_S32 AR_CNN_SR_GetNetId(AR_CNN_SR_HANDLE_S *pstSrHandle, AR_U32 u32Width, AR_U32 u32Height, AR_U32 u32Scale);
AR_CHAR *AR_CNN_SR_GetLibVersion();
AR_S32 AR_CNN_SR_GetLibNetNum();
AR_S32 AR_CNN_SR_GetModelInfo(AR_CNN_SR_HANDLE_S *Handle, AR_U32 s32NetId,  AR_U32 *u32Width, AR_U32 *u32Height, AR_U32 *u32Scale, AR_U32*u32ModuleID);

#ifdef __cplusplus
}
#endif

#endif

