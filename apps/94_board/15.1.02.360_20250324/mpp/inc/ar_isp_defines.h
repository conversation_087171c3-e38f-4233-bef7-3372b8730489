#ifndef __AR_ISP_DEFINES_H__
#define __AR_ISP_DEFINES_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#define ISP_MAX_PIPE_NUM 8

#include "hal_vin_type_def.h"
#include "hal_vin_aec_algo_lib.h"
#include "hal_vin_awb_algo_lib.h"
#include "hal_vin_af_algo_lib.h"

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */

#endif /* __AR_ISP_DEFINES_H__ */
