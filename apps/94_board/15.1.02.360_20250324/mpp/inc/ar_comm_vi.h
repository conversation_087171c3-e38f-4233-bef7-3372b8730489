/**
 * \file
 * \brief 描述视频输入相关的通用数据结构
 */

#ifndef __AR_COMM_VI_H__
#define __AR_COMM_VI_H__

#include "ar_common.h"
#include "hal_errno.h"
#include "ar_comm_video.h"
#include "ar_comm_gdc.h"
#include "ar_comm_isp.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"
{
#endif /* __cplusplus */
#endif /* __cplusplus */

/********************************Macro Definition********************************/
/** \addtogroup      MPI_VI */
/** @{ */  /** <!-- [MPI_VI] */

#define VI_MAX_ADCHN_NUM            (4UL)       /**<VI设备属性中s32AdChnId的个数*/

#define VI_PMFCOEF_NUM              (9UL)       /**<@note 新添加参数。*/
#define VI_COMPMASK_NUM             (2UL)       /**<VI设备属性中Component mask的个数*/
#define VI_PRO_MAX_FRAME_NUM        (8UL)       /**<@note 新添加参数。*/
#define VI_SHARPEN_GAIN_NUM         32          /**<@note 新添加参数。*/
#define VI_AUTO_ISO_STRENGTH_NUM    16          /**<@note 新添加参数。*/

#define VI_INVALID_FRMRATE  (-1)                /**<VI帧率控制的宏，当原始帧率和目标帧率都为该宏时，不做帧率控制*/
#define VI_CHN0               0                 /**<@note 新添加参数。*/
#define VI_CHN1               1                 /**<@note 新添加参数。*/
#define VI_CHN2               2                 /**<@note 新添加参数。*/
#define VI_CHN3               3                 /**<@note 新添加参数。*/
#define VI_INVALID_CHN       -1                 /**<@note 新添加参数。*/

#define VI_MAX_VC_NUM         4                 /**<@note 新添加参数。*/

/** @} */  /** <!-- ==== Macro Definition end ==== */

/*************************** Structure Definition ****************************/
/** \addtogroup      MPI_VI */
/** @{ */  /** <!-- [MPI_VI] */

/**定义VI的工作模式*/
typedef enum
{
 VIN_CAMERA_NORMAL,                 /**<@note 新添加参数。*/
 VIN_CAMERA_OFFLINE,                /**<@note 新添加参数。*/
 VIN_CMAERA_MULTI_MODE,             /**<@note 新添加参数。*/
 VIN_CMAERA_TOOL_SIMULATION,             /**<@note 新添加参数。sensor 工作在可以由工具发送raw 图仿真*/
}ENUM_VIN_WORK_MODE_T;


/**VI的公共属性，包括VI的工作模式，各个模块的工作频率*/
typedef struct
{
	ENUM_VIN_WORK_MODE_T cam_mode;                /**<@note 新添加参数。VI的工作模式。*/
	AR_S32 vif_fre_mod;             /**<@note 新添加参数。vif时钟频率模式。0：自动模式；1：频率由vif_fre_hz指定，范围请参考soc 手册。*/
	AR_S32 vif_fre_hz;              /**<@note 新添加参数。vif时钟频率，单位为HZ。*/	
	AR_S32 pcs_fre_mod;             /**<@note 新添加参数。ocs时钟频率模式。0：自动模式；1：频率由pcs_fre_hz指定，范围请参考soc 手册。*/
	AR_S32 pcs_fre_hz;              /**<@note 新添加参数。pcs时钟频率，单位为HZ。9341 只有200M和333M，不是这两个值，默认为333M*/
	AR_S32 isp_fre_mod;             /**<@note 新添加参数。isp时钟频率模式。0：自动模式；1：频率由isp_fre_hz指定，范围请参考soc 手册。*/
	AR_S32 isp_fre_hz;              /**<@note 新添加参数。isp时钟频率，单位为HZ。*/
	AR_S32 hdr_fre_mod;             /**<@note 新添加参数。hdr时钟频率模式。0：自动模式；1：频率由hdr_fre_hz指定，范围请参考soc 手册。*/
	AR_S32 hdr_fre_hz;              /**<@note 新添加参数。hdr时钟频率，单位为HZ。*/
	AR_S32 dpvif_fre_hz;  /**<@note 新添加参数。dvpif时钟频率，单位为HZ。*/
	AR_S32 cvisp0_fre_hz;  /**<@note 新添加参数。cvisp时钟频率，单位为HZ。*/
	AR_S32 cvisp1_fre_hz; /**<@note 新添加参数。cvisp时钟频率，单位为HZ。*/	
	AR_S32 cvvif0_fre_hz;  /**<@note 新添加参数。cvvif时钟频率，单位为HZ。*/
	AR_S32 cvvif1_fre_hz; /**<@note 新添加参数。cvvif时钟频率，单位为HZ。*/
	ENUM_VIN_HW_BURST_T hw_burst;   /**<@note 新添加参数。主要指vif的hw burst */
	ENUM_VIN_HW_BURST_T isp_burst; /**<@note 新添加参数。主要指isp的hw burst */	
	ENUM_VIN_LTM_WOTK_MODE_T ltm_work_mode;  /// ltm 运行在大图模式
	ENUM_VIN_POWER_CLK_ACTION_T power_clk_action;/*本枚举量定义open vin 设备的时候，对电源做出的动作，*/
	AR_S32  scaler_smooth_fator; /*设置isp 输出缩放模块的平滑系数 1-256，0 使用默认的init lut*/
	AR_S32  scaler_sharp_fator;/*设置isp 输出缩放模块的锐化系数 1-256，0 使用默认的init lut scaler_smooth_fator 和 scaler_sharp_fator 都为0时使用默认lut，否则依据此参数生成 */
	ENUM_VIN_DRIVER_LOAD_MODE_T  load_vin_driver_mode;  /*禁止打开vin 设备的时候加载vin 驱动，以及禁止close vin 设备的时候unload vin 驱动。默认情况下会加载驱动，如果不想加载驱动，可以设置为VIN_DRIVER_LOAD_MODE_NO_LOAD
	，sdk0.17 以后，无论是app 模式或者是lib 模式，驱动都在open vin dev的时候加载，除非用这个标志设置不加载驱动。不再用脚本或者sys init 加载了*/
}VI_DEV_PROP_S;

/**定义VI通道低延时属性*/
typedef struct arVI_LOW_DELAY_INFO_S
{
    AR_BOOL bEnable;                /**<低延时使能开关。AR_FALSE：不使能；AR_TRUE：使能。*/
    AR_U32 u32LineCnt;              /**<@note 不使用。*/
}VI_LOW_DELAY_INFO_S;


/**此属性是计算大图左右或者上下2个分割的结果*/
typedef struct __STRU_TWO_SPLIT_T__
{
    // split input
    AR_S32 w_i;    /**< 输入的等待分割的宽度，如果是垂直分割，代表高度*/
    AR_S32 lr_w;   /**< 输入分割成两个图像后的宽度*/
    AR_S32 r_offset; /**< 分割为两个图像后，右边的图像的开始像素在整个图像中的偏移*/
    AR_S32 overlap;  /**< 输入图像的重合区像素数，指的是一半的重合，lr_w=w_i/2+overlap,对应tile属性中的overlap*/

    // crop for zoom
    AR_S32 l_crop;  /**<输入的分割后的左边的图像经过isp处理后，第一个crop的开始位置，这个个rcop 一般用来zoom*/
    AR_S32 lr_crop_w;/**<输入的分割后的左边的图像经过isp处理后，第一个crop的宽度*/
    AR_S32 r_crop;/**<输入的分割后的右边的图像经过isp处理后，第一个crop的宽度*/

    // resize
    AR_S32 w_resize;  /**<经过crop 后，图像开始做resize 处理，表示resize 后的宽度*/

    // out crop
    AR_S32 crop_x;  /**<输入的分割后的右边的图像经过resize后，第二个crop的开始位置，左边的开始位置始终是0，这个个rcop 用来切边，防止接缝*/
    AR_S32 crop_w; /**<输入的分割后的左右边的图像经过resize后，第二个crop的宽度，这个个rcop 用来切边，防止接缝*/
    AR_S32 r_o_offset; /**< 右边图像写入ddr和左边图像拼接的时候相对于左边图像的偏移像素数*/
	AR_S32 ltm_mesh;
} STRU_TWO_SPLIT_T;




/**定义VI PIPE的RAW数据压缩参数*/
typedef struct arVI_CMP_PARAM_S
{
	AR_U8  bEnable;                             /**<压缩使能。*/
    AR_U8  au8CmpParam[VI_CMP_PARAM_SIZE];      /**<@note 转义参数 后续实现可能会重定义。*/
} VI_CMP_PARAM_S;

/**用户图片类型*/
typedef enum ar_VI_USERPIC_MODE_E
{
    VI_USERPIC_MODE_PIC = 0,        /**<YUV图像模式。*/
    VI_USERPIC_MODE_BGC,            /**<纯色背景图像模式。*/
    VI_USERPIC_MODE_BUTT,
} VI_USERPIC_MODE_E;

/**纯色背景模式下的用户图片相关信息*/
typedef struct arVI_USERPIC_BGC_S
{
    AR_U32          u32BgColor;     /**<填充数据，与颜色的RGB值对应。取值范围：[0, 0xFFFFFF]。*/
} VI_USERPIC_BGC_S;

/**用户图片信息*/
typedef struct arVI_USERPIC_ATTR_S
{
    VI_USERPIC_MODE_E       enUsrPicMode;       /**<用户图片模式。*/
    union
    {
        VIDEO_FRAME_INFO_S  stUsrPicFrm;        /**<YUV图像模式下的用户图片信息。*/
        VI_USERPIC_BGC_S    stUsrPicBg;         /**<纯色背景模式下的用户图片信息。*/
    } unUsrPic;
} VI_USERPIC_ATTR_S;

typedef enum arEN_VI_ERR_CODE_E
{
    ERR_VI_FAILED_NOTENABLE = 64,   /**<@note 新添加参数。device or channel not enable*/
    ERR_VI_FAILED_NOTDISABLE,       /**<@note 新添加参数。device not disable*/
    ERR_VI_FAILED_CHNOTDISABLE,     /**<@note 新添加参数。channel not disable*/
    ERR_VI_CFG_TIMEOUT,             /**<@note 新添加参数。config timeout*/
    ERR_VI_NORM_UNMATCH,            /**<@note 新添加参数。video norm of ADC and VIU is unmatch*/
    ERR_VI_INVALID_WAYID,           /**<@note 新添加参数。invlalid way ID*/
    ERR_VI_INVALID_PHYCHNID,        /**<@note 新添加参数。invalid phychn id*/
    ERR_VI_FAILED_NOTBIND,          /**<@note 新添加参数。device or channel not bind */
    ERR_VI_FAILED_BINDED,           /**<@note 新添加参数。device or channel not unbind */
    ERR_VI_DIS_PROCESS_FAIL         /**<@note 新添加参数。dis process failed */
} EN_VI_ERR_CODE_E;

#define AR_ERR_VI_INVALID_PARA          AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_ILLEGAL_PARAM)        /**<错误码：视频输入参数设置无效*/
#define AR_ERR_VI_INVALID_DEVID         AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_DEVID)        /**<错误码：视频输入设备号无效*/
#define AR_ERR_VI_INVALID_PIPEID        AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_PIPEID)       /**<错误码：PIPE号无效*/
#define AR_ERR_VI_INVALID_STITCHGRPID   AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_STITCHGRPID)  /**<错误码：拼接组无效*/
#define AR_ERR_VI_INVALID_CHNID         AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_CHNID)        /**<错误码：视频输入通道号无效*/
#define AR_ERR_VI_INVALID_NULL_PTR      AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_NULL_PTR)             /**<错误码：输入参数空指针错误*/
#define AR_ERR_VI_FAILED_NOTCONFIG      AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_CONFIG)           /**<错误码：视频设备或通道属性未配置*/
#define AR_ERR_VI_SYS_NOTREADY          AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_SYS_NOTREADY)         /**<错误码：视频输入系统未初始化*/
#define AR_ERR_VI_BUF_EMPTY             AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUF_EMPTY)            /**<错误码：视频输入缓存为空*/
#define AR_ERR_VI_BUF_FULL              AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUF_FULL)             /**<错误码：视频输入缓存为满*/
#define AR_ERR_VI_NOMEM                 AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOMEM)                /**<错误码：分配内存失败*/
#define AR_ERR_VI_NOT_SUPPORT           AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_SUPPORT)          /**<错误码：操作不支持*/
#define AR_ERR_VI_BUSY                  AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUSY)                 /**<错误码：视频输入系统忙*/
#define AR_ERR_VI_NOT_PERM              AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_PERM)             /**<错误码：操作不允许*/

#define AR_ERR_VI_FAILED_NOTENABLE      AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_FAILED_NOTENABLE)      /**<错误码：视频输入设备未启用*/
#define AR_ERR_VI_FAILED_NOTDISABLE     AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_FAILED_NOTDISABLE)     /**<错误码：视频输入设备未禁用*/
#define AR_ERR_VI_FAILED_CHNOTDISABLE   AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_FAILED_CHNOTDISABLE)   /**<错误码：视频输入通道未禁用*/
#define AR_ERR_VI_CFG_TIMEOUT           AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_CFG_TIMEOUT)           /**<错误码：视频配置属性超时*/
#define AR_ERR_VI_NORM_UNMATCH          AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_NORM_UNMATCH)          /**<错误码：不匹配*/
#define AR_ERR_VI_INVALID_WAYID         AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_INVALID_WAYID)         /**<错误码：视频通路号无效*/
#define AR_ERR_VI_INVALID_PHYCHNID      AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_INVALID_PHYCHNID)      /**<@note 新添加参数。错误码：物理通道号无效*/
#define AR_ERR_VI_FAILED_NOTBIND        AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_FAILED_NOTBIND)        /**<错误码：视频通道未绑定*/
#define AR_ERR_VI_FAILED_BINDED         AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, ERR_VI_FAILED_BINDED)         /**<错误码：视频通道已绑定*/

#define AR_ERR_VI_PIPE_EXIST            AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_EXIST)                /**<错误码：PIPE已存在*/
#define AR_ERR_VI_PIPE_UNEXIST          AR_MPP_DEF_ERR(AR_ID_VI, HAL_ERR_LEVEL_ERROR, HAL_ERR_UNEXIST)              /**<错误码：PIPE不存在*/


/**定义视频设备的接口模式*/
typedef enum arVI_INTF_MODE_E
{
    VI_MODE_BT656 = 0,              /**<输入数据的协议符合标准BT.656协议，端口数据输入模式为亮度色度分离模式，分量模式为单分量。*/
    VI_MODE_BT601,                  /**<DVP 外同步*/
    VI_MODE_DIGITAL_CAMERA,         /**<输入数据的协议为Digital camera协议，端口数据输入模式为亮度色度复合模式，分量模式为单分量。*/
    VI_MODE_BT1120_STANDARD,        /**<输入数据的协议符合标准BT.1120协议（BT656+双分量），端口数据输入模式为亮度色度分离模式，分量模式为双分量。*/
    VI_MODE_BT1120_INTERLEAVED,     /**<输入数据的协议符合BT.1120 interleave模式，端口数据输入模式为亮度色度分离模式，分量模式为双分量。*/
    VI_MODE_MIPI,                   /**<输入数据符合MIPI协议，用于传输RAW数据。*/
    VI_MODE_MIPI_YUV420_NORMAL,     /**<输入数据符合MIPI协议，用于传输YUV420 normal模式的数据。*/
    VI_MODE_MIPI_YUV420_LEGACY,     /**<输入数据符合MIPI协议，用于传输YUV420 legacy模式的数据。*/
    VI_MODE_MIPI_YUV422,            /**<输入数据符合MIPI协议，用于传输YUV422数据。*/
    VI_MODE_LVDS,                   /**<输入数据符合LVDS 协议。*/
    VI_MODE_HISPI,                  /**<输入数据符合HISPI协议。*/
    VI_MODE_SLVS,                   /**<输入数据符合SLVS-EC协议。*/
	VI_MODE_MEM,                    /**<从内存中取数据，需要用户调用相关api 推入到vin*/
	VI_MODE_DP,                     /**<输入接口是displayport，9411 支持。*/
    VI_MODE_BUTT
} VI_INTF_MODE_E;


/**定义视频设备的输入模式*/
typedef enum arVI_INPUT_MODE_E
{
    VI_INPUT_MODE_BT656 = 0,        /**<输入数据的协议符合标准BT.656协议。*/
    VI_INPUT_MODE_BT601,            /**<输入数据的协议符合标准BT.601协议。*/
    VI_INPUT_MODE_DIGITAL_CAMERA,   /**<输入数据的协议为Digital camera协议。*/
    VI_INPUT_MODE_INTERLEAVED,      /**<输入数据的协议为BT1120 interleave。*/
    VI_INPUT_MODE_MIPI,             /**<输入数据符合MIPI协议。*/
    VI_INPUT_MODE_LVDS,             /**<输入数据符合LVDS协议。*/
    VI_INPUT_MODE_HISPI,            /**<输入数据符合HISPI协议。*/
    VI_INPUT_MODE_SLVS,             /**<输入数据符合SLVS-EC协议。*/

    VI_INPUT_MODE_BUTT
} VI_INPUT_MODE_E;


/**定义视频设备的复合工作模式*/
typedef enum arVI_WORK_MODE_E
{
    VI_WORK_MODE_1Multiplex = 0,    /**<1路复合工作模式。*/
    VI_WORK_MODE_2Multiplex,        /**<2路复合工作模式，输入数据的协议必须为标准BT.656协议。*/
    VI_WORK_MODE_3Multiplex,        /**<3路复合工作模式，输入数据的协议必须为标准BT.656协议。*/
    VI_WORK_MODE_4Multiplex,        /**<4路复合工作模式，输入数据的协议必须为标准BT.656协议。*/
	VI_WORK_MODE_2Split,             /**<@note 新加参数，表示输出被分割成两个图像，只在dp接口的时候使用，其他模式不生效*/
    VI_WORK_MODE_BUTT
} VI_WORK_MODE_E;

/**定义视频设备接收的是隔行或逐行输入图像*/
typedef enum arVI_SCAN_MODE_E
{
    VI_SCAN_INTERLACED  = 0,        /**<VI输入为隔行图像。*/
    VI_SCAN_PROGRESSIVE,            /**<VI输入为逐行图像。*/

    VI_SCAN_BUTT
} VI_SCAN_MODE_E;

/**定义视频设备接收的YUV数据的数据排列顺序*/
typedef enum arVI_YUV_DATA_SEQ_E
{
    VI_DATA_SEQ_VUVU = 0,           /**<YUV 数据通过分离模式输入时，C分量的输入排列顺序为VUVU。*/
    VI_DATA_SEQ_UVUV,               /**<YUV 数据通过分离模式输入时，C分量的输入排列顺序为UVUV。*/

    VI_DATA_SEQ_UYVY,               /**<YUV 数据通过复合模式输入时，顺序为UYVY。*/
    VI_DATA_SEQ_VYUY,               /**<YUV 数据通过复合模式输入时，顺序为VYUY。*/
    VI_DATA_SEQ_YUYV,               /**<YUV 数据通过复合模式输入时，顺序为YUYV。*/
    VI_DATA_SEQ_YVYU,               /**<YUV 数据通过复合模式输入时，顺序为YVYU。*/

    VI_DATA_SEQ_BUTT
} VI_YUV_DATA_SEQ_E;

/**定义视频设备接收的时钟类型*/
typedef enum arVI_CLK_EDGE_E
{
    VI_CLK_EDGE_SINGLE_UP = 0,      /**<时钟单沿模式，且VI设备在上升沿采样。*/
    VI_CLK_EDGE_SINGLE_DOWN,        /**<时钟单沿模式，且VI设备在下降沿采样。*/

    VI_CLK_EDGE_BUTT
} VI_CLK_EDGE_E;

/**定义视频设备接收的数据的分量类型*/
typedef enum arVI_COMPONENT_MODE_E
{
    VI_COMPONENT_MODE_SINGLE = 0,   /**<输入数据为单分量。*/
    VI_COMPONENT_MODE_DOUBLE,       /**<输入数据为双分量。*/

    VI_COMPONENT_MODE_BUTT
} VI_COMPONENT_MODE_E;

/**定义视频设备接收的数据是复合还是分离模式*/
typedef enum arVI_COMBINE_MODE_E
{
    VI_COMBINE_COMPOSITE = 0,       /**<复合模式。*/
    VI_COMBINE_SEPARATE,            /**<分离模式。*/

    VI_COMBINE_BUTT
} VI_COMBINE_MODE_E;

/**定义视频设备输入数据的垂直同步信号类型*/
typedef enum arVI_VSYNC_E
{
    VI_VSYNC_FIELD = 0,             /**<垂直同步翻转模式，即翻转一次表示一场。BT.601模式下表示场号，DC模式下表示行有效信号。*/
    VI_VSYNC_PULSE,                 /**<垂直同步脉冲模式，即一个脉冲到来表示新的一帧或一场。*/

    VI_VSYNC_BUTT
} VI_VSYNC_E;

/**定义视频设备输入数据垂直同步信号的极性*/
typedef enum arVI_VSYNC_NEG_E
{
    VI_VSYNC_NEG_HIGH = 0,          /**<若VI_VSYNC_E = VI_VSYNC_FIELD，则表示偶数场的vsync信号为高电平；若VI_VSYNC_E = VI_VSYNC_PULSE，则表示vsync同步脉冲为正脉冲。*/
    VI_VSYNC_NEG_LOW,               /**<若VI_VSYNC_E = VI_VSYNC_FIELD，则表示偶数场的vsync信号为低电平；若VI_VSYNC_E = VI_VSYNC_PULSE，则表示vsync同步脉冲为负脉冲。*/

    VI_VSYNC_NEG_BUTT
} VI_VSYNC_NEG_E;

/**定义视频设备输入数据的水平同步信号类型*/
typedef enum arVI_HSYNC_E
{
    VI_HSYNC_VALID_SINGNAL = 0,     /**<水平同步数据有效信号。*/
    VI_HSYNC_PULSE,                 /**<水平同步脉冲信号。*/

    VI_HSYNC_BUTT
} VI_HSYNC_E;

/**定义视频设备输入数据水平同步信号的极性*/
typedef enum arVI_HSYNC_NEG_E
{
    VI_HSYNC_NEG_HIGH = 0,          /**<若VI_HSYNC_E = VI_HSYNC_VALID_SINGNAL，则高电平表示有效数据；若VI_HSYNC_E = VI_HSYNC_PULSE，则正脉冲表示同步脉冲。*/
    VI_HSYNC_NEG_LOW,               /**<若VI_HSYNC_E = VI_HSYNC_VALID_SINGNAL，则低电平表示有效数据；若VI_HSYNC_E = VI_HSYNC_PULSE，则负脉冲表示同步脉冲。*/

    VI_HSYNC_NEG_BUTT
} VI_HSYNC_NEG_E;

/**定义视频设备输入数据的垂直有效同步信号类型*/
typedef enum arVI_VSYNC_VALID_E
{
    VI_VSYNC_NORM_PULSE = 0,        /**<表示垂直有效同步标识。*/
    VI_VSYNC_VALID_SINGAL,          /**<表示垂直同步时序行有效信号。*/

    VI_VSYNC_VALID_BUTT
} VI_VSYNC_VALID_E;

/**定义视频设备输入数据的垂直有效同步信号类型*/
typedef enum arVI_VSYNC_VALID_NEG_E
{
    VI_VSYNC_VALID_NEG_HIGH = 0,    /**<若VI_VSYNC_VALID_E = VI_VSYNC_NORM_SINGAL，则表示高电平为有效信号。*/
    VI_VSYNC_VALID_NEG_LOW,         /**<若 VI_VSYNC_VALID_E = VI_VSYNC_NORM_SINGAL，则表示低电平为有效信号。*/

    VI_VSYNC_VALID_NEG_BUTT
} VI_VSYNC_VALID_NEG_E;

/**定义视频设备输入数据的DE信号类型*/
typedef enum arVI_DE_E
{
    VI_DE_PIXEL_IN = 0,             /**<@note 新添加参数。*/
    VI_DE_HSYNC_IN,                 /**<@note 新添加参数。*/
    VI_DE_LOW_HVSYNC,               /**<@note 新添加参数。*/
    VI_DE_NO_DE,                    /**<@note 新添加参数。*/

    VI_DE_BUTT
} VI_DE_E;

/**定义视频设备输入数据的DE信号极性*/
typedef enum arVI_DE_NEG_E
{
    VI_DE_NEG_HIGH = 0,    /**<表示高电平为有效信号。*/
    VI_DE_NEG_LOW,         /**<表示低电平为有效信号。*/

    VI_DE_NEG_BUTT
} VI_DE_NEG_E;


/**定义视频设备输入时序的消隐信息*/
typedef struct arVI_TIMING_BLANK_S
{
    AR_U32 u32HsyncHfb ;            /**<水平前消隐区宽度。*/
    AR_U32 u32HsyncAct ;            /**<水平有效宽度。*/
    AR_U32 u32HsyncHbb ;            /**<水平后消隐区宽度*/
    AR_U32 u32VsyncVfb ;            /**<帧图像或隔行输入时奇场图像的垂直前消隐区高度。*/
    AR_U32 u32VsyncVact ;           /**<帧图像或隔行输入时奇场垂直有效高度。*/
    AR_U32 u32VsyncVbb ;            /**<帧图像或隔行输入时奇场垂直后消隐区高度。*/
    AR_U32 u32VsyncVbfb ;           /**<隔行输入时偶场垂直前消隐区高度（帧输入时无效）。*/
    AR_U32 u32VsyncVbact ;          /**<隔行输入时偶场垂直有效高度（帧输入时无效）。*/
    AR_U32 u32VsyncVbbb ;           /**<隔行输入时偶场垂直后消隐区高度（帧输入时无效）。*/
} VI_TIMING_BLANK_S;

/**定义视频设备接收BT.601、DC、BT.656和BT.1120时序的同步信息*/
typedef struct arVI_SYNC_CFG_S
{
    VI_VSYNC_E              enVsync;            /**<垂直同步信号类型。*/
    VI_VSYNC_NEG_E          enVsyncNeg;         /**<垂直同步信号的极性。*/
    VI_HSYNC_E              enHsync;            /**<水平同步信号类型。*/
    VI_HSYNC_NEG_E          enHsyncNeg;         /**<水平同步信号的极性。*/
    VI_VSYNC_VALID_E        enVsyncValid;       /**<垂直有效同步信号类型。*/
    VI_VSYNC_VALID_NEG_E    enVsyncValidNeg;    /**<垂直有效同步信号的极性。*/
    VI_TIMING_BLANK_S       stTimingBlank;      /**<输入时序的消隐信息。*/
    VI_DE_E                 enDE;               /**<@note 新添加参数。DE信号类型。*/
    VI_DE_NEG_E             enDENeg;            /**<@note 新添加参数。DE信号极性。*/
} VI_SYNC_CFG_S;

/**定义BT.656定时基准码最高bit配置*/
typedef enum arVI_BT656_FIXCODE_E
{
    VI_BT656_FIXCODE_1 = 0,         /**<BT.656协议的EAV/SAV最高bit固定为1。*/
    VI_BT656_FIXCODE_0,             /**<BT.656协议的EAV/SAV最高bit固定为0。*/

    VI_BT656_FIXCODE_BUTT
} VI_BT656_FIXCODE_E;

/**BT.656定时基准码场指示位（F）极性*/
typedef enum arVI_BT656_FIELD_POLAR_E
{
    VI_BT656_FIELD_POLAR_STD = 0,   /**<标准模式，第一场F=0，第二场F=1。*/
    VI_BT656_FIELD_POLAR_NSTD,      /**<非标准模式，第一场F=1，第二场F=0。*/

    VI_BT656_FIELD_POLAR_BUTT
} VI_BT656_FIELD_POLAR_E;

/**定义视频设备接收的BT.656时序的同步信息*/
typedef struct arVI_BT656_SYNC_CFG_S
{
    VI_BT656_FIXCODE_E     enFixCode;           /**<BT.656定时基准码最高bit配置。*/
    VI_BT656_FIELD_POLAR_E enFieldPolar;        /**<BT.656定时基准码场指示位（F）极性。*/
} VI_BT656_SYNC_CFG_S;

/**VI输入数据类型枚举*/
typedef enum arVI_DATA_TYPE_E
{
    VI_DATA_TYPE_YUV = 0,           /**<输入数据类型为YUV，VI前端一般接的是AD。*/
    VI_DATA_TYPE_RGB,               /**<输入数据类型为RGB，VI前端一般接的是Sensor。*/
    VI_DATA_TYPE_YUV_444,    
    VI_DATA_TYPE_YUV_420,
    VI_DATA_TYPE_BUTT
} VI_DATA_TYPE_E;

/**定义bayer域相位调整类型*/
typedef enum arVI_REPHASE_MODE_E
{
    VI_REPHASE_MODE_NONE = 0,       /**<不进行相位调整。*/
    VI_REPHASE_MODE_SKIP_1_2,       /**<进行SKIP1/2类型的调整。*/
    VI_REPHASE_MODE_SKIP_1_3,       /**<进行SKIP1/3类型的调整。*/
    VI_REPHASE_MODE_BINNING_1_2,    /**<进行BINNING1/2类型的调整。*/
    VI_REPHASE_MODE_BINNING_1_3,    /**<进行BINNING1/3类型的调整。*/

    VI_REPHASE_MODE_BUTT
} VI_REPHASE_MODE_E;

/**定义bayer域相位调整属性*/
typedef struct arVI_BAS_REPHASE_ATTR_S
{
    VI_REPHASE_MODE_E   enHRephaseMode;         /**<水平相位调整类型。*/
    VI_REPHASE_MODE_E   enVRephaseMode;         /**<垂直相位调整类型。*/
} VI_BAS_REPHASE_ATTR_S;

/**定义bayer域缩放属性*/
typedef struct arVI_BAS_SCALE_ATTR_S
{
    SIZE_S      stBasSize;          /**<缩放之后的宽与高。*/
} VI_BAS_SCALE_ATTR_S;

/**定义bayer域属性*/
typedef struct arVI_BAS_ATTR_S
{
    VI_BAS_SCALE_ATTR_S     stSacleAttr;        /**<Bayer域缩放属性。*/
    VI_BAS_REPHASE_ATTR_S   stRephaseAttr;      /**<Bayer域相位调整属性。*/
} VI_BAS_ATTR_S;

/**定义WDR参数数据信息*/
typedef struct arVI_WDR_ATTR_S
{
    WDR_MODE_E  enWDRMode;          /**<WDR工作模式，分为帧模式、行模式、非WDR等三大类。*/
    AR_U32      u32CacheLine;       /**<在线行模式WDR，离线PIPE数据缓存的行数。取值范围为[1，PIPE的图像高度u32MaxH]。根据带宽的使用情况和sensor的曝光行差等因素来调节到合适值。*/
} VI_WDR_ATTR_S;


/**定义视频输入设备的属性*/
typedef struct arVI_DEV_ATTR_S
{
    VI_INTF_MODE_E      enIntfMode;                             /**<@note 转义参数 接口模式。*/
    VI_WORK_MODE_E      enWorkMode;                             /**<复合工作模式，使用虚拟通道进行复合。*/

    AR_U32              au32ComponentMask[VI_COMPMASK_NUM];     /**<@note 转义参数 分量掩码配置。*/
    VI_SCAN_MODE_E      enScanMode;                             /**<输入扫描模式 (逐行、隔行)。*/
    AR_S32              as32AdChnId[VI_MAX_ADCHN_NUM];          /**<@note不使用参数 取值范围[-1, 3]，推荐统一设置为默认值-1，此参数无意义。*/

    VI_YUV_DATA_SEQ_E   enDataSeq;                              /**<@note 转义参数 输入数据顺序。*/
    VI_SYNC_CFG_S       stSynCfg;                               /**<@note 转义参数 同步时序配置，请参考dvp sensor 点亮文档。*/

    VI_DATA_TYPE_E      enInputDataType;                        /**<输入数据类型，Sensor输入一般为RGB，AD输入一般为YUV。*/

    AR_BOOL             bDataReverse;                           /**<@note不使用参数 因为走线约束等硬件原因，有可能出现AD/Sensor的数据线与VI数据线连接数据高低位反接。当AD/Sensor管脚与VI管脚正向连接时，取bDataReverse = AR_FALSE；当反向连接时，取bDataReverse = AR_TRUE。*/

    SIZE_S              stSize;                                 /**<VI设备可设置要捕获图像的高宽。*/

    VI_BAS_ATTR_S       stBasAttr;                              /**<@note不使用参数 Bayer域缩放之后的宽、高，以及相位调整的类型。*/

    VI_WDR_ATTR_S       stWDRAttr;                              /**<WDR属性。*/

    DATA_RATE_E         enDataRate;                             /**<设备的速率。*/
    AR_BOOL             bDvpHighLowByteSwap;                    /**<@note新加参数 DVP数据线高低8位交换。*/

    AR_BOOL             bIrSensor;                              /**<@note新加参数 是否为IR senosr。*/
    AR_BOOL             bDvpInputFromNuc;                       /**<@note新加参数 DVP输入是否来自于NUC。该参数主要用于艾睿红外sensor，这种sensor不是直接走DVP输出，而是由NUC模块接收sensor输出，再转换为DVP信号。*/
    AR_BOOL             bIrDataInverse;                         /**<@note新加参数 红外数据黑白热转换。*/
	AR_BOOL             bForceBig;                              /**<@note新加参数，1:强制启动大图模式 0:自动确定大图模式 2:强制非大图模式，用于单独的获取大图raw*/
	AR_BOOL             bPdaf;                                  /**>@note新加参数，是否启动sensor的pdaf 功能*/
} VI_DEV_ATTR_S;

/**<因为94xx isp的能力只能处理2688的图像，为了处理4k，需要把raw图分割成左右两部分，从而引申出大图模式的概念*/
typedef struct arVI_DEV_EXT_ATTR_S
{
   AR_S32 s32OverLapPixNum;/**<大图模式下，输出图像重合区的像素数，一般取32*/
   AR_S32 s32DownScalerRation; /**< if<=0.1x，nor 2x 4x 6x 8x*/
   AR_S32 s32OutADDRAlign;
   AR_S32 s32ZoomScalerEn;  /**< 大图模式下，使能此feature 后，ltm 会被关闭，这时可以使用isp的zoom和scaler 功能*/
   AR_S32 s32LtmMesh;
}VI_DEV_EXT_ATTR_S;


/**定义VI DEV与PIPE的绑定关系*/
typedef struct arVI_DEV_BIND_PIPE_S
{
    AR_U32  u32Num;                             /**<该VI Dev所绑定的PIPE数目，取值范围[1, VI_MAX_PIPE_NUM]。*/
    VI_PIPE PipeId[VI_MAX_PHY_PIPE_NUM];        /**<该VI Dev绑定的PIPE号。*/
} VI_DEV_BIND_PIPE_S;

/**定义VI PIPE的3DNR参考帧来源选择*/
typedef enum arVI_NR_REF_SOURCE_E
{
    VI_NR_REF_FROM_RFR = 0,         /**<重构帧作为参考帧。*/
    VI_NR_REF_FROM_CHN0,            /**<0通道输出作为参考帧。*/

    VI_NR_REF_FROM_BUTT
} VI_NR_REF_SOURCE_E;

/**定义VI PIPE的Bypass模式*/
typedef enum arVI_PIPE_BYPASS_MODE_E
{
    VI_PIPE_BYPASS_NONE,            /**<VI的数据经过FE与BE处理。*/
    VI_PIPE_BYPASS_FE,              /**<VI的数据不经过FE处理，只经过BE处理。*/
    VI_PIPE_BYPASS_BE,              /**<VI的数据经过FE处理，不经过BE处理。*/

    VI_PIPE_BYPASS_BUTT
} VI_PIPE_BYPASS_MODE_E;

/**定义VI PIPE的3DNR属性*/
typedef struct arVI_NR_ATTR_S
{
    PIXEL_FORMAT_E      enPixFmt;               /**<@note 不使用 重构帧的像素格式。*/
    DATA_BITWIDTH_E     enBitWidth;             /**<@note 不使用 重构帧的bit位宽。*/
    VI_NR_REF_SOURCE_E  enNrRefSource;          /**<@note 不使用 参考帧来源选择。*/
    COMPRESS_MODE_E     enCompressMode;         /**<note 转义参数 重构帧是否压缩。*/
} VI_NR_ATTR_S;


typedef enum arVI_PIPE_FEATURE_E
{
    FEA_AR_MPI_ISP_GetVDTimeOut=1<<0,
	FEA_AR_MPI_ISP_GetAEStatistics=1<<1,
	FEA_AR_MPI_ISP_GetWBStatistics=1<<2,
	FEA_AR_MPI_ISP_GetFocusStatistics=1<<3,
	FEA_AR_MPI_ISP_GetIrStatistics=1<<4,
	FEA_AR_MPI_DP_GetDPPFTimeOut=1<<5,
	FEA_AR_MPI_VIF_GETVIFPFTimeOut=1<<6,
}VI_PIPE_FEATURE_E;

typedef enum arVI_NOCPM_ENABLE_E
{
   VIN_PIPE_NOCPM_DISABLE=0,
   VIN_PIPE_NOCPM_ENABLE=0X12EDB48C,
}VI_NOCPM_ENABLE_E;


typedef enum arVI_ISP_TYPE_E
{
   VIN_PIPE_ISP_TYPE_NORMAL_ISP=0,   /**<@note 普通isp0。*/	
   VIN_PIPE_ISP_TYPE_NORMAL_ISP1,    /**<@note 普通isp1，不支持*/	
   VIN_PIPE_ISP_TYPE_NORMAL_ISP2,    /**<@note 普通isp2，不支持*/	
   VIN_PIPE_ISP_TYPE_NORMAL_ISP3,    /**<@note 普通isp3，不支持*/	
   VIN_PIPE_ISP_TYPE_CV_ISP,        /**<@note，cvisp，94xx 支持*/	
   VIN_PIPE_ISP_TYPE_BULT=0XFFFFFFFF,
}VI_ISP_TYPE_E;

typedef struct {
    AR_S32 s32TileEn;   /**<是否使能tile 功能，也就是分片处理功能*/
    AR_U32 u32TileWidth; /**< 分片后，每一片的宽度，比如1920 水平分两片，那么就是 960*/
    AR_U32 u32TileHeight; /**<分片后，每一篇的高度，比如1080，垂直分2片，那么就是540*/
    AR_U32 u32TileOverlapX; /**<分片后，x 方向分片的重合像素*/
    AR_U32 u32TileOverlapY; /**<分片后，y方向上的重合像素*/
	AR_U32 u32TileCntX;	
	AR_U32 u32TileCntY;
    SIZE_S stWholeSize; /**<不分片的情况下，raw 图的wh*/
    RECT_S stWholeRect; /**<不分片的情况下，isp 的crop roi*/	
	AR_U32               offsetRaw[4][4]; /**<每一片的raw 相对于首地址方向的偏移*/	
	AR_U32               s32TileFrmX[4][4];	 /**<每一片的raw x 坐标*/	
	AR_U32               s32TileFrmY[4][4];    /**<每一片的raw y 坐标*/	
	AR_U32 ltm_mesh;
} VI_PIPE_TILE_ATTR;

/**定义VI PIPE属性*/
typedef struct arVI_PIPE_ATTR_S
{
    VI_PIPE_BYPASS_MODE_E enPipeBypassMode;     /**<@note 不使用 VI PIPE的Bypass模式。静态属性，创建PIPE时设定，不可更改。*/
    AR_BOOL               bYuvSkip;             /**<@note 不使用 是否关闭下采样和CSC。AR_FALSE：yuv skip 不使能；AR_TRUE：yuv skip 使能。静态属性，创建PIPE时设定，不可更改。*/
    AR_BOOL               bIspBypass;           /**<@note 不使用 ISP是否bypass。AR_FALSE：ISP正常运行；AR_TRUE：ISP bypass，不运行ISP。静态属性，创建PIPE时设定，不可更改。*/
    AR_U32                u32MaxW;              /**<输入图像宽度。静态属性，创建PIPE时设定，不可更改。*/
    AR_U32                u32MaxH;              /**<输入图像高度。静态属性，创建PIPE 时设定，不可更改。*/
    PIXEL_FORMAT_E        enPixFmt;             /**<@指示pipe的像素格式。一般指示raw的格式*/
    COMPRESS_MODE_E       enCompressMode;       /**<@note 转义参数     数据压缩格式。*/
    DATA_BITWIDTH_E       enBitWidth;           /**<@note 转义参数 输入图像的bit位宽。静态属性，创建PIPE时设定，不可更改*/
    AR_BOOL               bNrEn;                /**<@note 不使用 NR使能开关。AR_FALSE：不使能；AR_TRUE：使能。*/
    VI_NR_ATTR_S          stNrAttr;             /**<@note 转义参数 NR属性结构体。静态属性，创建PIPE时设定，不可更改。*/
    AR_BOOL               bSharpenEn;           /**<@note 不使用 Sharpen使能开关。*/
    FRAME_RATE_CTRL_S     stFrameRate;          /**<@note 不使用 帧率控制。*/
    AR_BOOL               bDiscardProPic;       /**<@note 不使用 视频通路是否丢弃长曝光的帧。*/
	ENUM_VFE_MODE         enVfeMode;            /**<@note 新添加参数 前处理模式，主要支持ceva hdr，ai isp 功能。*/
	AR_S32                s32Trigger;          /**<@note 新添加参数,是否需要用户调用api  触发出图，只针对实际的sensor */
	AR_S32                s32EnableManAecUpdate; /**<@note 新添加参数,是否使能手动的更新aec，一般用在大图模式下的aec手动运行的情况 */
	AR_S32                s32EnableManAwbUpdate; /**<@note 新添加参数,是否使能手动的更新awb，一般用在大图模式下的awb手动运行的情况*/
	AR_S32                s32EnableManAfUpdate; /**<@note 新添加参数,是否使能手动的更新af，一般用在大图模式下的af手动运行的情况 */	
	AR_S32                s32enableUsrAec;/**<@note 新添加参数,是否使能使用用户直接的aec，屏蔽掉系统aec */	
	AR_S32                s32enableUsrAwb;/**<@note 新添加参数,是否使能使用用户直接的awb，屏蔽掉系统awb */	
	AR_S32                s32enableUsrAf;/**<@note 新添加参数,是否使能使用用户直接的af，屏蔽掉系统af */	
	AR_S32                s32AwbRation;/**<@note 新添加参数,awb的调整率，用来降低awb 的计算频率，从而节省cpu 消耗*/	
	AR_U32                u32FeatureMask; /**<@note 新添加参数,使能某些pipe 功能, VI_PIPE_FEATURE_E*/
	VI_NOCPM_ENABLE_E     enEnableNoCmp; /**<@note 新添加参数,使能pipe的非压缩功能，如果不使能非压缩，即使enCompressMode 配置为非压缩，系统仍然使用压缩功能*/	
	AR_S32                s32AecRation;/**<@note 新添加参数,aec的调整率，用来降低aec 的计算频率，从而节省cpu 消耗,需要配合sensor驱动的特性，aec 算法调试参数保证aec 收敛速度满足需求*/	
	VI_ISP_TYPE_E         enIspType;     /**<@note 新添加参数,选择isp的类型*/
	AR_S32                s32Mirror;     /**<@note 新添加参数,ISP mirro 属性，1 表示打开isp，mirro，0 表示关闭isp mirror*/	
	AR_S32                s32enableUsrLtmAlgo; /**<@note 新添加参数,屏蔽掉系统的ltm 算法，使用用户自己的ltm 算法*/
	AR_S32 single_mode; // isp工作在单次模式
    VI_PIPE_TILE_ATTR     stTileAttr;/**<@note 新添加参数,分块属性*/	
	AR_S32                s32LtmRation;/**<@note 新添加参数,aec的调整率，用来降低aec 的计算频率，从而节省cpu 消耗,需要配合sensor驱动的特性，aec 算法调试参数保证aec 收敛速度满足需求*/	
} VI_PIPE_ATTR_S;

/**定义VI PIPE属性，本属性用来给用户机会修改驱动的一些默认配置，典型的使用本属性的场景是一个ispin的驱动适配所有灌raw，本参数一旦使能，所有的参数都要正确配置，
强烈建议用户先调用本属性的get 获取驱动的值，然后针对自己的需求修改，本api 必须放到AR_MPI_ISP_SetPubAttr 之后，AR_MPI_ISP_Init 之前
这点需要注意，AR_MPI_VI_GetPipeExtAttr，获取到的是驱动的默认值，而不是AR_MPI_VI_SetPipeExtAttr 设置进去的*/
typedef struct arVI_PIPE_EXT_ATTR_S
{
    AR_S32 s32EnableExtAttr;  /**< 使能管道的扩展属性，使能后，这些属性值将使用本设置的属性，不再使用从驱动程序获取的值*/
	
    AR_U32  u32LinesPer500ms; /**< 500ms 曝光时间的时候，sensor的曝光行数*/
	AR_U32  u32Hmax;          /**< Sensor的htotal=width+hblank， 等于sensor的宽度加上行消隐*/
    AR_U32  u32FullLinesStd;  /**< Sensor的vtotal=height+vblank， 等于sensor的高度加上帧消隐*/
    AR_U32  u32FullLinesMid;  /**< 在hdr模式的时候，sensor的中曝光的最大曝光行数*/
    AR_U32  u32FullLinesShort;  /**< 在hdr模式的时候，sensor的短曝光的最大曝光行数*/
    AR_U32  u32FullLinesMax;   /**< sensor 容许的最大曝光行数*/
	AR_CHAR TuningPraBinName[256]; /**< isp的效果参数的文件名，注意带路径*/
    AR_BOOL bDvpDownSampleEn;/**<使用dvp输入的降采样功能*/
	AR_BOOL bFoucs;/**<使用isp的对焦功能*/
    AR_U32   au32ShortOffset;/**<描述在非vc hdr模式下，短曝光开始的位置相对于帧的偏移的行数*/
    AR_U32   au32MidOffset;/**<描述在非vc hdr模式下，中曝光开始的位置相对于帧的偏移的行数*/
    AR_U32   au32LongOffset;/**<描述在非vc hdr模式下，长曝光开始的位置相对于帧的偏移的行数*/
    AR_U32   au32VcCnt;/**<描述在vc hdr模式下，虚拟通道的个数，一般为2 或者 3，非vc 模式设置为0*/
    AR_U32   au32VcMask;/**<描述在vc hdr模式下，使用的虚拟通道的情况，不同的bit，代表不同弄的vc 号，非 vchdr 设置为0*/
	AR_S32	 Vc[4]; /**for PDAF*/
	AR_S32	 Dt[4]; /**for PDAF*/
	AR_S32	 BitWidth[4];  /**for PDAF*/
	AR_S32	 fps[4];	/**for PDAF*/
	AR_S32   Width[4];
	AR_S32	 Height[4];
	AR_S32	 PdVcId;
	AR_S32	 PdPixelFormat;
}VI_PIPE_EXT_ATTR_S;


#define IR_DYNAMIC_CALIB_NONE       (0x0)
#define IR_DYNAMIC_CALIB_SNS        (0x1)
#define IR_DYNAMIC_CALIB_OCC        (0x1 << 1)
#define IR_DYNAMIC_CALIB_B          (0x1 << 2)
#define IR_DYNAMIC_CALIB_QGG        (0x1 << 3)
#define IR_DYNAMIC_CALIB_DP_DETECT  (0x1 << 4)



#define	IR_START_CALI_MODE_COVER   (0)
#define	IR_START_CALI_MODE_NOCOVER (1)
#define	IR_START_CALI_MODE_COVER2NOCOVER (2)
#define	IR_START_CALI_MODE_NOCOVER_CALI (3)


typedef struct {
    AR_U32 u32ZoomRatio;        /**<RW; 锅盖矫正模板图像缩小倍数，支持1、2、3、4倍缩小。如果为0，则由SDK自行决定缩小倍数。模板图像的宽度不能超过640，请根据这一限制来选择合适的缩小倍数。*/
    AR_U32 u32GrabFrmCnt;
    AR_U32 u32CornerStatsWidth;
    AR_FLOAT f32QggStrength;
} IR_QGG_CALIB_CFG_S;

typedef struct {
    VIDEO_FRAME_S stQggBuf;
    AR_U32 u32QggCorrection;
} IR_QGG_CALIB_RES_S;

//#define IR_CALIB_DATA_HEADER_LEN        512

typedef struct {
    AR_U32 u32Width;
    AR_U32 u32Height;
    AR_U32 u32Stride;
} IR_OCC_DATA_HEADER_S;

typedef struct {
    AR_U32 u32Width;
    AR_U32 u32Height;
    AR_U32 u32Stride;
    AR_U32 u32BadColumnCnt;
    AR_U32 au32BadColumn[IR_MAX_BAD_COLUMN_CNT];
} IR_KB_DATA_HEADER_S;

typedef struct {
    AR_U32 u32Width;
    AR_U32 u32Height;
    AR_U32 u32Stride;
    AR_U32 u32QggCorrection;
} IR_QGG_DATA_HEADER_S;


typedef enum {
    IR_CALI_MODE_AUTO_COVER,
    IR_CALI_MODE_AUTO_NOCOVER,
    IR_CALI_MODE_MANUAL_SHUTTER,
    IR_CALI_MODE_MANUAL_SCENE,
    IR_CALI_MODE_AUTO_COVER_USE_TEMP_CHANGE,
    IR_CALI_MODE_NONE,
} IR_CALIB_MODE_E;


typedef STRU_CAM_PRA_SET_IR_DP_AUTO_DETECT_PARAM_PRA_T IR_DP_DETECT_CALIB_CFG_S;


typedef struct{
    //--------------------矫正使用的pipe， 获取默认属性后重新赋值----------------
	VI_PIPE rawViPipe;
	VI_PIPE thrmlViPipe;
	//--------------------基本矫正参数-------------------
	AR_S32  AutoCalibStartMode;             /*1: 无遮挡矫正 0：遮挡矫正 2 先遮挡矫正，若干时间后切换为无遮挡矫正*/	
    AR_BOOL bIrLockWhenCalib;	            //做自动标定的过程中，是否锁定帧
    volatile AR_BOOL bAutoCalib;	        //如果自动运行被设置，而且矫正类型是无，那么使用无遮挡运行，采用预先标定号的参数运行红外
    AR_U32  u32AutoCalibIntervalMs;	        //当工作在定时自动矫正的时候，多长时间做一次自动矫正
	AR_S32  Cover2NoCoverSwitchTimeMs;      /* 当AutoCalibStartMode 为 2的时候，表示多长时间后切换为无遮挡矫正*/	
    volatile AR_BOOL bManualCalib;	        /**<为1的时候触发一次手动矫正，矫正完成后自动变成0*/	
    AR_U32 u32ManualCalibType;	            //触发手动矫正的时候，矫正类型
    AR_U32 u32FirstIrDynamicCalibType;      //开机第一次触发的矫正类型
    volatile AR_U32 u32IrDynamicCalibType;	//定时矫正的时候，矫正类型    
    volatile AR_U32 u32IrDynamicCalibTypeDefault;	//定时矫正的时候，矫正类型 
    volatile AR_BOOL bShutter;	            //是否开启shutter
    volatile AR_BOOL bSaveResult;
	volatile AR_BOOL bWaitManualCalibDone;
	AR_S32 force;                           //不管温度或者定时是否满足，执行一直自动标定，执行完成后，自动清零


    //-----------------温度改变驱动矫正----------------	
	volatile AR_S32   bEnableTempChangeCoverClib;      /**< 使能温度驱动的有遮挡自动矫正*/	
	AR_U32   u32AutoCalibTempChangePollIntervalMs;/**< 多长时间轮训检测一次温度改变*/	
	AR_U32   TempChangeTimeOutMs;  /**<如果长时间温度不变，则做一次b矫正*/		
    AR_FLOAT fDetaTempOccAndB;  /*温度改变多少，做一次occ+b*/
	AR_FLOAT fDetaTempBOnly;	/*温度改变多少度，做一次b*/
    AR_FLOAT fDetaTempSnsOccAndB; /*温度改变多少后，做一次sns+occ+b*/

	// ---------sensor cali---------
    AR_U32 u32SnsParamSize;	
    AR_CHAR *strInitSnsParamFname;          /* 初始红外sensor标定参数文件。*/
    AR_CHAR strSnsCalibFname[128];
    IR_SNS_CALIB_CFG_S stSnsCalibCfg;

    //------------occ -------------------    
    AR_BOOL bOccCalibWithHeader;	
    const AR_CHAR *strInitOccFname;               /* 初始红外occ标定文件。*/
    AR_CHAR strOccCalibFname[128];
    IR_OCC_CALIB_CFG_S stOccCalibCfg;

	///---------------kb ------------------		
    AR_BOOL bBCalibWithHeader;
    const AR_CHAR *strInitKbFname;                /* 初始红外kb标定文件。*/	
    AR_CHAR strBCalibFname[128];	
    IR_B_CALIB_CFG_S stBCalibCfg;	
    IR_KB_CALIB_CFG_S stKbCalibCfg;               /* 红外kb标定配置。*/

	//--------------qgg---------------------		
    const AR_CHAR *strInitQggFname;               /* 初始红外锅盖标定文件。*/	
    AR_CHAR strQggCalibFname[128];
	IR_QGG_CALIB_CFG_S stQggCalibCfg;
   
	//dp auto detect
	IR_DP_DETECT_CALIB_CFG_S stDpDetctCfg;	
    AR_CHAR strDpDetectCalibFname[128];
	AR_S32	TimeMsDpDetct;	/*每隔多少毫秒做一次*/
	AR_S32  DpDetct_mode;   /*0: 定时做, 1:跟随有挡矫正*/
	AR_S32  DpDetect_setMode; /*0 直接使用探测到的坏点设置k，1：使用原始k和探测到的坏点或操作*/

	//---------------no cover cali-------------------------
	const AR_CHAR *strNoCoverOccBPraTuningName;	/* 无遮挡矫正标定文件。*/		
    volatile AR_BOOL bNoCoverCalib; //bNoCoverCalib, 驱动无遮挡矫正，本标志用于工厂中获取基础基于温度的 ooc b 的矫正数据。实际场景不要使用此标志，打开此标志后，矫正程序会判断温度变化
    //如果问题变化超过 TempDeta，就会启动一次矫正，并保存矫正结果，矫正结果会带着温度。这个标志是一个tuning的过程，和实际的运行无关，而且实际运行无遮挡矫正的时候，这个标志不能被设置    
    AR_U32 u32NoCoverAutoCalibIntervalMs;  //在无遮挡运行的时候的，参数更新时间    
	AR_FLOAT min_temp;//无遮挡标定的时候，温度的下限
	AR_FLOAT max_temp;	//无遮挡标定温度上限
    volatile AR_FLOAT TempDeta;	//无遮挡标定的时候，温度变化多少度标定一个occ 或者b
	volatile AR_BOOL  inter;   //是否使能无遮挡b插值

	//---------------------measure temp cali pra---------------------
    AR_CHAR strMesureTempLutFname[128];


	//first auto calibration delay ms
	AR_S32 s32FirtstAutoCalibrationDelay;
}VI_IR_AUTOCALIATTR_S;


typedef struct{
    IR_CALIB_MODE_E mode;  //调用切换模式会触发一次矫正，手动矫正会判断是否保存结果，如果相应的矫正文件存在，就会保存在矫正文件中
	AR_S32          sns;
	AR_S32          occ;
	AR_S32          b;
	AR_S32          qgg;	
	AR_S32          dp_detect;
	AR_S32          save_result;  //保存结果
	AR_S32          with_header;  
	AR_CHAR         *sns_name;
	AR_CHAR         *occ_name;
	AR_CHAR         *b_name;	
	AR_CHAR         *qgg_name;	
	AR_CHAR         *dp_detect_name;
}VI_AUTO_CALI_SWITCH_T;


/**定义VI PIPE的3DNR X接口的参数，静态属性，isp run 之后不可修改*/
typedef struct arVI_PIPE_NRX_PARAM_S
{
    AR_BOOL   bEnable;                                          /**<@note 新添加参数。Range:[0, 1]; Format:1.0;Enable/Disable NrXParam Function*/
    AR_BOOL   bManual;                                          /**<@note 新添加参数。*/
    STRU_ISP_SUB_MODULE_3D_2D_NR_TUNING_T stAutoNrXParam;       /**<@note 新添加参数。set to tuning bin, the system will select the NrXParam from tuning*/
    STRU_ISP_SUB_MODULE_3D_2D_NR_PRA stManualNrXParam;          /**<@note 新添加参数。the isp will use the NrXParam directly from this setting*/
}VI_PIPE_NRX_PARAM_S;

/**定义VI通道属性*/
typedef struct arVI_CHN_ATTR_S
{
    SIZE_S              stSize;                 /**<目标图像大小。*/
    PIXEL_FORMAT_E      enPixelFormat;          /**<目标图像像素格式。静态属性，设置CHN时设定，不可更改。*/
    DYNAMIC_RANGE_E     enDynamicRange;         /**<@note不使用 目标图像动态范围。静态属性，设置CHN时设定，不可更改。*/
    VIDEO_FORMAT_E      enVideoFormat;          /**<@note不使用 目标图像视频数据格式。*/
    COMPRESS_MODE_E     enCompressMode;         /**<@note 转义参数 目标图像压缩格式，只支持seg（cf50）或不压缩,压缩只ch0支持，且ch0必须配置和sensor大小相同的情况下支持*/
    AR_BOOL             bMirror;                /**<@note不使用 Mirror使能开关。AR_FALSE：不使能；AR_TRUE：使能。*/
    AR_BOOL             bFlip;                  /**<@使能开关。AR_FALSE：不使能；AR_TRUE：使能。94xx 系列有效*/
    AR_U32              u32Depth;               /**<@note 转义参数，队列超过这个深度，前面的数据会被丢弃。*/
    FRAME_RATE_CTRL_S   stFrameRate;            /**<帧率控制。源帧率取值范围：(0, 240]，以及-1。目标帧率取值范围：[-1, 240]。当源帧率为-1 时，目标帧率必须为-1(不进行帧率控制)，其他情况下，目标帧率不能大于源帧率。*/
    AR_S32              u32BufCount;            /**<@note 新添加参数。 分配给本通道使用的buffer的数据，如果为0，默认设置为5. 设置的原则是，如果vin 后面bind的模块较多，则增加这个数目*/
    AR_S32              u32DepthClient[8];      /**<@note 新添加参数,多进程取流使用，每一个用户获取图像的队列深度。队列超过这个深度，前面的数据会被丢弃，最多支持8个用户独立获取*/
    AR_S32              s32Timeout;            /**<@note  新加参数，<=0 的时候默认200ms，加大这个值有利用减少系统调用，节省cpu，但是也导致需要多一个buffer。*/
	AR_S32              s32KeepRation;         /**<@note  新加参数，控制isp 缩小输出的时候是否先crop到输出图像的比例，然后压缩，0，不做同比例，1 做同比例*/	
	AR_S32				s32AntiDisable; 		/**<@note  新加参数，控制isp 输出缩放是否打开抗锯齿功能>	，0 打开抗锯齿，1： 关闭抗锯齿*/
	AR_S32              s32ContinueBuffer;      /**<@note  新加参数，控制yuv 三个pannel的地址是否连续在一起，1：连续在一起。0 非连续在一起*/
	AR_S32              s32UsePriVbPool;       /**<@note   新加参数，是否使用通道私有VBpool*/
	AR_S32              s32BigPicRWriteOffset;
} VI_CHN_ATTR_S;


typedef enum{
	EXTCH_ZOOM_TYPE_SCALER,
	EXTCH_ZOOM_TYPE_AI,    /*AI room only support 2x and 4x*/
}VI_EXTCH_ZOOM_TYPE_T;

typedef struct __EXTCH_ZOOM__{
   VI_EXTCH_ZOOM_TYPE_T zoom_type; /**<*/   
   SIZE_S               stSize;  /**<zoom 输出的宽高*/   
   AR_S32               stride;
   ISP_ZOOM_TIDY_ATTR_S stZoomTidyAttr;
   AR_S32               s32SuperOnly;
   AR_S32               s32AiBypass;
}VI_EXTCH_ZOOM_ATTR_T;

typedef enum {
	EXTCH_SOFTWARE_COLORMAP,
	EXTCH_HARDWARE_COLORMAP,
	EXTCH_HARDWARE_IVE_COLORMAP,
}VI_EXTCH_COLORMAP_TYPE_T;

typedef struct __COLOR_MAP__{   
	AR_CHAR map_table[128]; /*colormap 表的文件名*/
	VI_EXTCH_COLORMAP_TYPE_T colormap_type;
	AR_S32 palette_idx;
	AR_S32 palette_use_sram;
	AR_S32 only_use_extch;
	AR_U32 infusion_freq;
}VI_EXTCH_COLORMAP_ATTR_T;

typedef struct __MEASURE_TEMP__{
    AR_S32 revert;	
}VI_MEASURE_TEMP_ATTR_T;

typedef struct __GDC_LDC_PRAM
{
  AR_FLOAT k[9];
  AR_FLOAT ldc_k0 ;
  AR_FLOAT ldc_k1 ;
  AR_FLOAT ldc_k2 ;
} STU_GDC_LDC_PARAM_T;

typedef struct __DUAL_LIGHT_INFUSIN__{
	AR_S32 pair_pipe;
	AR_S32 pair_ch;	
	SIZE_S stSize; //dual light infusion size	
	AR_S32 infusion_freq;
	AR_S32 palette_idx;
	AR_S32 en_align;
	AR_FLOAT matrix[9];
	AR_CHAR map_table[128]; /*colormap 表的文件名*/
	AR_S32 work_mode;
	AR_S32 user_preset_tuning;
	AR_S32 user_matrix;
	AR_CHAR xfeat_filename[128];
	AR_CHAR glue_filename[128];
	STU_GDC_LDC_PARAM_T ldc_param;
	AR_S32 en_ldc;
}VI_DUAL_LIGHT_INFUSIN_ATTR_T;


typedef enum{
	VI_EXTCH_FEATURE_ZOOM,
	VI_EXTCH_FEATURE_COLORMAP,
	VI_EXTCH_FEATURE_ZOOM_COLORMAP,		
	VI_EXTCH_FEATURE_INFUSIN,
}VI_EXTCH_FEATURE_T;


typedef struct arVI_EXTCHN_ATTR_S
{
    AR_S32 src_chid;
	VI_EXTCH_FEATURE_T enFeature;
	VI_EXTCH_ZOOM_ATTR_T stZoom;
	VI_EXTCH_COLORMAP_ATTR_T stColorMap;
	VI_MEASURE_TEMP_ATTR_T   stMearsureTemp;
	VI_DUAL_LIGHT_INFUSIN_ATTR_T stDualInfusion;	
}VI_EXTCHN_ATTR_S;


typedef struct{
	AR_S32 s32SofLowdeayEn;  /**< 是否使能软件lowdelay 功能 */
	AR_U32 u32LowdeayPattern;/**< 软件lowdeay 使用的pattern */
    AR_U32 au32LowdelayLine[32];
    AR_U32 u32LowdelayLineCnt;
}VIN_SOFT_LOWDELAY_ATTR;


typedef struct{
	RECT_S               stHwCrop;
	RECT_S               stHwCrop1;
	AR_U32               offsetOut;	
}STRU_CROP_TILE_ATTR;


/**定义VI通道扩展属性，在设置完成通道属性后，isp run 之前配置。静态属性，isp run 之后不可修改*/
typedef struct arVI_CHN_EXT_ATTR_S
{
   RECT_S               stHwCrop;  /**<配置通道的crop 属性，通道对原图像进行指定区域的crop 后输出，如果ch输出的是raw图，请水平方向6像素对齐,如果kw 设置为0，将使用默认的crop 策略*/  

   /**<如下属性控制数据写到buffer的什么位置,使能这个feature 后，(total_w total_h 大于0就认为使能了本特征),驱动会分配一个total_w * total_h 的buffer。 数据会写入到这个buffer的
      x，y 开始的位置，数据的wh 由VI_CHN_ATTR_S 的stSize 指定，本feature 用来动态修改分辨率的时候使用*/
   AR_S32 x;  //
   AR_S32 y;
   AR_S32 total_w;  //x+width<=total_w
   AR_S32 total_h;  //y+total_h<=total_h

   /**<通道的lowdelay 模式*/
   STRU_LOW_DELAY_MODE_T enLowDelayMode;
   AR_S32 s32DelayLine;  /**<通道写入多少行后，输出lowdelay 信号，默认16*/
   ENUM_LOW_DELAY_SINK_T enLowDelaySink;
   AR_S32 hw_out_index;
   RECT_S               stHwCrop1;  /**<配置通道的crop1 属性，本属性只作用于isp输出的scaler 之后，对scaler之后的图像进行cop 输出*/
   VIN_SOFT_LOWDELAY_ATTR stSoftLowdelayAttr;
   STRU_CROP_TILE_ATTR tile_crop[4][4];  /**< 对于每一个分片，告诉isp 输出如何crop，用与融合切边*/
   SIZE_S              stSizeWhole;
   AR_S32              fill_mem;
   uint8_t             Color_yuv[3];
} VI_CHN_EXT_ATTR_S;



/**定义VI PIPE的状态信息*/
typedef struct arVI_PIPE_STATUS_S
{
    AR_BOOL bEnable;                /**<当前PIPE是否使能。*/
    AR_U32  u32IntCnt;              /**<中断计数。*/
    AR_U32  u32FrameRate;           /**<VI PIPE的实时帧率。*/
    AR_U32  u32LostFrame;           /**<丢帧计数。*/
    AR_U32  u32VbFail;              /**<VB申请失败计数。*/
    SIZE_S  stSize;                 /**<PIPE当前图像大小。*/
} VI_PIPE_STATUS_S;

typedef struct
{
   STRU_CAM_PRA_CF50_CMP_PRA_T stChCmpAttr;  
}VI_CH_CF50_CMP_ATTR_T;

/**定义扩展通道的图像来源*/
typedef enum arVI_EXT_CHN_SOURCE_E
{
    VI_EXT_CHN_SOURCE_TAIL,         /**<扩展通道的图像来自物理通道后处理（DIS,LDC,SPREAD等）后的图像。*/
    VI_EXT_CHN_SOURCE_HEAD,         /**<扩展通道的图像来自物理通道后处理（DIS,LDC,SPREAD等）前的图像。*/

    VI_EXT_CHN_SOURCE_BUTT
} VI_EXT_CHN_SOURCE_E;

/**定义VI扩展通道属性*/
typedef struct arVI_EXT_CHN_ATTR_S
{
    VI_EXT_CHN_SOURCE_E enSource;               /**<定义扩展通道的图像来源，只支持VI_EXT_CHN_SOURCE_TAIL。*/
    VI_CHN              s32BindChn;             /**<绑定的源物理通道。*/
    SIZE_S              stSize;                 /**<目标图像大小。*/
    PIXEL_FORMAT_E      enPixFormat;            /**<目标图像像素格式。*/
    DYNAMIC_RANGE_E     enDynamicRange;         /**<目标图像动态范围。*/
    COMPRESS_MODE_E     enCompressMode;         /**<目标图像压缩格式。*/
    AR_U32              u32Depth;               /**<用户获取图像的队列深度。*/
    FRAME_RATE_CTRL_S   stFrameRate;            /**<帧率控制。源帧率取值范围：(0, 240]，以及-1。目标帧率取值范围：[-1, 240]。当源帧率为-1时，目标帧率必须为-1(不进行帧率控制)，其他情况下，目标帧率不能大于源帧率。*/
} VI_EXT_CHN_ATTR_S;

/**定义VI扩展通道属性*/
typedef enum arVI_CROP_COORDINATE_E
{
    VI_CROP_RATIO_COOR = 0,         /**<相对坐标。*/
    VI_CROP_ABS_COOR,               /**<绝对坐标。*/
    VI_CROP_BUTT
} VI_CROP_COORDINATE_E;

/**定义VI CROP信息结构体*/
typedef struct arVI_CROP_INFO_S
{
    AR_BOOL                 bEnable;            /**<CROP使能开关。*/
    VI_CROP_COORDINATE_E    enCropCoordinate;   /**<CROP起点坐标模式。*/
    RECT_S                  stCropRect;         /**<CROP的矩形区域。*/
} VI_CROP_INFO_S;


/**定义VI通道的状态信息*/
typedef struct arVI_CHN_STATUS_S
{
    AR_BOOL bEnable;                /**<当前通道是否使能。0：不使能；1：使能。*/
    AR_U32  u32FrameRate;           /**<VI通道的实时帧率。*/
    AR_U32  u32LostFrame;           /**<丢帧计数。*/
    AR_U32  u32VbFail;              /**<VB申请失败计数。*/
    SIZE_S  stSize;                 /**<通道当前图像大小。*/
	AR_U32  u32FredomBufCount;      /**<当前通道可以自由使用的buffer的个数，也就是说这个buffer 已经被其他应用者释放，但是还没有配置给isp的buffer的数量*/
} VI_CHN_STATUS_S;

typedef struct arVI_PMF_ATTR_S
{
    AR_BOOL bEnable;                            /**<@note 新添加参数。Whether PMF is enable */
    SIZE_S  stDestSize;                         /**<@note 新添加参数。Target size */
    AR_S64  as64PMFCoef[VI_PMFCOEF_NUM];        /**<@note 新添加参数。Array of PMF coefficients */
} VI_PMF_ATTR_S;

/**定义VS信号的属性*/
typedef struct arVI_DUMP_ATTR_S
{
    AR_BOOL         bEnable;        /**<是否使能dump。*/
    AR_U32          u32Depth;       /**<Dump数据的队列深度。取值范围：[0, 8]。*/
} VI_DUMP_ATTR_S;

/**定义VI PIPE数据的来源类型*/
typedef enum arVI_PIPE_FRAME_SOURCE_E
{
    VI_PIPE_FRAME_SOURCE_DEV = 0,   /**<数据来自设备。*/
    VI_PIPE_FRAME_SOURCE_USER_FE,   /**<数据来自用户从FE送进来的数据。*/
    VI_PIPE_FRAME_SOURCE_USER_BE,   /**<数据来自用户从BE送进来的数据。*/

    VI_PIPE_FRAME_SOURCE_BUTT
} VI_PIPE_FRAME_SOURCE_E;

typedef struct ar_VI_RAW_INFO_S
{
    VIDEO_FRAME_INFO_S      stVideoFrame;       /**<@note 新添加参数。*/
    ISP_CONFIG_INFO_S       stIspInfo;          /**<@note 新添加参数。*/
} VI_RAW_INFO_S;

/**定义VI通道提前上报中断属性*/
typedef struct arVI_EARLY_INTERRUPT_S
{
    AR_BOOL bEnable;                /**<提前上报中断使能开关。AR_FALSE：不使能；AR_TRUE：使能。*/
    AR_U32 u32LineCnt;              /**<提前上报中断提前的行数。该行数必须大于0，小于等于通道图像输出的高度。*/
} VI_EARLY_INTERRUPT_S;


/**定义红外产品控制接口类型（串口或I2C）*/
typedef enum arIR_CTRL_TYPE_E
{
    IR_CTRL_TYPE_UART = 0,
    IR_CTRL_TYPE_I2C,

    IR_CTRL_TYPE_BUTT,
} IR_CTRL_TYPE_E;

/**定义红外产品控制命令*/
typedef enum arIR_CTRL_E
{
    /**0x01 ~ 0xC3为系统定义的命令*/
    IR_CTRL_EN_AUTO_SHUTTER_CALIB       = 0x01,         /**<禁止用户实现。*/
    IR_CTRL_DIS_AUTO_SHUTTER_CALIB      = 0x02,         /**<禁止用户实现。*/
    IR_CTRL_MANUAL_SHUTTER_CALIB        = 0x03,         /**<禁止用户实现。*/
    IR_CTRL_MANUAL_BACKGROUND_CALIB     = 0x04,         /**<禁止用户实现。*/
    IR_CTRL_SWITCH_FPS_25               = 0x05,         /**<用户实现。*/
    IR_CTRL_SWITCH_FPS_50               = 0x06,         /**<用户实现。*/
    IR_CTRL_MANUAL_SELF_CHECK           = 0x07,         /**<禁止用户实现。*/
    IR_CTRL_GET_DEVICE_ID               = 0x08,         /**<用户实现。*/
    IR_CTRL_GET_SNS_TEMPERATURE         = 0x09,         /**<禁止用户实现。*/
    IR_CTRL_SET_REGISTER_ADDR           = 0x0A,         /**<用户实现。*/
    IR_CTRL_GET_REGISTER_ADDR           = 0x0B,         /**<用户实现。*/
    IR_CTRL_SET_REGISTER_DATA           = 0x0C,         /**<用户实现。*/
    IR_CTRL_GET_REGISTER_DATA           = 0x0D,         /**<用户实现。*/
    IR_CTRL_START_STOP_OUTPUT           = 0x0E,         /**<用户实现。*/
    IR_CTRL_SET_OUTPUT_TYPE             = 0x0F,         /**<用户实现。*/
    IR_CTRL_SWITCH_OUTPUT_TYPE          = 0x10,         /**<用户实现。*/
    IR_CTRL_GET_OUTPUT_TYPE             = 0x11,         /**<用户实现。*/
    IR_CTRL_GET_DEVICE_SN               = 0x12,         /**<禁止用户实现。*/
    IR_CTRL_MANUAL_SWITCH_SHUTTER       = 0x13,         /**<禁止用户实现。*/
    IR_CTRL_MANUAL_CALIB                = 0x14,         /**<禁止用户实现。*/
    IR_CTRL_DUMP_LT_HT_RAW_IMAGE        = 0x15,         /**<禁止用户实现。*/
    IR_CTRL_KB_CALIB                    = 0x16,         /**<禁止用户实现。*/
    IR_CTRL_GET_KB_DATA                 = 0x17,         /**<禁止用户实现。*/
    IR_CTRL_GET_LT_HT_RAW_IMAGE         = 0x18,         /**<禁止用户实现。*/
    IR_CTRL_SET_KB_DATA                 = 0x19,         /**<禁止用户实现。*/
    IR_CTRL_QGG_CALIB                   = 0x1A,         /**<禁止用户实现。*/
    IR_CTRL_GET_QGG_DATA                = 0x1B,         /**<禁止用户实现。*/
    IR_CTRL_GET_CAM_INFO                = 0x1C,         /**<禁止用户实现。*/
    IR_CTRL_ENTER_FW_UPGRADE_MODE       = 0x1D,         /**<用户实现。*/
    IR_CTRL_SET_DEVICE_SN               = 0x1E,         /**<禁止用户实现。*/
    IR_CTRL_GET_THERMAL_IMAGE           = 0x1F,         /**<禁止用户实现。*/
    IR_CTRL_GET_FW_VERSION              = 0x20,         /**<禁止用户实现。*/
    IR_CTRL_SET_COLOR_MAP_IDX           = 0x21,         /**<禁止用户实现。*/
    IR_CTRL_SET_QGG_DATA                = 0x22,         /**<禁止用户实现。*/
    IR_CTRL_SET_ZOOM_RATIO              = 0x23,         /**<禁止用户实现。*/
    IR_CTRL_SET_BRIGHTNESS              = 0x24,         /**<禁止用户实现。*/
    IR_CTRL_GET_BRIGHTNESS              = 0x25,         /**<禁止用户实现。*/
    IR_CTRL_SET_CONTRAST                = 0x26,         /**<禁止用户实现。*/
    IR_CTRL_GET_CONTRAST                = 0x27,         /**<禁止用户实现。*/
    IR_CTRL_SET_SHARPNESS               = 0x28,         /**<禁止用户实现。*/
    IR_CTRL_GET_SHARPNESS               = 0x29,         /**<禁止用户实现。*/
    IR_CTRL_GET_Y_IMAGE                 = 0x2A,         /**<禁止用户实现。*/
    IR_CTRL_SET_DVP_TX_DRIVING_STRENGTH = 0x2B,         /**<禁止用户实现。*/
    IR_CTRL_SWITCH_FPS_COMMON           = 0x2C,         /**<用户实现。*/
    IR_CTRL_CFG_TEMPERATURE_MEASURE     = 0x2D,         /**<禁止用户实现。*/
    IR_CTL_MEASURE_TEMPERATURE          = 0x2E,         /**<禁止用户实现。*/

    IR_CTRL_REQUEST_RECV_BULK_DATA      = 0xC0,         /**<禁止用户实现。*/
    IR_CTRL_REQUEST_SEND_BULK_DATA      = 0xC1,         /**<禁止用户实现。*/

    /**0xC4 ~ 0xFF为用户自定义的命令*/
    IR_CTRL_USER_DEFINE_START           = 0xC4,         /**<用户实现。*/
    IR_CTRL_USER_DEFINE_END             = 0xFF,         /**<用户实现。*/

    IR_CTRL_BUTT,
} IR_CTRL_E;


typedef AR_S32 (*IR_CTRL_PROC_FN)(const AR_U8 au8MsgIn[], AR_U8 au8MsgOut[], AR_VOID *pPriv);

/**定义红外控制命令的用户实现*/
typedef struct arIR_CTRL_USER_PROC_S
{
    IR_CTRL_E enCmd;            /**<控制命令。*/
    IR_CTRL_PROC_FN pfnProc;    /**<用户实现命令处理。au8MsgIn[]: 上位机向红外产品发送的命令。au8MsgOut[]: 红外产品向上位机回复的消息。*/
} IR_CTRL_USER_PROC_S;


#define IR_CTRL_MAX_PATH_LEN        128
#define IR_CTRL_USER_PROC_MAX_CNT   ((IR_CTRL_USER_DEFINE_END - IR_CTRL_USER_DEFINE_START + 1) + 20)

/**定义红外控制串口配置信息*/
typedef struct arIR_CTRL_UART_CFG_S
{
    AR_CHAR strUartDevName[IR_CTRL_MAX_PATH_LEN];       /**<串口设备文件名。*/
    AR_U32 u32UartBaudrate;                             /**<串口波特率。*/
} IR_CTRL_UART_CFG_S;

/**定义红外控制I2C配置信息*/
typedef struct arIR_CTRL_UART_I2C_S
{
    AR_CHAR strI2cDevName[IR_CTRL_MAX_PATH_LEN];        /**<I2C设备文件名。*/
} IR_CTRL_I2C_CFG_S;


/**定义红外控制配置信息*/
typedef struct arIR_CTRL_CFG_S
{
    IR_CTRL_TYPE_E enIrCtrlType;                        /**<红外控制接口类型。*/
    union {
        IR_CTRL_UART_CFG_S stUartCfg;                   /**<红外控制串口配置信息。*/
        IR_CTRL_I2C_CFG_S stI2cCfg;                     /**<红外控制I2C配置信息。*/
    };

    IR_CTRL_USER_PROC_S astUserProc[IR_CTRL_USER_PROC_MAX_CNT];     /**<红外控制命令的用户实现。仅适用于标注“用户实现”的命令。*/
    AR_U32 u32UserProcCnt;                                          /**<用户实现的红外控制命令数量。*/

    VI_PIPE ViPipe;                                     /**<Vi Pipe号。*/
    VI_CHN yuvChn;                                      /**<Vi Pipe号。*/
    VI_CHN colorMapExtChn;                              /**<Vi Pipe号。*/
    VI_CHN zoomExtChn;                                  /**<Vi Pipe号。*/

    AR_CHAR strDeviceSnFname[IR_CTRL_MAX_PATH_LEN];     /**<保存设备序列号的文件名。*/
    AR_CHAR strFwVersionFname[IR_CTRL_MAX_PATH_LEN];    /**<保存固件版本号的文件名。*/

    AR_VOID *pPriv;                                     /**<私有数据指针。*/
} IR_CTRL_CFG_S;


typedef AR_VOID* IR_CTRL_OBJ_HANDLE;                    /**<红外控制句柄。*/


/** @} */  /** <!-- ==== Structure Definition End ==== */

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif /* End of #ifndef__AR_COMM_VIDEO_IN_H__ */


