#ifndef __MPI_IVE_API_H__
#define __MPI_IVE_API_H__

#include "hal_ive_api.h"


AR_S32 AR_MPI_IVE_And(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2,
                        IVE_DST_IMAGE_S *pstDst, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Or(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2,
                        IVE_DST_IMAGE_S *pstDst, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Xor(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2,
                        IVE_DST_IMAGE_S *pstDst, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Add(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1,IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_ADD_CTRL_S *pstAddCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Sub(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_SUB_CTRL_S *pstSubCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Multi(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MULTI_CTRL_S *pstMultiCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Thresh(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_THRESH_CTRL_S *pstThreshCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Thresh_U16(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_THRESH_U16_CTRL_S *pstThreshCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Thresh_S16(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_THRESH_S16_CTRL_S *pstThreshCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Integ(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_INTEG_CTRL_S *pstIntegCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_16BitTo8Bit(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_16BIT_TO_8BIT_CTRL_S *pst16BitTo8BitCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Hist(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Map(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_MEM_INFO_S *pstMap, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAP_CTRL_S *pstMapCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Csc(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_CSC_CTRL_S *pstCscCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Dma(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_DMA_CTRL_S *pstDmaCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Dilate(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_DILATE_CTRL_S *pstDilateCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Erode(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_ERODE_CTRL_S *pstErodeCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_OrdStatFilter(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_ORD_STAT_FILTER_CTRL_S *pstOrdStatFltCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Filter(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_FILTER_CTRL_S *pstFltCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Sobel(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDstH, IVE_DST_IMAGE_S *pstDstV,
                        IVE_SOBEL_CTRL_S *pstSobelCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_MagAndAng(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDstMag, IVE_DST_IMAGE_S *pstDstAng,
                        IVE_MAG_AND_ANG_CTRL_S *pstMagAndAngCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_MauMatrixMul(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_CTRL_S *pstMauCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_MauCosDist(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_CTRL_S *pstMauCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_MauEuclidDist(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_CTRL_S *pstMauCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_MauManhattanDist(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_CTRL_S *pstMauCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_MauVectorOp(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_VECTOR_OP_CTRL_S *pstMauVectorOpCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_MauTypeConvert(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_TYPE_CONVERT_CTRL_S *pstMauTypeConvCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_CannyHysEdge(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_DST_IMAGE_S *pstDst, IVE_DST_MEM_INFO_S *pstStack,
                        IVE_CANNY_HYS_EDGE_CTRL_S *pstCannyHysEdgeCtrl, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_CannyEdge(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstEdge, IVE_MEM_INFO_S *pstStack, AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Route(AR_HANDLE *pIveHandle, AR_IVE_OPER_TYPE_E aenOper[], IVE_SRC_IMAGE_S astImgSrc[],
                        IVE_DST_IMAGE_S astImgDst[], AR_IVE_CTRL_U aunCtrl[], AR_BOOL bInstant);
AR_S32 AR_MPI_IVE_Query(AR_HANDLE IveHandle, AR_BOOL *pbFinished, AR_BOOL bBlock);
AR_S32 AR_MPI_IVE_ATTR_SET(AR_IVE_ATTR_S *pstAttr);

AR_S32 AR_MPI_IVE_Init(AR_VOID);
AR_VOID AR_MPI_IVE_DeInit(AR_VOID);

#endif
