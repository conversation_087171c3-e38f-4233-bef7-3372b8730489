/**
 * @file mpi_type.h
 * @brief  描述MPI基础数据类型定义
 * <AUTHOR> Software Team
 * @version 0.0.1
 * @date 2021/07/21
 * @license 2021-2025, Artosyn. Co., Ltd.
**/

#ifndef __AR_TYPE_H__
#define __AR_TYPE_H__
#include <stdint.h>

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/*----------------------------------------------*
 * The common data type, will be used in the whole project.*
 *----------------------------------------------*/

typedef unsigned char           AR_U8;
typedef unsigned short          AR_U16;
typedef unsigned int            AR_U32;
typedef signed char             AR_S8;
typedef short                   AR_S16;
typedef int                     AR_S32;
typedef float                   AR_FLOAT;
typedef double                  AR_DOUBLE;
typedef unsigned long long      AR_U64;
typedef long long               AR_S64;
typedef char                    AR_CHAR;
typedef unsigned char           AR_UCHAR;
typedef unsigned long           AR_HANDLE;

#define AR_VOID                 void

/*----------------------------------------------*
 * const defination                             *
 *----------------------------------------------*/


#ifndef NULL
    #define NULL        0L
#endif

#define AR_NULL         0L
#define AR_SUCCESS      0
#define AR_FAILURE      (-1)


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __MPI_TYPE_H__ */

