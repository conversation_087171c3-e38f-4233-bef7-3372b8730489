/**
 * \file
 * \brief 描述图像匹配相关的通用数据结构
 */
#ifndef __AR_COMM_DPU_MATCH_H__
#define __AR_COMM_DPU_MATCH_H__

#include "hal_errno.h"
#include "ar_comm_video.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define DPU_MATCH_LEFT_PIPE 0
#define DPU_MATCH_RIGHT_PIPE 1

typedef AR_S32 DPU_MATCH_GRP;
typedef AR_S32 DPU_MATCH_PIPE;
typedef AR_S32 DPU_MATCH_CHN;

#define AR_ERR_DPU_MATCH_NULL_PTR        AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NULL_PTR)
#define AR_ERR_DPU_MATCH_NOTREADY        AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_SYS_NOTREADY)
#define AR_ERR_DPU_MATCH_INVALID_DEVID   AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_DEVID)
#define AR_ERR_DPU_MATCH_INVALID_CHNID   AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_CHNID)
#define AR_ERR_DPU_MATCH_EXIST           AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_EXIST)
#define AR_ERR_DPU_MATCH_UNEXIST         AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_UNEXIST)
#define AR_ERR_DPU_MATCH_NOT_SUPPORT     AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_SUPPORT)
#define AR_ERR_DPU_MATCH_NOT_PERM        AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_PERM)
#define AR_ERR_DPU_MATCH_NOMEM           AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOMEM)
#define AR_ERR_DPU_MATCH_NOBUF           AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOBUF)
#define AR_ERR_DPU_MATCH_ILLEGAL_PARAM   AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_ILLEGAL_PARAM)
#define AR_ERR_DPU_MATCH_BUSY            AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUSY)
#define AR_ERR_DPU_MATCH_BUF_EMPTY       AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUF_EMPTY)
#define AR_ERR_DPU_MATCH_BUF_FULL        AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUF_FULL)
#define AR_ERR_DPU_MATCH_TIMEOUT         AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_TIMEOUT)

#define AR_ERR_DPU_MATCH_OPEN_FILE       AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_DPU_MATCH_OPEN_FILE)
#define AR_ERR_DPU_MATCH_READ_FILE       AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_DPU_MATCH_READ_FILE)
#define AR_ERR_DPU_MATCH_WRITE_FILE      AR_MPP_DEF_ERR(AR_ID_DPU_MATCH, HAL_ERR_LEVEL_ERROR, HAL_ERR_DPU_MATCH_WRITE_FILE)

typedef struct arDPU_MATCH_MEM_INFO_S {
    AR_U64  u64PhyAddr;
    AR_U64  u64VirAddr;
    AR_U32  u32Size;
} DPU_MATCH_MEM_INFO_S;

typedef enum arDPU_MATCH_MASK_MODE_E {
    DPU_MATCH_MASK_DEFAULT_MODE  =  0x0,
    DPU_MATCH_MASK_1X1_MODE      =  0x1,
    DPU_MATCH_MASK_3X3_MODE      =  0x2,
    DPU_MATCH_MASK_5X5_MODE      =  0x3,
    DPU_MATCH_MASK_7X7_MODE      =  0x4,
    DPU_MATCH_MASK_9X9_MODE      =  0x5,
    DPU_MATCH_MASK_MODE_BUTT
} DPU_MATCH_MASK_MODE_E;

typedef enum arDPU_MATCH_DENS_ACCU_MODE_E {
    DPU_MATCH_DENS_ACCU_MODE_D0_A9  =  0x0,
    DPU_MATCH_DENS_ACCU_MODE_D1_A8  =  0x1,
    DPU_MATCH_DENS_ACCU_MODE_D2_A7  =  0x2,
    DPU_MATCH_DENS_ACCU_MODE_D3_A6  =  0x3,
    DPU_MATCH_DENS_ACCU_MODE_D4_A5  =  0x4,
    DPU_MATCH_DENS_ACCU_MODE_D5_A4  =  0x5,
    DPU_MATCH_DENS_ACCU_MODE_D6_A3  =  0x6,
    DPU_MATCH_DENS_ACCU_MODE_D7_A2  =  0x7,
    DPU_MATCH_DENS_ACCU_MODE_D8_A1  =  0x8,
    DPU_MATCH_DENS_ACCU_MODE_D9_A0  =  0x9,
    DPU_MATCH_DENS_ACCU_MODE_BUTT
} DPU_MATCH_DENS_ACCU_MODE_E;

typedef enum arDPU_MATCH_SPEED_ACCU_MODE_E {
    DPU_MATCH_SPEED_ACCU_MODE_SPEED =  0x0,
    DPU_MATCH_SPEED_ACCU_MODE_ACCU  =  0x1,
    DPU_MATCH_SPEED_ACCU_MODE_BUTT
} DPU_MATCH_SPEED_ACCU_MODE_E;

typedef enum arDPU_MATCH_DISP_SUBPIXEL_E {
    DPU_MATCH_DISP_SUBPIXEL_DISABLE  =  0x0,
    DPU_MATCH_DISP_SUBPIXEL_ENABLE   =  0x1,
    DPU_MATCH_DISP_SUBPIXEL_BUTT
} DPU_MATCH_DISP_SUBPIXEL_E;

typedef struct arDPU_MATCH_SGM_PARAM_S {
    AR_BOOL bSgm720pPlanb;        /* SGM pyramid scheme, using three-tiered pyramid,
                                     if there is too many cache miss and rm speckle too long, using plan B; default plan A */
    AR_BOOL bSgmLrcBypass;        /* SGM lrc check bypass, default 0, not bypass */
    AR_BOOL bSgmLrcUniqueCheck;   /* SGM lrc unique check, 0: not check, 1: check */
    AR_U16  u16SpeckleArea;       /* Window size required to eliminate speckle noise, Range:[0, 4095], default 16 */
    AR_U16  u16SgmParaP1;         /* Small penalty item of Cost Compute, Range:[0, 255], default 10 */
    AR_U16  u16SgmParaP2;         /* Big penalty item of Cost Compute, Range:[0, 255], default 150 */
    AR_U16  u16WtaUniqueRatio;    /* Wta unique ratio of LR check, Range:[0, 127], default 26 */
    AR_U16  u16LrcBaseThreshold;  /* Base judgement threshold of LR check, skip LRC check of current pixel
                                     when base disparity great than this param, Range:[0, 15], default 1 */
    AR_U16  u16LrcDispThreshold;  /* Disparity check threshold of LR check, invalid when diff value between
                                    left disp and right disp great than this param, Range:[0, 255], default 16 */
} DPU_MATCH_SGM_PARAM_S;

typedef struct arDPU_MATCH_GRP_ATTR_S {
    SIZE_S stLeftImageSize;                      /* Left image size. */
    SIZE_S stRightImageSize;                     /* Right image size. */
    DPU_MATCH_SGM_PARAM_S stSgmParam;            /* SGM parameter. */
    DPU_MATCH_MASK_MODE_E enMatchMaskMode;       /* Aggregation mask mode. */
    DPU_MATCH_DENS_ACCU_MODE_E enDensAccuMode;   /* Adjust density-accuracy trade-off. */
    DPU_MATCH_SPEED_ACCU_MODE_E enSpeedAccuMode; /* Adjust speed-accuracy trade-off. */
    DPU_MATCH_DISP_SUBPIXEL_E enDispSubpixelEn;  /* Calculate subpixel disparity or not. */
                                                 /* u16DispNum currently not used as disparity need
                                                  * to set same size to src input */
    AR_U16 u16DispNum;                           /* The number of disparity, it must be the multiple of 64,
                                                    Range:[64, 256], default 64 */
    AR_U16 u16DispStartPos;                      /* Minimum start disparity, Range:[0, 49] */
    AR_U32 u32Depth;                             /* The depth of user image queue for getting Match output image,
                                                    it can not be changed dynamic. Range:[0, 8] */
    AR_BOOL bNeedSrcFrame;                       /* The flag of getting source videoframe. It will effect when bind dpu rect. */
    DPU_MATCH_MEM_INFO_S stAssistBuf;            /* Assistance buffer. */
    FRAME_RATE_CTRL_S stFrameRate;               /* Group frame rate contrl. */
} DPU_MATCH_GRP_ATTR_S;

typedef struct arDPU_MATCH_CHN_ATTR_S {
    // NOTICE: Currently DPU disparity size only support size equal to src frame.
    // For 1080P, its' width can be halved.
    SIZE_S stImageSize;  /* Output image size. */
} DPU_MATCH_CHN_ATTR_S;

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __AR_COMM_DPU_MATCH_H__ */
