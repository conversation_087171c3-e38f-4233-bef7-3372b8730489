#ifndef __HAL_VPS_GDC_H__
#define __HAL_VPS_GDC_H__

#include "ar_comm_video.h"
#include "hal_type.h"
#include "hal_sys.h"
#include "hal_npu_types.h"
#include "hal_dbglog.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */


#define AR_HAL_MOD_GDC          HAL_TAG_ID(AR_SYS_ID_GDC)

#define AR_VPS_GDC_DEV "/dev/argdc"

//=========================93 Structs And Apis====================================================//

#define GDC_LUT_MATRIX_ROW 65
#define GDC_LUT_MATRIX_COL 65

#define GDC_LUT_AXIS_STRIDE  (((GDC_LUT_MATRIX_COL + 3) /4)*4)
#define VPS_GDC_ION_SIZE_MAX  (GDC_LUT_AXIS_STRIDE*GDC_LUT_MATRIX_ROW*4 +1024)

#define GDC_MAX_PANNEL_COUNT 4

typedef enum
{
    AR_GDC_IOC_START           = 1,
    AR_GDC_IOC_SET_FREQUENCY,
    AR_GDC_IOC_GET_TIME,
}ENUM_GDC_RPC_ID;

typedef struct
{
  int width;
  int height;
  int luma_stride;
  int chroma_stride;
}ar_gdc_buffer_format_t;

typedef struct
{
   unsigned long long addr_virt;
   unsigned long long addr_phy;
}ar_gdc_buffer_pannel_t;

typedef struct
{
  ar_gdc_buffer_pannel_t pannel[GDC_MAX_PANNEL_COUNT]; //GDC DATA 地址
  ar_gdc_buffer_format_t format;  //GDC 图像格式
}AR_GDC_BUFFER_S;


typedef struct
{
	AR_GDC_BUFFER_S in_buffer;   //GDC 图像输入
	AR_GDC_BUFFER_S out_buffer;  //GDC 图像输出
	uint32_t rotate_angle;       //0: 0, 1: 90, 2:180, 3:270  counter-clockwise
	uint32_t transform_ex;       //0: Standard Rotate(0/90/180/270) Non-0:Other GDC Operations
	uint32_t lut_pa;             //LUT 物理地址
	uint32_t lut_len;            //LUT 数据长度
} AR_GDC_PARAMS_S;


/**
* @brief 用户态实现图像:旋转,水平/垂直翻转,LDC功能
* @param pstGdcParams GDC参数
* @retval 0 成功 其他 失败
* @note  输入输出地址为有效物理地址
**/
AR_S32 ar_hal_vps_gdc_general(AR_GDC_PARAMS_S *pstGdcParams);
AR_S32 ar_hal_gdc_set_frequency(AR_U32 u32FreqMHz);
AR_S32 ar_hal_gdc_get_time(AR_FLOAT *fpTime);

/**
* @brief  将GDC挂起，power off
* @param  NULL
* @retval 0 成功，非零值 失败.
* @note  在挂起之前请调用者自行保证没有正在运行的任务
*/
AR_S32 ar_hal_gdc_suspend(void);

/**
* @brief  将GDC唤醒，power on
* @param  NULL
* @retval 0 成功，非零值 失败.
* @note  唤醒后，原来由调用者自行设置的频率需要调用者负责恢复
*/
AR_S32 ar_hal_gdc_resume(void);


//=========================94 Structs And Apis====================================================//

typedef enum
{
    AR_GDC_ADV_IOC_PROCESS           = 1,
    AR_GDC_ADV_IOC_CONFIG,
    AR_GDC_ADV_IOC_CONFIG_CPUBPLINE,
    AR_GDC_ADV_IOC_START,
    AR_GDC_ADV_IOC_STOP,
    AR_GDC_ADV_IOC_PWR_ON,
    AR_GDC_ADV_IOC_PWR_OFF,
    AR_GDC_ADV_IOC_QUERY,
    AR_GDC_ADV_IOC_GET_FRMPTS,
    AR_GDC_ADV_IOC_SET_FREQUENCY,
    AR_GDC_ADV_IOC_GET_TIME,
    AR_GDC_ADV_IOC_START_COMPAT,
}ENUM_GDC_ADV_RPC_ID;

typedef struct
{
	AR_U8 u8Src1pnb;		//0:1Byte;   1:2Byte;   2:4Byte;   3:8Byte; (2,3 not for picture)
	AR_U8 u8SrcEndian;		//0:little;  1:big; 
	AR_U8 u8SrcSigned;		//0:unsigned;  1:signed
	AR_U8 u8SrcC3Downscaler;
	AR_U8 u8SrcC3Enable;
	AR_U8 u8SrcC2Downscaler;
	AR_U8 u8SrcC2Enable;
	AR_U8 u8SrcC1Downscaler;
	AR_U8 u8SrcC1Enable;
	AR_U8 u8SrcC0Downscaler;
	AR_U8 u8SrcC0Enable;
} AR_GDC_CHN_CTR_S;

typedef struct
{
	AR_U8 u8MeshB1f16xExtDisable;  //0:enable X;  1:disable(x = xcnt);  ## valid at mesh_mode==2 ##
	AR_U8 u8MeshB1f16xSigned;       //0:16bit-X is unsigned;	1:X is signed; 
	AR_U8 u8MeshB1f16xExtFloatBits;
	AR_U8 u8MeshB1f16xOffsetFloatBits;
	AR_U16 u16MeshB1f16xOffset;
} AR_GDC_MESHX_CTR_S;

typedef struct
{
	AR_U8 u8MeshB1f16yExtDisable;  //0:enable X;  1:disable(x = xcnt);  ## valid at mesh_mode==2 ##
	AR_U8 u8MeshB1f16ySigned;       //0:16bit-X is unsigned;	1:X is signed; 
	AR_U8 u8MeshB1f16yExtFloatBits;
	AR_U8 u8MeshB1f16yOffsetFloatBits;
	AR_U16 u16MeshB1f16yOffset;
} AR_GDC_MESHY_CTR_S;


typedef struct
{
	AR_U32 u32MeshMode;
	AR_U32 u32MeshAddr;
	AR_U32 u32MeshStride;
	AR_GDC_MESHX_CTR_S stMeshxCtrl;
	AR_GDC_MESHY_CTR_S stMeshyCtrl;
} AR_GDC_MESH_CTR_S;

typedef struct
{
	AR_U32 u32WarpMode;   //0:use ext-warp;  （ddr中）  1:used apb-matrix;（寄存器中）   2:disable; （关闭）
	AR_U32 u32WarpFlushCnt;
} AR_GDC_WARP_MODE_S;

typedef struct
{
	AR_GDC_WARP_MODE_S stWarpMode;
	AR_U32 u32WarpAddr;
	AR_U32 u32ApbMatrix[9];
} AR_GDC_WARP_CTR_S;


typedef struct
{
	AR_U32 u32WeightFlush;
	AR_U32 u32WeightMode; // 0:bilinear interpolation;  1:use ext-weight;   ## valid at mesh_mode==0 ##
} AR_GDC_WEIGHT_MODE_S;

typedef struct
{
	AR_GDC_WEIGHT_MODE_S stWeightMode;
	AR_U32 u32WeightAddr;
} AR_GDC_WEIGHT_CTR_S;


typedef struct
{
	AR_U32 u32PaddingValue;
	AR_U32 u32PaddingMode;
} AR_GDC_PADDING_CTR_S;

typedef struct
{
	AR_U8 u8InterpMode; //0: bilinear;  1:nearest-neighbor;
	AR_U8 u8Rotation;           //[7:4] mirror rotation:0:disable;1:H-mirror;2:V-mirror;
								//[3:0] angle rotation: 0:disable; 1:0°;2:90°;3:180°;4:270°;  (anti-clock) mirror first; =0 rotation disable;
} AR_GDC_ROT_CTRL_S;

typedef struct
{
	AR_U16 u16PixMax;
	AR_U16 u16PixMin;
} AR_GDC_PIXEL_CTRL_S;

typedef struct
{	
	AR_U8 u8InBpEn;
	AR_U8 u8InBpLdProtEn;
	uint16_t u8InBpLines;
} AR_GDC_IN_BP_CTRL_S;

typedef struct
{	
	AR_U8 u8CpuBpEn;
	AR_U16 u16CpuBpLines;
} AR_GDC_CPU_BP_CTRL_S;

typedef struct
{	
	AR_U8 u8OutBpEn;
	AR_U8 u8OutBpFast;
	AR_U16 u16OutBpLines;
} AR_GDC_OUT_BP_CTRL_S;

typedef struct
{
	AR_GDC_IN_BP_CTRL_S stInBp;
	AR_GDC_CPU_BP_CTRL_S stCpuBp;
	AR_GDC_OUT_BP_CTRL_S stOutBp;
} AR_GDC_LD_CTRL_S;

typedef struct
{
	AR_U8 u8ClkGatDisable;
	AR_U8 u8AxiQos;
	AR_U8 u8AxiProt;
	AR_U8 u8AxiCache;
} AR_GDC_AXI_CTRL_S;

typedef struct
{
	AR_U8 u8StartMode;   //	0:normal;	
						  //	1:auto #at frame done atuo start next frame(with input-BP-port, for LowDelay);
						  //	2:shadow #regs have double group,  ""frame_start"" can be sent early 
						  //	3:free #one frame done, immediately start next(auto disable in/cpu_bp)
	AR_U8 u8SafetyStart;
	AR_U8 u8FrameStart;
} AR_GDC_START_CTRL_S;

typedef struct
{
	AR_U8 u8CplxPadCtl ;	
	AR_U8 u8BackPixelCtl ;  //0:flip; 1:padding; (when coor_w<0)
	AR_U8 u8DstRoiCtl ;	  //0:disable; 1:roi-in;  2:roi-out; 
	AR_U8 u8SrcRoiCtl ;	  //0:disable; 1:roi-in;  2:roi-out; 
} AR_GDC_ROI_EXT_CTRL_S;

typedef struct
{
	AR_U16 u16Start ;	
	AR_U16 u16End ;
} AR_GDC_ROI_POINT_S;

typedef struct
{
	AR_U16 u16C1;
	AR_U16 u16C0;	
} AR_GDC_ROI_PADDING0_S;

typedef struct
{
	AR_U16 u16C3;
	AR_U16 u16C2;	
} AR_GDC_ROI_PADDING1_S;

typedef struct
{
	AR_GDC_ROI_EXT_CTRL_S stExtCtrl;
	AR_GDC_ROI_POINT_S stSrcRoiWidth;
	AR_GDC_ROI_POINT_S stSrcRoiHeight;
	AR_GDC_ROI_POINT_S stDstRoiWidth;
	AR_GDC_ROI_POINT_S stDstRoiHeight;
	AR_GDC_ROI_PADDING1_S stExtPadding1;
	AR_GDC_ROI_PADDING0_S stExtPadding0;
} AR_GDC_ROI_CTRL_S;

typedef struct
{
	AR_GDC_CHN_CTR_S stChannelCfg;
	AR_GDC_MESH_CTR_S stMeshCfg;
	AR_GDC_WARP_CTR_S stWarpCfg;
	AR_GDC_WEIGHT_CTR_S stWeightCfg;
	AR_GDC_PADDING_CTR_S stPaddingCfg;
	AR_GDC_ROT_CTRL_S stRotCfg;
	AR_GDC_PIXEL_CTRL_S stPixelCfg;
	AR_GDC_LD_CTRL_S stLdCfg;
	AR_GDC_AXI_CTRL_S stAxiCfg;
	AR_GDC_ROI_CTRL_S stRoiCfg;
	AR_GDC_START_CTRL_S stStartCfg;
} AR_GDC_ADV_CFG_S;


typedef enum
{
	AR_GDC_LD_MODE_NONE   = 0,
	AR_GDC_LD_MODE_IN     = 1,
	AR_GDC_LD_MODE_OUT    = 2,
	AR_GDC_LD_MODE_IO     = 3,
	AR_GDC_LD_MODE_BUTT
} AR_GDC_LD_MODE_E;

typedef struct 
{
	PIXEL_FORMAT_E enFormat;
	AR_U32 u32PlanarNum;
	AR_U32 u32Width[4];
	AR_U32 u32Lines64Enable; //default lb 32 lines, 64 lines only use for 1080p
    ENMU_SYS_HARDWARE_LOWDEALY_MODE enLowdelayMode;
    ENMU_SYS_HARDWARE_SRC enSrcSel;
} AR_GDC_LOWDELAY_INFO_S;

typedef struct
{
	AR_GDC_LD_MODE_E  enLdMode;
	AR_GDC_LOWDELAY_INFO_S  stInLowdelay;
	AR_GDC_LOWDELAY_INFO_S  stOutLowdelay;
} AR_GDC_LOWDELAY_PARAM_S;

typedef enum
{
	AR_GDC_CF50_MODE_NONE 	   = 0,
	AR_GDC_CF50_MODE_DECODE    = 1,
	AR_GDC_CF50_MODE_ENCODE    = 2,
	AR_GDC_CF50_MODE_TRANSCODE = 3,
	AR_GDC_CF50_MODE_BUTT
} AR_GDC_CF50_MODE_E;

typedef struct 
{
	ENMU_SYS_PIXEL_FORMAT       enFormat;
	AR_U32                      u32Width;
    AR_U32                      u32Height;
    AR_U32                      u32Align;
    AR_BOOL                     bLossy;
    CF50_COMPRESS_RATIO_ENUM    enRatioTo;
    AR_U32                      u32Cf50HeaderPhy[4];
    AR_UINTPTR                  uptrCf50HeaderVirt[4];
    AR_BOOL                     bDisablePingPongbuf;
} AR_GDC_CF50_INFO_S;

typedef struct
{
	AR_GDC_CF50_MODE_E  enCF50Mode;
	AR_GDC_CF50_INFO_S  stCF50Decoder;
	AR_GDC_CF50_INFO_S  stCF50Encoder;
} AR_GDC_CF50_PARAM_S;

typedef struct
{
    AR_U32 u32IsVB;
    AR_U32 u32FrameId;
    AR_IMG_FORMAT_E enFormat;
    AR_U32 u32Width;
    AR_U32 u32Height;
    AR_U32 u32ChannelNum;
	AR_U32 u32DataLen;
    AR_IMG_CHANNEL_S astChannels[4];
} AR_GDC_IMG_S;

typedef struct
{
	AR_S32 s32CoreId;
	AR_S32 s32NonBlock; //options: 0:block 1:non-block
	AR_GDC_IMG_S stInBuffer;  
	AR_GDC_IMG_S stOutBuffer;  
	AR_GDC_ADV_CFG_S stGdcParam;
	AR_U32 u32FrmId;
	AR_U64 u64FrmPts;
	AR_U8  u8TaskMode; //0:normal 1:over-write
	AR_U8  u8LdEn;
	AR_U8  u8Cf50En;
	AR_GDC_LOWDELAY_PARAM_S stLdParam;
	AR_GDC_CF50_PARAM_S stCF50Param;
} AR_GDC_ADV_TRANSFORM_S;

typedef struct
{
	AR_S32 s32CoreId;
	AR_S32 s32LineNum;
} AR_GDC_ADV_LINE_PARAMS_S;

typedef struct
{
	AR_S32 s32CoreId;
	AR_U32 u32FrmId;
	AR_U64 u64FrmPts;
} AR_GDC_ADV_FRMPTS_PARAMS_S;


AR_S32 ar_hal_vps_gdc_adv_process(AR_GDC_ADV_TRANSFORM_S *pstGdcParams);
AR_S32 ar_hal_vps_gdc_adv_config(AR_GDC_ADV_TRANSFORM_S *pstGdcParams);
AR_S32 ar_hal_vps_gdc_adv_config_cpubp_line(AR_GDC_ADV_LINE_PARAMS_S *pstGdcLineParams);
AR_S32 ar_hal_vps_gdc_adv_start(AR_GDC_ADV_TRANSFORM_S *pstGdcParams);
AR_S32 ar_hal_vps_gdc_adv_stop(AR_S32 s32CoreId);
AR_S32 ar_hal_vps_gdc_adv_pwron(AR_S32 s32CoreId);
AR_S32 ar_hal_vps_gdc_adv_pwroff(AR_S32 s32CoreId);
AR_S32 ar_hal_vps_gdc_adv_get_frmpts(AR_GDC_ADV_FRMPTS_PARAMS_S *pstGdcFrmPtsParams);
AR_S32 ar_hal_vps_gdc_adv_query(AR_GDC_ADV_TRANSFORM_S *pstGdcParams);


#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */


#endif //__HAL_EIS_H__

