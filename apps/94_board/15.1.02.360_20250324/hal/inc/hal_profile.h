#ifndef __HAL_PROFILE_H__
#define __HAL_PROFILE_H__

#ifdef __cplusplus
#if __cplusplus
	extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#include "hal_type.h"
#include "osal_lock.h"
#include "utils_list.h"
#include "binder_ipc.h"

//使用profile统计函数运行时间时定义enable宏定义
//#define AR_PROFILE_ENABLE

#define AR_HAL_MAX_PROFILE   1024

typedef enum
{
	PROFILE_UID_CORE = 0,
	PROFILE_UID_HAL = 1,
	PROFILE_UID_MPP = 2,
	PROFILE_UID_APP = 3,
	PROFILE_UID_MAX
} AR_HAL_PROFILE_UID_E;

typedef struct
{
    struct ar_list_head next;
	AR_CHAR moudle[64];
    AR_CHAR function_name[128];
    AR_U64 	tic;
    AR_U64 	toc;
} AR_HAL_PROFILE_S;

typedef struct 
{
    struct ar_list_head profile_list_head;
    ar_lock_id_t mutex;
	BINDER_CMD_T *binder_cmd;
    AR_U32 total_profile_num;
} AR_HAL_PROFILES_HANDLE_S;

/**
* @brief  在用户态释放profile.
* @retval 0 成功 , 其它 失败.
* @note   用户态使用profile进行时间统计时需调用主动释放profile
*/

AR_S32 ar_hal_profile_exit();

/**
* @brief  在用户态统计profile起始时间.
* @param  enUid 当前模块所在，core：PROFILE_UID_CORE，hal：PROFILE_UID_HAL， mpp：PROFILE_UID_MPP， app：PROFILE_UID_APP.
* @param  pchMoudle 时间统计函数所在模块.
* @param  pchFuncName 时间统计函数名称.
* @retval 0 成功 , 其它 失败.
* @note   与ar_hal_profile_end 函数配合使用
*/

AR_S32 ar_hal_profile_start(AR_HAL_PROFILE_UID_E enUid, const AR_CHAR * pchMoudle, const AR_CHAR * pchFuncName);

/**
* @brief  在用户态统计profile终止时间.
* @param  enUid 当前模块所在，core：PROFILE_UID_CORE，hal：PROFILE_UID_HAL， mpp：PROFILE_UID_MPP， app：PROFILE_UID_APP.
* @param  pchMoudle 时间统计函数所在模块.
* @param  pchFuncName 时间统计函数名称.
* @retval 0 成功 , 其它 失败.
* @note   与ar_hal_profile_start 函数配合使用
* @note   单进程模式下使用bdcmd dump_profile --help获取统计信息
* @note   多进程模式下使用bdcmd dump_profile_core --help 获取core层统计信息，使用bdcmd dump_profile --help获取hal/mpp/app层统计信息
*/
AR_S32 ar_hal_profile_end(AR_HAL_PROFILE_UID_E enUid, const AR_CHAR * pchMoudle, const AR_CHAR * pchFuncName);

#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */

#endif /* __HAL_PROFILE_H__ */
