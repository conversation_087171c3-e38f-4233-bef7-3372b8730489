#ifndef __HAL_DP_H__
#define __HAL_DP_H__

#ifdef __cplusplus
#if __cplusplus
	extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#include "hal_type.h"
#include "hal_errno.h"
#include "hal_comm.h"

#include <misc/ar_dp_rx_uapi.h>

#define HAL_ERR_DP_INVALID_CHNID        AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_CHNID)
#define HAL_ERR_DP_INVALID_DEVID        AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_INVALID_DEVID)
#define HAL_ERR_DP_TIMEOUT              AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_TIMEOUT)
#define HAL_ERR_DP_NULL_PTR             AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_NULL_PTR)
#define HAL_ERR_DP_NOMEM                AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOMEM)
#define HAL_ERR_DP_NOBUF                AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOBUF)
#define HAL_ERR_DP_UNEXIST              AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_UNEXIST)
#define HAL_ERR_DP_ILLEGAL_PARAM        AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_ILLEGAL_PARAM)
#define HAL_ERR_DP_NOT_READY            AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_SYS_NOTREADY)
#define HAL_ERR_DP_BUSY                 AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_BUSY)
#define HAL_ERR_DP_NOT_PERM             AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_NOT_PERM)
#define HAL_ERR_DP_SIZE_NOT_ENOUGH      AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_SIZE_NOT_ENOUGH)
#define HAL_ERR_DP_BADADDR              AR_HAL_DEF_ERR(AR_SYS_ID_DP, HAL_ERR_LEVEL_ERROR, HAL_ERR_BADADDR)


AR_S32 ar_hal_dp_rx_get_link_state_change(struct artosyn_dprx_config * config);
AR_S32 ar_hal_dp_rx_get_video_format_change(struct artosyn_dprx_config * config);
AR_S32 ar_hal_dp_rx_get_audio_format_change(struct artosyn_dprx_config * config);

AR_S32 ar_hal_dp_rx_get_link_state_change_nowait(struct artosyn_dprx_config * config);
AR_S32 ar_hal_dp_rx_get_video_format_change_nowait(struct artosyn_dprx_config * config);
AR_S32 ar_hal_dp_rx_get_audio_format_change_nowait(struct artosyn_dprx_config * config);
AR_S32 ar_hal_dp_rx_get_all_info_nowait(struct artosyn_dprx_config * config);

AR_S32 ar_hal_dp_rx_get_edid(AR_U8 (*edid)[AR_DPRX_EDID_SIZE]);
AR_S32 ar_hal_dp_rx_set_edid(AR_U8 (*edid)[AR_DPRX_EDID_SIZE]);

AR_S32 ar_hal_dp_rx_get_hpd_status(AR_U32 * hpd_status);
AR_S32 ar_hal_dp_rx_set_hpd_status(AR_U32 hpd_status);

AR_S32 ar_hal_dp_rx_get_vid_clk(AR_U32 * p_vid_clk_rate);
AR_S32 ar_hal_dp_rx_set_vid_clk(AR_U32 vid_clk_rate);

AR_S32 ar_hal_dp_rx_exit_video();
AR_S32 ar_hal_dp_rx_exit_audio();

AR_S32 ar_hal_dp_rx_exit();


#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */


#endif
