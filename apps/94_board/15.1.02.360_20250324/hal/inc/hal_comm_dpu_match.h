/**
 * \file
 * \brief 描述图像匹配相关的通用数据结构
 */
#ifndef __HAL_COMM_DPU_MATCH_H__
#define __HAL_COMM_DPU_MATCH_H__

#include "osal.h"
#include "hal_vb.h"
#include "hal_errno.h"
#include "hal_type.h"
#include "ar_common_define.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define AR_DPU_RPC_NAME   "ar_dpu"
#define AR_DPU_DEV_NAME   "/dev/ar_dpu"
#define AR_DPU_COMP_NAME  "artosyn,dpu"

#define AR_DPU_MAX_CHN_NUM          1
#define AR_DPU_MAX_GRP_NUM          8
#define AR_DPU_MAX_OUTPUT_NUM       8
#define DPU_COMMAND_QUEUE_DEPTH     2

#define AR_DPU_MIN_WIDTH            128
#define AR_DPU_MAX_WIDTH            1920
#define AR_DPU_MIN_HEIGHT           16
#define AR_DPU_MAX_HEIGHT           1080

#define AR_DPU_ALLIGN_128     128  // 16B allign,  16 * 8
#define AR_DPU_ALLIGN_256     256  // 32B allign,  32 * 8
#define AR_DPU_ALLIGN_2048    2048 // 256B allign, 256 * 8
#define DPU_ALIGN_UP(size, align) (((size) + (align) -1L) & ~((align) -1L))

typedef AR_S32 AR_DPU_MATCH_GRP;
typedef AR_S32 AR_DPU_MATCH_CHN;

typedef enum {
    HAL_ERR_DPU_MATCH_SYS_TIMEOUT = HAL_ERR_BUTT + 1, /* DPU MATCH process timeout */
    HAL_ERR_DPU_MATCH_OPEN_FILE,                      /* DPU MATCH open file error */
    HAL_ERR_DPU_MATCH_READ_FILE,                      /* DPU MATCH read file error */
    HAL_ERR_DPU_MATCH_WRITE_FILE,                     /* DPU MATCH write file error */

    HAL_ERR_DPU_MATCH_BUTT
} ENUM_EN_DPU_MATCH_ERR_CODE;

typedef struct {
    AR_U64  u64_phy_addr;
    AR_U64  u64_vir_addr;
    AR_U32  u32_size;
} STRU_DPU_MATCH_MEM_INFO;

typedef enum {
    AR_DPU_MATCH_MASK_DEFAULT_MODE   =  0x0,
    AR_DPU_MATCH_MASK_1X1_MODE       =  0x1,
    AR_DPU_MATCH_MASK_3X3_MODE       =  0x2,
    AR_DPU_MATCH_MASK_5X5_MODE       =  0x3,
    AR_DPU_MATCH_MASK_7X7_MODE       =  0x4,
    AR_DPU_MATCH_MASK_9X9_MODE       =  0x5,
    AR_DPU_MATCH_MASK_MODE_BUTT
} ENUM_DPU_MATCH_MASK_MODE;

typedef enum {
    AR_DPU_MATCH_DENS_ACCU_MODE_D0_A9  =  0x0,
    AR_DPU_MATCH_DENS_ACCU_MODE_D1_A8  =  0x1,
    AR_DPU_MATCH_DENS_ACCU_MODE_D2_A7  =  0x2,
    AR_DPU_MATCH_DENS_ACCU_MODE_D3_A6  =  0x3,
    AR_DPU_MATCH_DENS_ACCU_MODE_D4_A5  =  0x4,
    AR_DPU_MATCH_DENS_ACCU_MODE_D5_A4  =  0x5,
    AR_DPU_MATCH_DENS_ACCU_MODE_D6_A3  =  0x6,
    AR_DPU_MATCH_DENS_ACCU_MODE_D7_A2  =  0x7,
    AR_DPU_MATCH_DENS_ACCU_MODE_D8_A1  =  0x8,
    AR_DPU_MATCH_DENS_ACCU_MODE_D9_A0  =  0x9,
    AR_DPU_MATCH_DENS_ACCU_MODE_BUTT
} ENUM_DPU_MATCH_DENS_ACCU_MODE;

typedef enum {
    AR_DPU_MATCH_SPEED_ACCU_MODE_SPEED =  0x0,
    AR_DPU_MATCH_SPEED_ACCU_MODE_ACCU  =  0x1,
    AR_DPU_MATCH_SPEED_ACCU_MODE_BUTT
} ENUM_DPU_MATCH_SPEED_ACCU_MODE;

typedef enum {
    AR_DPU_MATCH_DISP_SUBPIXEL_DISABLE  =  0x0,
    AR_DPU_MATCH_DISP_SUBPIXEL_ENABLE   =  0x1,
    AR_DPU_MATCH_DISP_SUBPIXEL_BUTT
} ENUM_DPU_MATCH_DISP_SUBPIXEL;

typedef enum {
    AR_PIXEL_FORMAT_YVU_PLANAR_422,
    AR_PIXEL_FORMAT_YVU_PLANAR_420,
    AR_PIXEL_FORMAT_YVU_PLANAR_444,
    AR_PIXEL_FORMAT_YUV_400,

    /* SVP data format */
    AR_PIXEL_FORMAT_S8C1,
    AR_PIXEL_FORMAT_S8C2_PACKAGE,
    AR_PIXEL_FORMAT_S8C2_PLANAR,
    AR_PIXEL_FORMAT_S16C1,
    AR_PIXEL_FORMAT_U8C1,
    AR_PIXEL_FORMAT_U16C1,
    AR_PIXEL_FORMAT_S32C1,
    AR_PIXEL_FORMAT_U32C1,
} ENUM_PIXEL_FORMAT;

typedef enum {
    AR_DPU_MATCH_CMD_CREATE_GROUP = AR_COMMAND_DPU_BASE, // 0x0
    AR_DPU_MATCH_CMD_DESTORY_GROUP,                      // 0x1
    AR_DPU_MATCH_CMD_SET_GROUP_ATTR,                     // 0x2
    AR_DPU_MATCH_CMD_GET_GROUP_ATTR,                     // 0x3
    AR_DPU_MATCH_CMD_START_GROUP,                        // 0x4
    AR_DPU_MATCH_CMD_STOP_GROUP,                         // 0x5
    AR_DPU_MATCH_CMD_SET_CHN_ATTR,                       // 0x6
    AR_DPU_MATCH_CMD_GET_CHN_ATTR,                       // 0x7
    AR_DPU_MATCH_CMD_ENABLE_CHN,                         // 0x8
    AR_DPU_MATCH_CMD_DISABLE_CHN,                        // 0x9
    AR_DPU_MATCH_CMD_SEND_FRAME,                         // 0xA
    AR_DPU_MATCH_CMD_GET_FRAME,                          // 0xB
    AR_DPU_MATCH_CMD_RELEASE_FRAME,                      // 0xC
    AR_DPU_MATCH_CMD_QUERY_STATUS,                       // 0xD
    AR_DPU_MATCH_CMD_INIT_DEVICE,                        // 0xE
} ENUM_DPU_MATCH_CMD;

typedef struct ar_dpu_match_grp_drv_attr {
    int grp_id;                         /**< 读写; 要创建的DPU Group ID, 取值范围:[0, 8); 静态属性 */

    // dpu_reg0
    unsigned short width;               /**< 读写; 计算视差的原图宽度，取值范围:[128,1920]; 动态属性 */
    unsigned short height;              /**< 读写; 计算视差的原图高度，取值范围:[16,1080]; 动态属性 */

    // dpu_reg2
    unsigned short sub_pixel_en;        /**< 读写; 是否使能亚像素差值,取值范围:[0, 1]; 动态参数*/
    unsigned short sgm_lrc2_unique;     /**< 读写; 是否使能level2的left&right check唯一性约束, 取值范围:[0,1]; 动态属性*/
    unsigned short sgm_lrc1_unique;     /**< 读写; 是否使能level1的left&right check唯一性约束, 取值范围:[0,1]; 动态属性*/
    unsigned short sgm_lrc0_unique;     /**< 读写; 是否使能level0的left&right check唯一性约束, 取值范围:[0,1]; 动态属性*/
    unsigned short post_ups_bypass;     /**< 读写; 是否bypass post的的upscale,取值范围:[0, 1]; 动态参数*/
    unsigned short post_mfilter_Bypass; /**< 读写; 是否是能视差3X3的中值滤波,取值范围:[0, 1]; 动态参数*/
    unsigned short rm_speckle_bypass;   /**< 读写; 是否bypass 去斑点,取值范围:[0, 1]; 动态参数*/
    unsigned short dpu_post_bypass;     /**< 读写; 是否bypass 后处理, 取值范围:[0, 1]; 动态参数*/
    unsigned short sgm_l2_ups_bypass;   /**< 读写; 是否bypass level2的upscale, 取值范围:[0, 1]; 动态参数*/
    unsigned short sgm_lrc2_bypass;     /**< 读写; 是否bypass level2的left&right check, 取值范围:[0, 1]; 动态参数*/
    unsigned short sgm_lrc1_bypass;     /**< 读写; 是否bypass level1的left&right check, 取值范围:[0, 1]; 动态参数*/
    unsigned short sgm_lrc0_bypass;     /**< 读写; 是否bypass level0的left&right check, 取值范围:[0, 1]; 动态参数*/
    unsigned short downscale_bypass;    /**< 读写; 是否bypass SGM之前的scale down, 取值范围:[0, 1]; 0: 1/2 down scale; 动态参数*/

    // dpu_reg12
    unsigned short sgm_stride;          /**< 读写; l0 的视差stride，取值范围:[1,4], sgm_start_disp+15*sgm_stride <= 64; DispNum [u16SgmStartDisp*4-7, max(stopDisp, 255)], stopDisp = (u16SgmStartDisp + 15*sgmStride)*4 + 6, 动态属性 */
    unsigned short sgm_start_disp;      /**< 读写; 起始视差，取值范围:[0,49]; 动态属性 */

    // dpu_reg13
    unsigned short sgm_para_p1;         /**< 读写; LR check时候惩罚项P1，取值范围:[0, 255]; 动态参数*/
    unsigned short sgm_para_p2;         /**< 读写; LR check时候惩罚项P2，取值范围:[0, 255]; 动态参数*/

    // dpu_reg14
    unsigned short wta_unique_ratio;    /**< 读写; LR check时候唯一性约束的Rate, 取值范围:[0, 177]; 动态参数*/
    unsigned short lrc_base_threshold;  /**< 读写; LR check基础视差判断阈值，基础视差大于等于该值时，该像素点跳过lrc检查*/
    unsigned short lrc_disp_threshold;  /**< 读写; LR check阈值,当左视差与右视差差值大于该值时无效, 取值范围:[0, 255]; 动态参数*/

    // dpu_reg15
    unsigned short post_wb_save_mode;   /**< 读写; 视差写入DDR存储格式:
                                             0: 将数据按照每个数据16bit保存，高位补符号位；
                                             1: 将数据的小数据去掉，只保留8位整数部分，负数和invalid截断为0；*/

    // dpu_reg16
    unsigned long  lab_phy_addr[2];     /**< 读写; 辅助内存的labal物理地址, 临时缓存用于存储小连通域stage 1产生的label和disp的起始地址, 256B align, 动态参数*/
    unsigned long  lab_vir_addr[2];     /**< 读写; 辅助内存的labal虚拟地址, 动态参数*/
    unsigned int   lab_size[2];         /**< 读写; 辅助内存的labal大小, 动态参数*/

    // dpu_reg17
    unsigned long  cache_phy_addr[2];   /**< 读写; 辅助内存的cache物理地址, 临时缓存用于存储小连通域label的起始地址, 32B align, 动态参数*/
    unsigned long  cache_vir_addr[2];   /**< 读写; 辅助内存的cache虚拟地址, 动态参数*/

    // dpu_reg21
    unsigned short speckle_area;        /**< 读写; 连通域视差值阈值 */

    // accuracy and speed related param
    unsigned long  assist_phy_addr;     /**< 读写; 辅助内存的物理地址, 动态参数*/
    unsigned long  assist_vir_addr;     /**< 读写; 辅助内存的虚拟地址, 动态参数*/
    unsigned int   assist_size;         /**< 读写; 辅助内存的大小, 动态参数*/
    unsigned short depth;               /**< 读写; 输出视差队列的深度,取值范围:[0, 8]; 静态参数*/
    int   pic_line_stride;              /**< 读写; source image line stride, 16B align; 动态参数*/
    int   post_wb_line_stride;          /**< 读写; disparity image line stride, 16B align; 动态参数*/
    int   display_num;
    int   mask_mode;
    int   dens_accu_mode;
    int   need_src_frame;
    int   src_frame_rate;               /**< 读写; 输入的图像对的源帧率; 动态参数*/
    int   dst_frame_rate;               /**< 读写; 输如的图像对的目标帧率; 动态参数*/
} ar_dpu_match_grp_drv_attr_t;

typedef struct ar_dpu_chn_id {
    int grp_id;                       /**< 读写; 要创建的DPU Group ID, 取值范围:[0, 8); 静态属性 */
    int chn_id;                       /**< 读写; 要创建的DPU Channel ID, 取值范围:[0, 1); 静态属性 */
} ar_dpu_chn_id_t;

typedef struct ar_dpu_match_chn_drv_attr {
    int enable;
    int grp_id;                       /**< 读写; 要创建的DPU Group ID, 取值范围:[0, 8); 静态属性 */
    int chn_id;                       /**< 读写; 要创建的DPU Channel ID, 取值范围:[0, 1); 静态属性 */
    unsigned short output_width;      /**< 读写; 输出视差图像的宽度, vga可以放大,720p只能原始大小, 1080p可以缩小; 动态属性*/
    unsigned short output_height;     /**< 读写; 输出视差图像的高度, 高度不支持调整; 动态属性*/
} ar_dpu_match_chn_drv_attr_t;

typedef struct ar_dpu_match_grp_status {
    int      grp_id;                  /**< 读写; 要创建的DPU Group ID, 取值范围:[0, 8); 静态属性 */
    int      queue_depth;
    uint32_t frame_in_num;            /**< 从start开始输入的码流帧数 */
    uint32_t frame_ready_num;         /**< 从start开始输入的码流帧已经解码完成的帧数 */
    uint32_t frame_out_num;           /**< 从start开始解码完成的yuv帧被取出的数 */
    uint32_t frame_droped_num;        /**< 从start开始, 丢弃的解码后的yuv帧数 */
    uint32_t frame_release_num;       /**< 从start开始, display完成显示的帧数 */
    uint32_t frame_irq_num;           /**< 从start开始, stage2中断数 */

    uint64_t start_timestamp;         /**< 第一帧灰度图到达的时间点*/
    uint64_t last_timestamp;          /**< 上次计算统计信息的时间点*/
    uint64_t last_frame_in_num;       /**< 上次计算统计信息的输入码流帧数 */
    uint64_t last_frame_ready_num;    /**< 上次计算统计信息的解码完成的帧数*/
    uint64_t last_frame_out_num;      /**< 上次计算统计信息的被取出的帧数 */
} ar_dpu_match_grp_status_t;

typedef struct ar_dpu_frame {
    bool          is_16bit_out;       /**< 读写; 输出视差图像的宽度 */
    int           grp_id;             /**< 读写; 要创建的DPU Group ID, 取值范围:[0, 8) */
    int           timeout_ms;         /**< 读写; 超时参数,取值范围[-1, +∞], -1为阻塞接口; 0为非阻塞接口,大于0为超时等待时间,单位ms */
    uint64_t      create_time;        /**< 读写; 左右图达到时间 */
    unsigned int  pool_id[3];         /**< 读写; 左右图buffer的vb pool ID */
    unsigned int  mod_id[3];          /**< 读写; 左右图buffer的module ID */
    unsigned int  width[3];           /**< 读写; 左右图luma的宽度,取值范围[128, 1920] */
    unsigned int  height[3];          /**< 读写; 左右图luma的高度,取值范围[16, 1080] */
    unsigned int  stride[3];          /**< 读写; 左右图luma的stride */
    unsigned long phy_addr[3];        /**< 读写; 左右图luma的物理地址 */
    unsigned int  time_ref[3];        /**< 读写; 左右图buffer的TimeRef */
    unsigned long pts[3];             /**< 读写; 左右图buffer的PTS */
} ar_dpu_frame_t;

typedef struct ar_dpu_match_drv_frame {
    unsigned long  lab_phy_addr;        /**< 读写; 辅助内存的labal物理地址, 临时缓存用于存储小连通域stage 1产生的label和disp的起始地址, 256B align, 动态参数*/
    unsigned long  cache_phy_addr;      /**< 读写; 辅助内存的cache物理地址, 临时缓存用于存储小连通域label的起始地址, 32B align, 动态参数*/
    struct ar_dpu_frame frame;
    struct ar_dpu_match_grp_drv_attr grp_attr;
} ar_dpu_match_drv_frame_t;

typedef struct ar_dpu_match_grp {
    int start;
    int enable;
    int luma_len;
    int frame_index;
    AR_VB_POOL dst_pool;
    int pic_line_stride;
    int post_wb_line_stride;
    ar_os_msg_queue_id_t out_queue;
    struct ar_dpu_match_grp_status status;
    struct ar_dpu_match_grp_drv_attr grp_attr;
    struct ar_dpu_match_chn_drv_attr chn_attr[AR_DPU_MAX_CHN_NUM];
} ar_dpu_match_grp_t;

typedef struct {
    AR_U32 u32_width;
    AR_U32 u32_height;
} STRU_SIZE;

typedef struct {
    AR_S32  s32_src_frame_rate;        /* RW; source frame rate */
    AR_S32  s32_dst_frame_rate;        /* RW; dest frame rate */
} STRU_FRAME_RATE_CTRL;

typedef struct {
    AR_U32  u32_pool_id;
    AR_U32  u32_frame_id;
    AR_U32  u32_width;
    AR_U32  u32_height;
    AR_U32  u32_stride;

    AR_U64  u64_phy_addr;
    AR_U64  u64_vir_addr;

    AR_U32  u32_time_ref;
    AR_U64  u64_pts;
    ENUM_PIXEL_FORMAT pixel_format;
} STRU_DPU_FRAME_INFO;

typedef struct {
    AR_BOOL b_sgm_720p_planb;        /* SGM pyramid scheme, using three-tiered pyramid,
                                        if there is too many cache miss and rm speckle too long, using plan B; default plan A */
    AR_BOOL b_sgm_lrc_bypass;        /* SGM lrc check bypass, default 0, not bypass */
    AR_BOOL b_sgm_lrc_unique_check;  /* SGM lrc unique check, 0: not check, 1: check */
    AR_U16  u16_speckle_area;        /* Window size required to eliminate speckle noise, Range:[0, 4095], default 16 */
    AR_U16  u16_sgm_para_p1;         /* Small penalty item of Cost Compute, Range:[0, 255], default 10 */
    AR_U16  u16_sgm_para_p2;         /* Big penalty item of Cost Compute, Range:[0, 255], default 150 */
    AR_U16  u16_wta_unique_ratio;    /* Wta unique ratio of LR check, Range:[0, 127], default 26 */
    AR_U16  u16_lrc_base_threshold;  /* Base judgement threshold of LR check, skip LRC check of current pixel
                                        when base disparity great than this param, Range:[0, 15], default 1 */
    AR_U16  u16_lrc_disp_threshold;  /* Disparity check threshold of LR check, invalid when diff value between
                                        left disp and right disp great than this param, Range:[0, 255], default 16 */
} STRU_DPU_MATCH_SGM_PARAM;

typedef struct {
    STRU_SIZE st_left_image_size;                      /* Left image size. */
    STRU_SIZE st_right_image_size;                     /* Right image size. */
    STRU_DPU_MATCH_SGM_PARAM st_sgm_param;             /* SGM parameter. */
    ENUM_DPU_MATCH_MASK_MODE en_match_mask_mode;       /* Aggregation mask mode. */
    ENUM_DPU_MATCH_DENS_ACCU_MODE en_dens_accu_mode;   /* Adjust density-accuracy trade-off. */
    ENUM_DPU_MATCH_SPEED_ACCU_MODE en_speed_accu_mode; /* Adjust speed-accuracy trade-off. */
    ENUM_DPU_MATCH_DISP_SUBPIXEL en_disp_subpixel_en;  /* Calculate subpixel disparity or not. */
    AR_U16 u16_disp_num;                               /* The number of disparity, it must be the multiple of 64,
                                                          Range:[64, 256], default 64 */
    AR_U16 u16_disp_start_pos;                         /* Minimum start disparity, Range:[0, 49] */
    AR_U32 u32_depth;                                  /* The depth of user image queue for getting Match output image,
                                                          it can not be changed dynamic. Range:[0, 8] */
    AR_BOOL b_need_src_frame;                          /* The flag of getting source videoframe.It will effect when bind dpu rect. */
    STRU_DPU_MATCH_MEM_INFO st_assist_buf;             /* Assistance buffer. */
    STRU_FRAME_RATE_CTRL st_frame_rate;                /* Group frame rate contrl. */
} STRU_DPU_MATCH_GRP_ATTR;

typedef struct {
    // NOTICE: Currently DPU disparity size only support size equal to src frame.
    // For 1080P, its' width can be halved.
    STRU_SIZE st_image_size;  /* Output image size. */
} STRU_DPU_MATCH_CHN_ATTR;

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HAL_COMM_DPU_MATCH_H__ */
