#ifndef __HAL_IVE_API_H__
#define __HAL_IVE_API_H__
#include "hal_type.h"
#include "osal.h"

#define AR_IVE_DEV_NAME                  "arive"
#define AR_IVE_HANDLE_MAX_NUM    1024
#define IVE_ROUTE_OPER_MAX          3

#define AR_IVE_NO_ERR             0
#define AR_IVE_ERR_INTERNAL_ERR  -1
#define AR_IVE_ERR_INVALID_PARAM -2
#define AR_IVE_ERR_NO_RESOURCE   -3
#define AR_IVE_ERR_NONE_EXIST    -4
#define AR_IVE_ERR_UNSUPPORTED   -10	 //  TBC???

#define AR_IVE_RPC_ID_SET_OPER   1
#define AR_IVE_RPC_ID_OPER_QUERY 2
#define AR_IVE_RPC_ID_POWER_ON   3
#define AR_IVE_RPC_ID_ATTR_SET   4

typedef unsigned short     AR_U0Q16;

typedef union
{
    AR_S8 s8Val;
    AR_U8 u8Val;
}IVE_8BIT_U;

typedef enum
{
    IVE_IMAGE_TYPE_U8C1 = 0x0,
    IVE_IMAGE_TYPE_S8C1 = 0x1,

    IVE_IMAGE_TYPE_YUV420SP = 0x2, /*YUV420 SemiPlanar*/
    IVE_IMAGE_TYPE_YUV422SP = 0x3, /*YUV422 SemiPlanar*/
    IVE_IMAGE_TYPE_YUV420P = 0x4, /*YUV420 Planar */
    IVE_IMAGE_TYPE_YUV422P = 0x5, /*YUV422 planar */

    IVE_IMAGE_TYPE_S8C2_PACKAGE = 0x6,
    IVE_IMAGE_TYPE_S8C2_PLANAR = 0x7,

    IVE_IMAGE_TYPE_S16C1 = 0x8,
    IVE_IMAGE_TYPE_U16C1 = 0x9,

    IVE_IMAGE_TYPE_U8C3_PACKAGE = 0xa,
    IVE_IMAGE_TYPE_U8C3_PLANAR = 0xb,

    IVE_IMAGE_TYPE_S32C1 = 0xc,
    IVE_IMAGE_TYPE_U32C1 = 0xd,

    IVE_IMAGE_TYPE_S64C1 = 0xe,
    IVE_IMAGE_TYPE_U64C1 = 0xf,

    IVE_IMAGE_TYPE_BUTT

}IVE_IMAGE_TYPE_E;

typedef enum
{
    IVE_SUB_MODE_ABS   = 0x0, /*Absolute value of the difference*/
    IVE_SUB_MODE_SHIFT = 0x1, /*The output result is obtained by shifting the result one digit right to reserve the signed bit.*/
    IVE_SUB_MODE_BUTT
}IVE_SUB_MODE_E;

typedef enum
{
    IVE_MULTI_MODE_S8   = 0,
    IVE_MULTI_MODE_S16  = 1,
    IVE_MULTI_MODE_BUTT
}IVE_MULTI_MODE_E;

typedef enum
{
    IVE_THRESH_MODE_BINARY      = 0x0, /*srcVal <= lowThr, dstVal = minVal; srcVal > lowThr, dstVal = maxVal.*/
    IVE_THRESH_MODE_TRUNC       = 0x1, /*srcVal <= lowThr, dstVal = srcVal; srcVal > lowThr, dstVal = maxVal.*/
    IVE_THRESH_MODE_TO_MINVAL   = 0x2, /*srcVal <= lowThr, dstVal = minVal; srcVal > lowThr, dstVal = srcVal.*/
    IVE_THRESH_MODE_MIN_MID_MAX = 0x3, /*srcVal <= lowThr, dstVal = minVal; lowThr < srcVal <= highThr, dstVal = midVal; srcVal > highThr,
                                       dstVal = maxVal.*/
    IVE_THRESH_MODE_ORI_MID_MAX = 0x4, /*srcVal <= lowThr, dstVal = srcVal; lowThr < srcVal <= highThr, dstVal = midVal; srcVal > highThr,
                                       dstVal = maxVal.*/
    IVE_THRESH_MODE_MIN_MID_ORI = 0x5, /*srcVal <= lowThr, dstVal = minVal; lowThr < srcVal <= highThr, dstVal = midVal; srcVal > highThr,
                                       dstVal = srcVal.*/
    IVE_THRESH_MODE_MIN_ORI_MAX = 0x6, /*srcVal <= lowThr, dstVal = minVal; lowThr < srcVal <= highThr, dstVal = srcVal; srcVal > highThr,
                                       dstVal = maxVal.*/
    IVE_THRESH_MODE_ORI_MID_ORI = 0x7, /*srcVal <= lowThr, dstVal = srcVal; lowThr < srcVal <= highThr, dstVal = midVal; srcVal > highThr,
                                       dstVal = srcVal.*/
    IVE_THRESH_MODE_BUTT
}IVE_THRESH_MODE_E;

typedef enum
{
    IVE_THRESH_U16_MODE_U16_TO_U8_MIN_MID_MAX = 0x0,
    IVE_THRESH_U16_MODE_U16_TO_U8_MIN_ORI_MAX = 0x1,
    IVE_THRESH_U16_MODE_BUTT
}IVE_THRESH_U16_MODE_E;

typedef enum
{
    IVE_THRESH_S16_MODE_S16_TO_S8_MIN_MID_MAX = 0x0,
    IVE_THRESH_S16_MODE_S16_TO_S8_MIN_ORI_MAX = 0x1,
    IVE_THRESH_S16_MODE_S16_TO_U8_MIN_MID_MAX = 0x2,
    IVE_THRESH_S16_MODE_S16_TO_U8_MIN_ORI_MAX = 0x3,
    IVE_THRESH_S16_MODE_BUTT
}IVE_THRESH_S16_MODE_E;

typedef enum
{
    IVE_INTEG_OUT_CTRL_COMBINE = 0x0,
    IVE_INTEG_OUT_CTRL_SUM     = 0x1,
    IVE_INTEG_OUT_CTRL_SQSUM   = 0x2,
    IVE_INTEG_OUT_CTRL_BUTT
}IVE_INTEG_OUT_CTRL_E;

typedef enum
{
    IVE_16BIT_TO_8BIT_MODE_S16_TO_S8      = 0x0,
    IVE_16BIT_TO_8BIT_MODE_S16_TO_U8_ABS  = 0x1,
    IVE_16BIT_TO_8BIT_MODE_S16_TO_U8_BIAS = 0x2,
    IVE_16BIT_TO_8BIT_MODE_U16_TO_U8      = 0x3,
    IVE_16BIT_TO_8BIT_MODE_TEMP           = 0x64, // 100-mode for temperature.
    IVE_16BIT_TO_8BIT_MODE_BUTT
}AR_IVE_16BIT_TO_8BIT_MODE_E;

typedef enum
{
    IVE_MAP_MODE_U8_TO_U8    = 0x0,
    IVE_MAP_MODE_U8_TO_U16   = 0x1,
    IVE_MAP_MODE_U10_TO_U10  = 0x2,
    IVE_MAP_MODE_U10_TO_U16  = 0x3,
    IVE_MAP_MODE_U12_TO_U12  = 0x4,
    IVE_MAP_MODE_U12_TO_U16  = 0x5,
    IVE_MAP_MODE_U16_TO_U16  = 0x6,
    IVE_MAP_MODE_BUTT
}IVE_MAP_MODE_E;

typedef enum
{
    IVE_DMA_MODE_DIRECT_COPY   = 0x0,
    IVE_DMA_MODE_INTERVAL_COPY = 0x1,
    IVE_DMA_MODE_SET_3BYTE     = 0x2,
    IVE_DMA_MODE_SET_8BYTE     = 0x3,
    IVE_DMA_MODE_BUTT
}AR_IVE_DMA_MODE_E;

typedef enum
{
    /*CSC: YUV2RGB, video transfer mode, RGB value range [16, 235]*/
    IVE_CSC_MODE_VIDEO_BT601_YUV2RGB = 0x0,
    /*CSC: YUV2RGB, video transfer mode, RGB value range [16, 235]*/
    IVE_CSC_MODE_VIDEO_BT709_YUV2RGB = 0x1,
    /*CSC: YUV2RGB, picture transfer mode, RGB value range [0, 255]*/
    IVE_CSC_MODE_PIC_BT601_YUV2RGB = 0x2,
    /*CSC: YUV2RGB, picture transfer mode, RGB value range [0, 255]*/
    IVE_CSC_MODE_PIC_BT709_YUV2RGB = 0x3,
    /*CSC: YUV2HSV, picture transfer mode, HSV value range [0, 255]*/
    //IVE_CSC_MODE_PIC_BT601_YUV2HSV = 0x4,  // Not support HSV
    /*CSC: YUV2HSV, picture transfer mode, HSV value range [0, 255]*/
    //IVE_CSC_MODE_PIC_BT709_YUV2HSV = 0x5,  // Not support HSV
    /*CSC: YUV2LAB, picture transfer mode, Lab value range [0, 255]*/
    // IVE_CSC_MODE_PIC_BT601_YUV2LAB = 0x6, // Not support LAB
    /*CSC: YUV2LAB, picture transfer mode, Lab value range [0, 255]*/
    //IVE_CSC_MODE_PIC_BT709_YUV2LAB = 0x7,  // Not support LAB
    /*CSC: RGB2YUV, video transfer mode, YUV value range [0, 255]*/
    IVE_CSC_MODE_VIDEO_BT601_RGB2YUV = 0x8,
    /*CSC: RGB2YUV, video transfer mode, YUV value range [0, 255]*/
    IVE_CSC_MODE_VIDEO_BT709_RGB2YUV = 0x9,
    /*CSC: RGB2YUV, picture transfer mode, Y:[16, 235],U\V:[16, 240]*/
    IVE_CSC_MODE_PIC_BT601_RGB2YUV = 0xa,
    /*CSC: RGB2YUV, picture transfer mode, Y:[16, 235],U\V:[16, 240]*/
    IVE_CSC_MODE_PIC_BT709_RGB2YUV = 0xb,
    IVE_CSC_MODE_BUTT
}IVE_CSC_MODE_E;

typedef enum
{
    IVE_ORD_STAT_FILTER_MODE_MEDIAN = 0x0,
    IVE_ORD_STAT_FILTER_MODE_MAX    = 0x1,
    IVE_ORD_STAT_FILTER_MODE_MIN    = 0x2,
    IVE_ORD_STAT_FILTER_MODE_BUTT
}IVE_ORD_STAT_FILTER_MODE_E;

typedef enum
{
    IVE_FILTER_MODE_16    = 0,
    IVE_FILTER_MODE_8     = 1, //seperated
    IVE_FILTER_MODE_YUV   = 2, // 8bit
    IVE_FILTER_MODE_SOBEL = 3, // 8bit
}IVE_FILTER_MODE_E;

typedef enum
{
    IVE_SOBEL_OUT_CTRL_BOTH = 0x0, /*Output horizontal and vertical*/
    IVE_SOBEL_OUT_CTRL_HOR  = 0x1, /*Output horizontal*/
    IVE_SOBEL_OUT_CTRL_VER  = 0x2, /*Output vertical*/
    IVE_SOBEL_OUT_CTRL_BUTT
}IVE_SOBEL_OUT_CTRL_E;

typedef enum
{
    IVE_MAG_AND_ANG_OUT_CTRL_MAG = 0x0,
    IVE_MAG_AND_ANG_OUT_CTRL_MAG_AND_ANG = 0x1,
    IVE_MAG_AND_ANG_OUT_CTRL_BUTT
}IVE_MAG_AND_ANG_OUT_CTRL_E;

typedef enum
{
    IVE_MAU_MUL_DATA_TYPE_INT16 = 0,
    IVE_MAU_MUL_DATA_TYPE_FP16  = 1,
    IVE_MAU_MUL_DATA_TYPE_FP32  = 2,
}IVE_MAU_MUL_DATA_TYPE_E;

typedef enum {
    IVE_MAU_VECTOR_OP_MODE_SUB     = 0x0,
    IVE_MAU_VECTOR_OP_MODE_SUB_ABS = 0x1,
    IVE_MAU_VECTOR_OP_MODE_ADD     = 0x2,
    IVE_MAU_VECTOR_OP_MODE_ADD_ABS = 0x3,
    IVE_MAU_VECTOR_OP_MODE_BUTT
}IVE_MAU_VECTOR_OP_MODE_E;

typedef enum {
    IVE_MAU_TYPE_CONVERT_MODE_FP32_TO_FP16 = 0x0,
    IVE_MAU_TYPE_CONVERT_MODE_FP16_TO_FP32 = 0x1,
    IVE_MAU_TYPE_CONVERT_MODE_BUTT
}IVE_MAU_TYPE_CONVERT_MODE_E;

typedef enum
{
    AR_IVE_HW_TYPE_GEN = 0,
    AR_IVE_HW_TYPE_RAM,
    AR_IVE_HW_TYPE_PACK,
    AR_IVE_HW_TYPE_MAX,
}AR_IVE_HW_TYPE_E;

typedef enum
{
    AR_IVE_ATTR_SUB_SAMPLE,
    AR_IVE_ATTR_MAX
}AR_IVE_ATTR_TYPE_E;

typedef struct
{
    IVE_IMAGE_TYPE_E enType;
    AR_U32 u32PhyAddr[3];
    AR_U8 *pu8VirAddr[3];
    AR_U16 u16Stride[3];
    AR_U16 u16Width;
    AR_U16 u16Height;
    AR_U16 u16Reserved;
    //AR_U32 u32DebugReg[256];
}IVE_IMAGE_S;

typedef struct
{
    AR_U0Q16 u0q16X;    /*x of "xA+yB" */
    AR_U0Q16 u0q16Y;    /*y of "xA+yB" */
}IVE_ADD_CTRL_S;

typedef struct
{
    IVE_SUB_MODE_E enMode;
}IVE_SUB_CTRL_S;

typedef struct
{
    IVE_MULTI_MODE_E enMode;
}IVE_MULTI_CTRL_S;

typedef struct
{
    IVE_THRESH_MODE_E enMode;
    AR_U8 u8LowThr; /*user-defined threshold, 0<=u8LowThr<=255 */
    AR_U8 u8HighThr; /*user-defined threshold, if enMode<IVE_THRESH_MODE_MIN_MID_MAX, u8HighThr is not used, else
    0<=u8LowThr<=u8HighThr<=255;*/
    AR_U8 u8MinVal; /*Minimum value when tri-level thresholding*/
    AR_U8 u8MidVal; /*Middle value when tri-level thresholding, ifenMode<2, u32MidVal is not used; */
    AR_U8 u8MaxVal; /*Maxmum value when tri-level thresholding*/
}IVE_THRESH_CTRL_S;

typedef struct
{
    IVE_THRESH_U16_MODE_E enMode;
    AR_U16 u16LowThr;
    AR_U16 u16HighThr;
    AR_U8 u8MinVal;
    AR_U8 u8MidVal;
    AR_U8 u8MaxVal;
}IVE_THRESH_U16_CTRL_S;

typedef struct
{
    IVE_THRESH_S16_MODE_E enMode;
    AR_S16 s16LowThr; /*user-defined threshold*/
    AR_S16 s16HighThr; /*user-defined threshold*/
    IVE_8BIT_U un8MinVal; /*Minimum value when tri-level thresholding*/
    IVE_8BIT_U un8MidVal; /*Middle value when tri-level thresholding*/
    IVE_8BIT_U un8MaxVal; /*Maxmum value when tri-level thresholding*/
}IVE_THRESH_S16_CTRL_S;

typedef struct
{
    IVE_INTEG_OUT_CTRL_E enOutCtrl;
}IVE_INTEG_CTRL_S;

typedef struct
{
    AR_IVE_16BIT_TO_8BIT_MODE_E enMode;
    AR_U16 u16Denominator;
    AR_U8 u8Numerator;
    AR_S8 s8Bias;
}IVE_16BIT_TO_8BIT_CTRL_S;

typedef struct
{
    IVE_MAP_MODE_E enMode;
}IVE_MAP_CTRL_S;

typedef struct
{
    AR_U32 u32PhyAddr;
    AR_U8 *pu8VirAddr;
    AR_U32 u32Size;
    AR_U32 u16Reserved;
    //AR_U32 u32DebugReg[256];    /// TBC: For test only
}IVE_MEM_INFO_S;

typedef struct
{
    AR_IVE_DMA_MODE_E enMode;
    AR_U64 u64Val;
    AR_U16 u16LeftAddr; // X-coordinate or the pixel when direct copy mode
    AR_U16 u16UpAddr;   // Y-coordinate or the pixel when direct copy mode
    AR_U8 u8HorSegSize;
    AR_U8 u8VerSegRows;
    AR_U8 u8ElemSize;
    AR_BOOL bNoStrideMode;
}IVE_DMA_CTRL_S;

typedef struct
{
    AR_U8 au8Mask[25]; /*The template parameter value must be 0 or 255.*/
}IVE_DILATE_CTRL_S;

typedef struct
{
    AR_U8 au8Mask[25];
}IVE_ERODE_CTRL_S;

typedef struct
{
    IVE_CSC_MODE_E enMode; /*Working mode*/
    AR_FLOAT f16CoeffW0[3];
    AR_FLOAT f16CoeffW1[3];
    AR_FLOAT f16CoeffW2[3];
    AR_FLOAT f16CoeffBias[3];

    AR_U8 u8MaxThr[3];
    AR_U8 u8MinThr[3];
    AR_U8 u8MaxVal[3];
    AR_U8 u8MinVal[3];
}IVE_CSC_CTRL_S;

typedef struct
{
    IVE_ORD_STAT_FILTER_MODE_E enMode;
}IVE_ORD_STAT_FILTER_CTRL_S;

typedef struct
{
    IVE_FILTER_MODE_E enMode;
    AR_BOOL bChFltSigned[3];         /*Filter signed or not, per channel when filter mode is non-16bit*/
    AR_U8 u8Norm;                    /*Normalization parameter, by right shift*/
    AR_S32 as32Mask[25];             /*Template parameter filter coefficient*/
}IVE_FILTER_CTRL_S;

typedef struct
{
    IVE_SOBEL_OUT_CTRL_E enOutCtrl;    /*Output format*/
    AR_BOOL bChFltSigned[3];           /*Filter signed or not, per channel*/
    AR_S32 as32Mask[25];               /*Template parameter*/
}IVE_SOBEL_CTRL_S;

typedef struct
{
    IVE_MAG_AND_ANG_OUT_CTRL_E enOutCtrl;
    AR_BOOL bChFltSigned[3];           /*Filter signed or not, per channel*/
    AR_U16 u16Thr;
    AR_S32 as32Mask[25]; /*Template parameter.*/
}IVE_MAG_AND_ANG_CTRL_S;

typedef struct
{
    IVE_MEM_INFO_S stMem;
    AR_DOUBLE dTheta;
    AR_U16 u16LowThr;
    AR_U16 u16HighThr;
}IVE_CANNY_HYS_EDGE_CTRL_S;

typedef struct
{
    IVE_MAU_MUL_DATA_TYPE_E enDataType;
    AR_U8 u8RightShift;
    AR_BOOL bIntOutputMode; /*0-int32, 1-int16*/
}IVE_MAU_CTRL_S;

typedef struct
{
    IVE_MAU_VECTOR_OP_MODE_E enMode;
    IVE_MAU_MUL_DATA_TYPE_E enDataType;
}IVE_MAU_VECTOR_OP_CTRL_S;

typedef struct {
    IVE_MAU_TYPE_CONVERT_MODE_E enMode;
}IVE_MAU_TYPE_CONVERT_CTRL_S;

typedef struct
{
    IVE_CSC_MODE_E enMode; /*CSC working mode*/
    AR_S8 as8Mask[25]; /*Template parameter filter coefficient*/
    AR_U8 u8Norm; /*Normalization parameter, by right shift*/
}IVE_FILTER_AND_CSC_CTRL_S;

typedef struct
{
    AR_BOOL bSubSampleW; /* Sub-sample for width */
    AR_BOOL bSubSampleH; /* Sub-sample for height */
}AR_IVE_ATTR_PARAM_S;

typedef struct
{
    AR_U8 u8Dummy;
}IVE_DUMMY_OPER_CTRL_S;

typedef struct
{
    AR_U32 u32IveHandle;
    AR_BOOL bFinish;
    AR_BOOL bBlock;
}AR_IVE_QUERY_S;

typedef struct
{
    AR_BOOL bPowerOn;
}AR_IVE_POWERON_S;

typedef struct
{
    AR_IVE_ATTR_TYPE_E enType;
    AR_IVE_ATTR_PARAM_S stParam;
}AR_IVE_ATTR_S;

typedef IVE_IMAGE_S                    IVE_SRC_IMAGE_S;
typedef IVE_IMAGE_S                    IVE_DST_IMAGE_S;
typedef IVE_MEM_INFO_S                 IVE_DST_MEM_INFO_S;
typedef IVE_MEM_INFO_S                 IVE_SRC_MEM_INFO_S;

typedef union
{
    IVE_ADD_CTRL_S             stAddCtrl;
    IVE_SUB_CTRL_S             stSubCtrl;
    IVE_MULTI_CTRL_S           stMultiCtrl;
    IVE_THRESH_CTRL_S          stThrCtrl;
    IVE_THRESH_U16_CTRL_S      stThrU16Ctrl;
    IVE_THRESH_S16_CTRL_S      stThrS16Ctrl;
    IVE_INTEG_CTRL_S           stIntegCtrl;
    IVE_16BIT_TO_8BIT_CTRL_S   st16bitTo8bitCtrl;
    IVE_MAP_CTRL_S             stMapCtrl;
    IVE_CSC_CTRL_S             stCscCtrl;
    IVE_DILATE_CTRL_S          stDilateCtrl;
    IVE_ERODE_CTRL_S           stErodeCtrl;
    IVE_DMA_CTRL_S             stDmaCtrl;
    IVE_ORD_STAT_FILTER_CTRL_S stOrdStatFltCtrl;
    IVE_FILTER_CTRL_S          stFltCtrl;
    IVE_SOBEL_CTRL_S           stSobelCtrl;
    IVE_MAG_AND_ANG_CTRL_S     stMagAndAngCtrl;
    IVE_CANNY_HYS_EDGE_CTRL_S  stCannyHysEdgeCtrl;
    IVE_MAU_CTRL_S             stMauCtrl;
    IVE_MAU_VECTOR_OP_CTRL_S   stMauVectorOpCtrl;
    IVE_MAU_TYPE_CONVERT_CTRL_S stMauTypeConvCtrl;
    IVE_FILTER_AND_CSC_CTRL_S  stFltCscCtrl;
    IVE_DUMMY_OPER_CTRL_S      stDummyOperCtrl;
}AR_IVE_CTRL_U;

typedef enum
{
    /*Note: Please do NOT change the sequence in the enum.*/
    AR_IVE_OPER_AND = 0,
    AR_IVE_OPER_OR  = 1,
    AR_IVE_OPER_XOR = 2,
    AR_IVE_OPER_ADD = 3,
    AR_IVE_OPER_SUB = 4,
    AR_IVE_OPER_MULTI = 5,
    AR_IVE_OPER_THRESH = 6,
    AR_IVE_OPER_THRESH_U16 = 7,
    AR_IVE_OPER_THRESH_S16 = 8,
    AR_IVE_OPER_INTEG = 9,
    AR_IVE_OPER_16TO8 = 10,
    AR_IVE_OPER_HIST = 11,
    AR_IVE_OPER_MAP = 12,
    AR_IVE_OPER_DILATE = 13,
    AR_IVE_OPER_ERODE = 14,
    AR_IVE_OPER_CSC0 = 15,
    AR_IVE_OPER_DMA = 16,
    AR_IVE_OPER_ORD_FILTER = 17,
    AR_IVE_OPER_FILTER0 = 18,
    AR_IVE_OPER_FILTER1 = 19,
    AR_IVE_OPER_FILTER2 = 20,
    AR_IVE_OPER_SOBEL = 21,
    AR_IVE_OPER_SOBEL_MAG_ANG = 22,
    AR_IVE_OPER_CSC1 = 23,
    AR_IVE_OPER_CSC2 = 24,
    AR_IVE_OPER_MAU_MATRIX_MUL = 25,
    AR_IVE_OPER_MAU_COS_DIST = 26,
    AR_IVE_OPER_MAU_EUCLID_DIST = 27,
    AR_IVE_OPER_MAU_MANHATTAN_DIST = 28,
    AR_IVE_OPER_MAU_VECTOR_OP = 29,
    AR_IVE_OPER_MAU_TYPE_CONVERT = 30,
    AR_IVE_OPER_CANNY_HYS = 31,
    AR_IVE_OPER_CANNY_EDGE = 32,
    AR_IVE_OPER_ROUTE_MUX = 33,
    AR_IVE_OPER_ROUTE_BUFFER = 34,
    AR_IVE_OPER_TYPE_MAX,
}AR_IVE_OPER_TYPE_E;

typedef struct
{
    AR_BOOL bCrop;
    AR_U16 u16CropXStart;
    AR_U16 u16CropXEnd;
    AR_U16 u16CropYStart;
    AR_U16 u16CropYEnd;
}AR_IVE_CROP_S;

typedef struct
{
    IVE_IMAGE_TYPE_E enType;
    AR_U32 u32PhyAddr[3];
    AR_U8  u8VirAddr[3];
    AR_U16 u16Stride[3];
    AR_U16 u16Width;
    AR_U16 u16Height;
    AR_U16 u16ValidPixelStart;    /*Valid pixel start in each line*/
    AR_U16 u16Reserved;
    //AR_U32 u32DebugReg[256];    /// TBC: For test only
}AR_IVE_IMAGE_S;

typedef struct
{
    AR_U32 u32IveHandle;
    //AR_IVE_IMAGE_S src1;
    //AR_IVE_IMAGE_S src2;
    //AR_IVE_IMAGE_S dst;
    //AR_IVE_IMAGE_S dst2;
    AR_IVE_IMAGE_S astImgSrc[2];
    AR_IVE_IMAGE_S astImgDst[2];
    AR_IVE_CTRL_U aunCtrl[3];
    AR_IVE_CROP_S astCrop;
    //AR_IVE_CTRL_U ctrl;
    AR_BOOL bInstant;
    AR_BOOL bRoute;
    AR_IVE_OPER_TYPE_E aenOper[3];
    //AR_IVE_OPER_TYPE_E oper;
}AR_IVE_PARAMS_S;

typedef struct AR_IVE_OPER_NODE
{
    AR_IVE_PARAMS_S stIveParams;
    struct AR_IVE_OPER_NODE *pstNext;
}AR_IVE_OPER_NODE_S;

typedef struct AR_IVE_CLIENT
{
    AR_IVE_OPER_NODE_S *pstOperNode;   /*Second level list: Operators for each client*/
    struct AR_IVE_CLIENT *pstNext;     /*First level list: Clients*/
}AR_IVE_CLIENT_S;

AR_S32 ar_hal_ive_and(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2,
                        IVE_DST_IMAGE_S *pstDst, AR_BOOL bInstant);
AR_S32 ar_hal_ive_or(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2,
                        IVE_DST_IMAGE_S *pstDst, AR_BOOL bInstant);
AR_S32 ar_hal_ive_xor(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2,
                        IVE_DST_IMAGE_S *pstDst, AR_BOOL bInstant);
AR_S32 ar_hal_ive_add(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_ADD_CTRL_S *pstAddCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_sub(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_SUB_CTRL_S *pstSubCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_multi(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MULTI_CTRL_S *pstMultiCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_thresh(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_THRESH_CTRL_S *pstThreshCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_thresh_u16(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_THRESH_U16_CTRL_S *pstThreshCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_thresh_s16(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_THRESH_S16_CTRL_S *pstThreshCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_integ(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_INTEG_CTRL_S *pstIntegCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_16bit_to_8bit(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_16BIT_TO_8BIT_CTRL_S *pst16BitTo8BitCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_hist(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst, AR_BOOL bInstant);
AR_S32 ar_hal_ive_map(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_MEM_INFO_S *pstMap, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAP_CTRL_S *pstMapCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_csc(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_CSC_CTRL_S *pstCscCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_dilate(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_DILATE_CTRL_S *pstDilateCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_erode(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_ERODE_CTRL_S *pstDilateCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_dma(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_DMA_CTRL_S *pstDmaCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_ord_stat_filter(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_ORD_STAT_FILTER_CTRL_S *pstOrdStatFltCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_filter(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDst,
                        IVE_FILTER_CTRL_S *pstFltCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_sobel(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDstH, IVE_DST_IMAGE_S *pstDstV,
                        IVE_SOBEL_CTRL_S *pstSobelCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_mag_and_ang(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc, IVE_DST_IMAGE_S *pstDstMag, IVE_DST_IMAGE_S *pstDstAng,
                        IVE_MAG_AND_ANG_CTRL_S *pstMagAndAngCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_mau_matrix_mul(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_CTRL_S *pstMauCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_mau_cos_dist(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_CTRL_S *pstMauCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_mau_euclid_dist(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_CTRL_S *pstMauCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_mau_manhattan_dist(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_CTRL_S *pstMauCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_mau_vector_op(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_VECTOR_OP_CTRL_S *pstMauVectorOpCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_mau_type_convert(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_SRC_IMAGE_S *pstSrc2, IVE_DST_IMAGE_S *pstDst,
                        IVE_MAU_TYPE_CONVERT_CTRL_S *pstMauTypeConvCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_canny_hys(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstSrc1, IVE_DST_IMAGE_S *pstDst, IVE_DST_MEM_INFO_S *pstStack,
                        IVE_CANNY_HYS_EDGE_CTRL_S *pstCannyHysEdgeCtrl, AR_BOOL bInstant);
AR_S32 ar_hal_ive_canny_edge(AR_HANDLE *pIveHandle, IVE_SRC_IMAGE_S *pstEdge, IVE_MEM_INFO_S *pstStack, AR_BOOL bInstant);
AR_S32 ar_hal_ive_set_crop(const AR_IVE_CROP_S *pstCrop);
AR_S32 ar_hal_ive_route(AR_HANDLE *pIveHandle, AR_IVE_OPER_TYPE_E aenOper[], IVE_SRC_IMAGE_S astImgSrc[],
                        IVE_DST_IMAGE_S astImgDst[], AR_IVE_CTRL_U aunCtrl[], AR_BOOL bInstant);
AR_S32 ar_hal_ive_query_oper(AR_HANDLE IveHandle, AR_BOOL *pbFinish, AR_BOOL bBlock);
AR_S32 ar_hal_ive_attr_set(AR_IVE_ATTR_S *pstAttr);

AR_S32 ar_hal_ive_init(AR_VOID);
AR_VOID ar_hal_ive_deinit(AR_VOID);

#endif
