#ifndef __HAL_OPTFLOW_H__
#define __HAL_OPTFLOW_H__
#include "hal_type.h"
#include "ar_optflow.h"

#ifdef __cplusplus
#if __cplusplus
	extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#define AR_OPTFLOW_FEATURE_POINT_MAX 0x10000
#define AR_OPTFLOW_PYRAMID_MAX       4

typedef struct 
{
    AR_U32 u32X;         /* corner output x coor */
    AR_U32 u32Y;         /* corner output y coor */
    AR_U32 u32Response;  /* if output_mode set to 1, corner output response */
} AR_HAL_OPTFLOW_IMG_OUT_S;

typedef struct 
{
    AR_U32        u32Width;       /* picture width (64 - 4096) */
    AR_U32        u32Height;      /* picture height (64 - 2160) */
    AR_U32        u32Stride;      /* picture stride */
    AR_U32        u32ImgAddrPhy;  /* input picture phy address */
    AR_UINTPTR    u32ImgAddrVirt; /* input picture phy address */
} AR_HAL_OPTFLOW_IMG_S;

typedef struct
{
    AR_U32    u32Mode;                 /* feature point type, 0: harris; 1: shi-tomasi */
    AR_U32    u32OutputMode;           /* feature point output type, 0: output position without response; 1: output position with response */
    AR_BOOL   bHarrisUsrConfigKEnable; /* 1: enable user defined harris_k_value */
    AR_FLOAT  f32HarrisUsrConfigKValue;/* user defined harris k value */ 
    /* if not enable user config K, can use default value(0: 0.0400390625; 1: 0.046875; 
    : 0.0546875; 3: 0.0625; 4: 0.0703125; 5: 0.078125; 6: 0.0859375; 7: 0.09375; 8: 0.1015625;
    : 0.109375; 10: 0.1171875; 11: 0.125; 12: 0.1328125; 13: 0.140625; 14: 0.1484275; 15: 0.15625 
    */
    AR_U32    u32HarrisKConfig;        
    AR_U32    u32ThreshMin;            /* feature point thresh */
    AR_U32    u32BlockSize;            /* feature point window size, 0: 3x3; 1:5x5 */
    AR_U32    u32TileNormalSizeX;      /* tile size x = [16, 32, 64] */
    AR_U32    u32TileNormalSizeY;      /* tile size y = [64, 32, 16], while tile_size_x = 16, tile_size_y = 64 */
} AR_HAL_OPTFLOW_CORNER_CTRL_S;

typedef struct
{
    AR_U32      u32PyramidLevel;                                /* pyramid level [0-3] */
    AR_U32      u32PyramidAddrPhy[AR_OPTFLOW_PYRAMID_MAX];      /* pyramid every level start phy address */
    AR_UINTPTR  u32PyramidAddrVirt[AR_OPTFLOW_PYRAMID_MAX];     /* pyramid every level start virt address */
} AR_HAL_OPTFLOW_PYRAMID_CTRL_S;

typedef struct
{
    AR_U32  u32PyramidLevel;                            /* pyramid level [0-3] */
    AR_U32 	u32KltUseInitialFlowFlags;                  /* klt flags for use initial flow, set 1 enable */
   /* klt output mode, 
      0: only output trace position; 
      1: output position+status
      2: output position+err; 
      3: output position+status+err 
   */
    AR_U32  u32KltOutputMode;
    AR_U32  u32KltDeterminantMin;                       /* klt min determinant thresh value, default set to 1 ?*/
    AR_U32  u32KltWinowSize;                            /* klt windows size (5-25) */
    AR_U32  u32KltTraceScope[AR_OPTFLOW_PYRAMID_MAX];   /* klt trace scope to pyramid, 0: 16x16; 1: 32x32; 2: 48x48 */
    AR_U32  u32KltCriteriaMode;                         /* klt criteria mode, 0: use iterative_times; 1: use iterative_times & epsilon_value*/
    AR_U32  u32KltCriteriaIterativeTimes;               /* klt iterative times */
    AR_U32  u32KltCriteriaEpsilonValue;                 /* klt epsilon value */
} AR_HAL_OPTFLOW_KLT_CTRL_S;

typedef struct
{
    AR_U32      u32FeaturePointNum;                     /* output feature point num */
    AR_U32      u32FeaturePointAddrPhy;                 /* output feature point phy address */
    AR_UINTPTR  u32FeaturePointAddrVirt;                /* output feature point virt address */
} AR_HAL_OPTFLOW_POINT_OUTPUT_S;

AR_S32 ar_hal_optflow_corner(AR_HAL_OPTFLOW_IMG_S * pstImgIn, AR_HAL_OPTFLOW_CORNER_CTRL_S * pstCornerCtrl, AR_HAL_OPTFLOW_POINT_OUTPUT_S * pstCornerOut);
AR_S32 ar_hal_optflow_pyramid(AR_HAL_OPTFLOW_IMG_S * pstImgIn, AR_HAL_OPTFLOW_PYRAMID_CTRL_S * pstPyramidCtrl);
AR_S32 ar_hal_optflow_klt(AR_HAL_OPTFLOW_IMG_S * pstImgPre, AR_HAL_OPTFLOW_IMG_S * pstImgCur, AR_HAL_OPTFLOW_POINT_OUTPUT_S * pstKltPointPre, AR_HAL_OPTFLOW_POINT_OUTPUT_S * pstKltPointCur, AR_HAL_OPTFLOW_KLT_CTRL_S * pstKltCtrl);

AR_S32 ar_hal_optical_flow_corner(ar_optflow_corner_ioctl_st * stCornerParam);
AR_S32 ar_hal_optical_flow_pyramid(ar_optflow_pyramid_ioctl_st * stPyramidParam);
AR_S32 ar_hal_optical_flow_klt(ar_optflow_klt_ioctl_st * stKltParam);


#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */


#endif //__HAL_OPTFLOW_H__

