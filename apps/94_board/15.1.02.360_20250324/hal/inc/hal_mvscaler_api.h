#ifndef __HAL_MVSCALER_API_H__
#define __HAL_MVSCALER_API_H__
#include "ar_comm_video.h"
#include "hal_sys.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#define AR_MVSCALER_SMOOTH_LEVEL_MAX     256
#define AR_MVSCALER_SHARP_LEVEL_MAX      256
#define AR_MVSCALER_MAX_PANNELS          4
#define AR_MVSCALER_MAX_OUTPUT_NUM       4

typedef struct
{
    AR_U32          u32X;
    AR_U32          u32Y;
    AR_U32          u32W;
    AR_U32          u32H;
} AR_HAL_MVSCALER_CROP_S;

typedef enum
{
    AR_IMAGE_YUV444P  = 0,
    AR_IMAGE_YUV444SP = 1,
    AR_IMAGE_YUV422P  = 2,
    AR_IMAGE_YUV422SP = 3,
    AR_IMAGE_YUV420P  = 4,
    AR_IMAGE_YUV420SP = 5,
    AR_IMAGE_YUVI420  = 6, //YU12
    AR_IMAGE_YV12     = 7,
    AR_IMAGE_NV12     = 8,
    AR_IMAGE_NV21     = 9,
    AR_IMAGE_RGB      = 10,
    AR_IMAGE_BGR      = 11,
    AR_IMAGE_RGBD     = 12,
    AR_IMAGE_GRAY     = 15,
    AR_IMAGE_MAX
} AR_HAL_MVSCALER_IMG_FORMAT_E;

typedef struct
{
    AR_UINTPTR                     uptrAddrVirt;
    AR_U32                         u32AddrPhy;
    AR_U32                         u32Stride;
} AR_HAL_MVSCALER_IMG_CHANNEL_S;

typedef struct
{
    AR_U32                         u32Width;
    AR_U32                         u32Height;
    AR_U32                         u32ChannelNum;
    AR_HAL_MVSCALER_IMG_CHANNEL_S  astChannels[AR_MVSCALER_MAX_PANNELS];
} AR_HAL_MVSCALER_IMG_S;

typedef struct 
{
    PIXEL_FORMAT_E                  enFormat;
    AR_U32                          u32PlanarNum;
    AR_U32                          u32Width[4];
    AR_U32                          u32Lines64Enable; //default lb 32 lines, 64 lines only use for 1080p
} AR_HAL_MVSCALER_LOWDELAY_INFO_S;

typedef enum
{
    AR_MVSCALER_CF50_MODE_NONE      = 0,
    AR_MVSCALER_CF50_MODE_DECODE    = 1,
    AR_MVSCALER_CF50_MODE_ENCODE    = 2,
    AR_MVSCALER_CF50_MODE_TRANSCODE = 3,
    AR_MVSCALER_CF50_MODE_BUTT
} AR_HAL_MVSCALER_CF50_MODE_E;

typedef struct 
{
    //ENMU_SYS_PIXEL_FORMAT       enFormat;
    AR_U32                      u32Align;
    AR_BOOL                     bLossy;
    CF50_COMPRESS_RATIO_ENUM    enRatioTo;
    AR_U32                      u32Cf50HeaderPhy[AR_MVSCALER_MAX_PANNELS];
    AR_UINTPTR                  uptrCf50HeaderVirt[AR_MVSCALER_MAX_PANNELS];
} AR_HAL_MVSCALER_CF50_INFO_S;

typedef struct
{
    AR_HAL_MVSCALER_CF50_MODE_E enCF50Mode;
    AR_HAL_MVSCALER_CF50_INFO_S stCF50Decoder;
    AR_HAL_MVSCALER_CF50_INFO_S stCF50Encoder[AR_MVSCALER_MAX_OUTPUT_NUM];
} AR_HAL_MVSCALER_CF50_PARAM_S;

typedef struct
{
    AR_U32                           u32CoreId;
    AR_U32                           u32OutNum;
    AR_HAL_MVSCALER_IMG_FORMAT_E     enFormat;
    AR_HAL_MVSCALER_IMG_S            stSrcImgs;
    AR_HAL_MVSCALER_CROP_S           stCrops;
    AR_HAL_MVSCALER_IMG_S            stDstImgs[AR_MVSCALER_MAX_OUTPUT_NUM];
    AR_U8                            u8LdEn;
    AR_U8                            u8Cf50En;
    AR_HAL_MVSCALER_LOWDELAY_INFO_S  stLdInfo;
    AR_HAL_MVSCALER_CF50_PARAM_S     stCF50;     //mvscaler cf50模式压缩配置参数，压缩模式mvscaler最大支持3输出
} AR_HAL_MVSCALER_PARAMS_S;

AR_S32 ar_hal_mvscaler_open_dev(void);
AR_S32 ar_hal_mvscaler_close_dev(AR_S32 fd);

/**
* @brief  1to4 图像缩放处理接口
* @param  pstSrcImgs 源图片，pstCrops 坐标参数，pstDstImgs 目的图片，u32OutputImgNum 目的图片数量
* @retval retval > 0 成功，其他加载失败
* @note   In order to make it simple, please process the same type of images each time.
* @note   the output img num should between [1 - 4].
*/
AR_S32 ar_hal_mvscaler_crop_resize(AR_S32 s32Fd,            AR_HAL_MVSCALER_PARAMS_S *pstMvParams);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */

#endif //__HAL_MVSCALER_H__
