#ifndef __HAL_SCALER_RPC_H__
#define __HAL_SCALER_RPC_H__
#include "hal_dbglog.h"
#include "hal_errno.h"

#ifdef __cplusplus
#if __cplusplus
	extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#define AR_SCALER_SMOOTH_LEVEL_MAX     256
#define AR_SCALER_SHARP_LEVEL_MAX      256
#define SCALER_MAX_PANNELS             4
#define SCALER_MAX_BATCH_SIZE          128
#define AR_HAL_SCALER_DEV_NAME         "scaler_server"

#define AR_HAL_MOD_SCALER              HAL_TAG_ID(AR_SYS_ID_SCALER)

typedef enum
{
    SCALER_RPC_ID_PROCESS_SET_PARAMS                       = 0,
    SCALER_RPC_ID_PROCESS_SINGEL_FRAME,
    SCALER_RPC_ID_PROCESS_BATCH_FRAME,
    SCALER_RPC_ID_PROCESS_SUSPEND,
    SCALER_RPC_ID_PROCESS_RESUME,
    SCALER_RPC_ID_PROCESS_FREQUENCY
}ENUM_SCALER_RPC_ID;

//a frame is consisted by several commands
typedef enum
{
    E_SCALER_BIT_DEPTH_8=0,
    E_SCALER_BIT_DEPTH_10=1,
} E_SCALER_BIT_DEPTH;

typedef enum{
    E_SCALER_1_BYTES_PER_PIX=0,
    E_SCALER_2_BYTES_PER_PIX=1,
} E_SCALER_PIXEL_BYTES;

typedef struct
{
    uint32_t src_phy_addr;
    uint32_t src_w;
    uint32_t src_h;
    uint32_t src_stride;
    uint32_t crop_x;
    uint32_t crop_y;
    uint32_t crop_w;
    uint32_t crop_h;
    uint32_t dst_phy_addr;
    uint32_t dst_w;
    uint32_t dst_h;
    uint32_t dst_stride;
    uint32_t channels;
    uint32_t mode;                  //bilinear, bicubic
    uint32_t byte_per_pixel;        //0: 1byte, 1: 2bytes
    uint32_t bit_depth;             //0: 8bits, 1: 10bits
} scaler_params_t;

typedef struct
{
    int32_t        smooth_factor;
    int32_t        sharp_factor;
    int32_t        reserved[4];
} scaler_rpc_factor_params;

typedef struct
{
    uint32_t        frame_id;
    uint32_t        pannels;
    uint32_t        usr_lut_enable;
    uint32_t        usr_def_lut_phy;
    scaler_params_t scaler_param[SCALER_MAX_PANNELS];
} scaler_rpc_params;

typedef struct
{
    uint32_t        frame_id;
    uint32_t        batch_num;
    uint32_t        usr_lut_enable;
    uint32_t        usr_def_lut_phy;
    scaler_params_t scaler_param[SCALER_MAX_BATCH_SIZE];
} scaler_rpc_batch_params;

typedef enum
{
    SCALER_SUCCUESS                   =   0,
    SCALER_ERR_CREATE_CLIENT          =   0x40,
    SCALER_ERR_DELETE_CLIENT,
    SCALER_ERR_MALLOC_FRAME,
    SCALER_ERR_FREE_FRAME,
    SCALER_ERR_GET_FRAME,
    SCALER_ERR_PROCESS_FRAME,
    SCALER_ERR_DEV_OPEN,
    SCALER_ERR_DEV_CLOSE,
    SCALER_ERR_PARAMS,
    SCALER_ERR_STATE
} ENUM_SCALER_ERRNO;

#define  HAL_ERR_SCALER_CREATE_CLIENT                  AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_CREATE_CLIENT)
#define  HAL_ERR_SCALER_DELETE_CLIENT                  AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_DELETE_CLIENT)
#define  HAL_ERR_SCALER_MALLOC_FRAME                   AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_MALLOC_FRAME)
#define  HAL_ERR_SCALER_FREE_FRAME                     AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_FREE_FRAME)
#define  HAL_ERR_SCALER_GET_FRAME                      AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_GET_FRAME)
#define  HAL_ERR_SCALER_DEV_OPEN                       AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_DEV_OPEN)
#define  HAL_ERR_SCALER_DEV_CLOSE                      AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_DEV_CLOSE)
#define  HAL_ERR_SCALER_STATE                          AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_STATE)
#define  HAL_ERR_SCALER_PARAMS                         AR_HAL_DEF_ERR(AR_SYS_ID_SCALER, HAL_ERR_LEVEL_ERROR, SCALER_ERR_PARAMS)

#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */

#endif //__HAL_SCALER_H__

