#ifndef _ASM_ARCH_PROXIMA_CLOCK_H_
#define _ASM_ARCH_PROXIMA_CLOCK_H_

enum proxima_clk_id {
    /* CLK_SRC_INVAILD */
    CGU_AXI_CLK = 1,
    CGU_AXI_CLK_PRE_A,
    CGU_AXI_CLK_PRE_B,
    CGU_SYS_CLK,
    CGU_SYS_CLK_PRE_A,
    CGU_SYS_CLK_PRE_B,
    CGU_ISP_CLK,
    CGU_ISP_HDR_CLK,
    CGU_VIF_AXI_CLK,
    CGU_DLA_CLK,
    CGU_RSZ_CLK,
    CGU_DE_CLK,
    CGU_VENC_CLK,
    CGU_JPEG_CLK,
    CGU_MIPI_CSI_0_CLK,
    CGU_MIPI_CSI_1_CLK,
    CGU_MIPI_PCS_CLK,
    CGU_SD0_FIX_CLK,
    CGU_SD0_SAMPLE_CLK,
    CGU_SD0_DRV_CLK,
    CGU_SD1_FIX_CLK,
    CGU_SD1_SAMPLE_CLK,
    CGU_SD1_DRV_CLK,
    CGU_EMMC_FIX_CLK,
    CGU_EMMC_SAMPLE_CLK,
    CGU_EMMC_DRV_CLK,
    CGU_QSPI_CLK,
    CGU_I2S_MCLK,
    CGU_I2S_MST0_SCLK,
    CGU_I2S_MST1_SCLK,
    CGU_USB_PHY0_CLK,
    CGU_AUDIO_300M,
    CGU_AUDIO_ADC_CLK,
    CGU_GMAC_CORE_CLK,
    CGU_GMAC_PHY_CLK,
    CGU_HDECOMP_CLK,
    CGU_EFUSE_CLK,
    CGU_SENSOR_MCLK0,
    CGU_SENSOR_MCLK1,
    CGU_SENSOR_MCLK2,
    CGU_DVP_PATTERN_CLK,
    CGU_DVP_SUB_1_2X_PIX_CLK,
    CGU_NUC_CLK,
    CGU_SCGMAC_PTP_CLK,
    CGU_SCGMAC_RGMIITX_CLK,
    CGU_SCGMAC_MDC_CLK,
    CGU_NPU_CLK,
    CGU_NPU_CLK_PRE_A,
    CGU_NPU_CLK_PRE_B,
    CGU_NPU_ACLK,
    CGU_IFC_CLK,
    CGU_EIS_CLK,
    CGU_SCALER_CLK,
    CGU_GEN_CLK,
    CGU_SCAN_CLK_60M,
    CGU_SCAN_CLK_100M,
    CGU_SCAN_CLK_150M,
    CGU_SCAN_CLK_300M,
    CGU_SCAN_CLK_480M,
    CGU_SCAN_CLK_2400M,
    CGU_SCAN_CLK_I2S,
    MIPI_TX_PLL_ATE_OUT_CLK,
    AUDIO_PLL_ATE_OUT_CLK,
    PIXEL_PLL_ATE_OUT_CLK,
    FIX_PLL_ATE_OUT_CLK,
    ADC_PLL_ATE_OUT_CLK,
    CGU_CPU_CLK,
    CGU_CPU_CLK_PRE,
    CGU_CPU_CLK_PRE_A,
    CGU_CPU_CLK_PRE_B,
    CGU_CS_DBG_CLK,
    SYS_NPU_HCLK,
    SYS_GMACSC_CFG_CLK,
    SYS_DDR_PCLK,
    SYS_SYS_CFG_CLK,
    SYS_ANALOG_PCLK,
    SYS_RGU_PCLK,
    SYS_NOCMAIN_SYS_CLK,
    SYS_RTCDIG_PCLK,
    SYS_SYSCTRL_PCLK,
    SYS_AXIS_COMB_CLK,
    SYS_AXIM_COMB_CLK,
    SYS_SPACC_SYS_CLK,
    SYS_AHB_4TO1_CLK,
    SYS_GIC_CLK,
    SYS_SPIDGB_CFG_CLK,
    SYS_PERIP_CFG_CLK,
    SYS_ROM_CFG_CLK,
    SYS_VENC_CFG_CLK,
    SYS_HDE_CFG_CLK,
    SYS_EMMC_CFG_CLK,
    SYS_SEC_CFG_CLK,
    SYS_PMUX_CFG_CLK,
    SYS_GBE_CFG_CLK,
    SYS_USBOTG0_HCLK,
    SYS_DLA_CFG_CLK,
    SYS_VISION_CFG_CLK,
    SYS_A53_APB_CLK,
    AXI_VISION_DDR_ACLK,
    AXI_DDR_ACLK,
    AXI_A53_AXI_CLK,
    AXI_CCI_ACLK,
    AXI_VISION_ACLK,
    AXI_NOCMAIN_ACLK
};

enum proxima_clk_src {
	CLK_SRC_START = 1000,
	CGU_OSCIN_CLK,
	FIX_PLL_CLK100,
	FIX_PLL_CLK125,
	FIX_PLL_CLK250,
	FIX_PLL_CLK333,
	FIX_PLL_CLK400,
	FIX_PLL_CLK500,
	FIX_PLL_CLK600,
	FIX_PLL_CLK666,
	FIX_PLL_CLK800,
	FIX_PLL_CLK1000,//10
	FIX_PLL_CLK2000,
	ADC_PLL_CLK20,
	ADC_PLL_CLK25,
	ADC_PLL_CLK50,
	ADC_PLL_CLK60,
	ADC_PLL_CLK100,
	ADC_PLL_CLK150,
	ADC_PLL_CLK300,
	ADC_PLL_CLK400,
	ADC_PLL_CLK600,//20
	SD0_FIX_CLK,
	SD0_SAMPLE_CLK,
	SD0_DRV_CLK,
	SD1_FIX_CLK,
	SD1_SAMPLE_CLK,
	SD1_DRV_CLK,
	EMMC_FIX_CLK,
	EMMC_SAMPLE_CLK,
	EMMC_DRV_CLK,
	CLK_AUDIO_DIG,//30
	PIXEL_PLL_CLK,
	PIXEL_PLL_CLK1,
	PIXEL_PLL_CLK2,
	PIXEL_PLL_CLK3,
	PIXEL_PLL_CLK4,
	AUDIO_PLL_CLK,
	MIPI_PLL,
	MIPI_TX_PLL_DIV2,
	TST_PLL_DDR,
};

#endif
