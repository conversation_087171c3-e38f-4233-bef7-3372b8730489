#ifndef _ASM_ARCH_PROXIMA_RESET_H_
#define _ASM_ARCH_PROXIMA_RESET_H_

enum proxima_reset_id {
	PROXIMA_ROM_CFG_RSTN,
	PROXIMA_GIC_RSTN,
	PROXIMA_CCI_RSTN,
	PROXIMA_EFUSE_RSTN,
	PROXIMA_TROOT_RSTN,
	PROXIMA_RGU_SRAM_RSTN,
	PROXIMA_AHB_4TO1_RSTN,
	PROXIMA_SPACC_SYS_RSTN,
	PROXIMA_AXIM_COMB_RSTN,
	PROXIMA_AXIS_COMB_RSTN,
	PROXIMA_SYS_CFG_RSTN,
	PROXIMA_NOC_MAIN_RSTN,
	PROXIMA_VISION_RGU_RSTN,
	PROXIMA_VIF_MAIN_RGU_RSTN,
	PROXIMA_ISP_RGU_RSTN,
	PROXIMA_ISP_HDR_RGU_RSTN,
	PROXIMA_TOP_DMA_CORE_RGU_RSTN,
	PROXIMA_JPEG_RGU_RSTN,
	PROXIMA_SCALER_RGU_RSTN,
	PROXIMA_USBOTG0_CTL_RSTN,
	PROXIMA_USBOTG0_PHY_RSTN,
	PROXIMA_USBOTG0_PHY_PORT_RSTN,
	PROXIMA_GBE_MAC_RSTN,
	PROXIMA_QSPI_RGU_RSTN,
	PROXIMA_AUDIO_300M_RGU_RSTN,
	PROXIMA_VENC_CCLK_RGU_RSTN,
	PROXIMA_DLA_CORE_RGU_RSTN,
	PROXIMA_EMMC_CORE_RSTN,
	PROXIMA_HDECOMP_CORE_RSTN,
	PROXIMA_SENSOR_RGU_RSTN,
	PROXIMA_GBE_CFG_RSTN,
	PROXIMA_EMMC_HRSTN,
	PROXIMA_HDE_RGU_RSTN,
	PROXIMA_VENC_RGU_RSTN,
	PROXIMA_USBOTG0_HRSTN,
	PROXIMA_MIPI_PCS_RGU_RSTN,
	PROXIMA_PERIP_CFG_RSTN,
	PROXIMA_NUC_RSTN,
	PROXIMA_DDR_ARSTN,
	PROXIMA_TZC_ARSTN,
	PROXIMA_DDR_PRSTN,
	PROXIMA_TZC_PRSTN,
	PROXIMA_DDRCTL_RSTN,
	PROXIMA_DDRPHY_RSTN,
	PROXIMA_DLA_RGU_RSTN,
	PROXIMA_DISPLAY_CORE_RGU_RSTN,
	PROXIMA_SEC_RGU_RSTN,
	PROXIMA_SPIDBG_RGU_RSTN,
	PROXIMA_JPEG_CFG_RSTN,
	PROXIMA_SCALER_CFG_RSTN,
	PROXIMA_DISPLAY_CFG_RSTN,
	PROXIMA_DMA_CFG_RSTN,
	PROXIMA_MIPI_DSI_CFG_RSTN,
	PROXIMA_MIPI_CSI_CFG_RSTN,
	PROXIMA_VIF_CFG_RSTN,
	PROXIMA_ISP_CFG_RSTN,
	PROXIMA_SCGMAC_CORE_RSTN,
	PROXIMA_SCGMAC_PTP_RSTN,
	PROXIMA_NPU_HRESET_N,
	PROXIMA_NPU_ARESET_N,
	PROXIMA_NPU_RESET_N,
	PROXIMA_RSZ_SCALER_RGU_RSTN,
	PROXIMA_RSZ_IFC_RGU_RSTN,
	PROXIMA_A53_POR_RESET_N0,
	PROXIMA_A53_POR_RESET_N1,
	PROXIMA_A53_POR_RESET_N2,
	PROXIMA_A53_POR_RESET_N3,
	PROXIMA_A53_CORE_RESET_N0,
	PROXIMA_A53_CORE_RESET_N1,
	PROXIMA_A53_CORE_RESET_N2,
	PROXIMA_A53_CORE_RESET_N3,
	PROXIMA_A53_L2RESET_N,
	PROXIMA_A53_MBIST_RESET_N,
	PROXIMA_A53_PRESET_N,
	PROXIMA_A53_CGU_RESET_N,
	PROXIMA_A53_AXI_RESET_N,
	PROXIMA_A53_APB_RESET_N,
	PROXIMA_RSZ_EIS_RGU_RSTN,
	PROXIMA_CPU_POR_RESET_N,
	PROXIMA_CPU_CORE_RESET_N,
};

#endif
