#ifndef _ASM_ARCH_KUIPER_CLOCK_H_
#define _AS<PERSON>_ARCH_KUIPER_CLOCK_H_

#define K<PERSON>PER_PLL_VCO_MAX 3000000000
#define K<PERSON><PERSON>ER_PLL_VCO_MIN 1000000000

enum kuiper_clk_id {
    /* CLK_SRC_INVAILD */
    KUIPER_CGU_AXI_CLK = 1,
    KUIPER_CGU_AXI_CLK_PRE_A,
    <PERSON><PERSON><PERSON>ER_CGU_AXI_CLK_PRE_B,
    KUIPER_CGU_SYS_CLK,
    KUIPER_CGU_SYS_CLK_PRE_A,
    KUI<PERSON>ER_CGU_SYS_CLK_PRE_B,
    <PERSON><PERSON><PERSON>ER_CGU_MCU_CORE_CLK,
    KUIPER_CGU_MCU_CORE_CLK_PRE_A,
    <PERSON><PERSON><PERSON>ER_CGU_MCU_CORE_CLK_PRE_B,
    <PERSON><PERSON><PERSON><PERSON>_CGU_SRAM_CLK,
    <PERSON><PERSON><PERSON><PERSON>_CGU_SRAM_CLK_PRE_A,
    <PERSON><PERSON><PERSON><PERSON>_CGU_SRAM_CLK_PRE_B,
    K<PERSON>PER_CGU_DSP_CLK,
    <PERSON><PERSON>PER_CGU_DSP_CLK_PRE_A,
    KUIPER_CGU_DSP_CLK_PRE_B,
    KUIPER_CGU_ISP0_CLK,
    KUIPER_CGU_ISP_HDR_CLK,
    KUIPER_CGU_VIF0_AXI_CLK,
    KUIPER_CGU_DLA_CLK,
    KUIPER_CGU_DE0_CLK,
    KUIPER_CGU_VENC_CLK,
    KUIPER_CGU_JPEG_CLK,
    KUIPER_CGU_MIPI_CSI_0_CLK,
    KUIPER_CGU_MIPI_CSI_1_CLK,
    KUIPER_CGU_MIPI_PCS_CLK,
    KUIPER_CGU_DPISP_CLK,
    KUIPER_CGU_SD0_MCLK,
    KUIPER_CGU_SD0_FIX_CLK,
    KUIPER_CGU_SD0_SAMPLE_CLK,
    KUIPER_CGU_SD0_DRV_CLK,
    KUIPER_CGU_SD1_MCLK,
    KUIPER_CGU_SD1_FIX_CLK,
    KUIPER_CGU_SD1_SAMPLE_CLK,
    KUIPER_CGU_SD1_DRV_CLK,
    KUIPER_CGU_EMMC_MCLK,
    KUIPER_CGU_EMMC_FIX_CLK,
    KUIPER_CGU_EMMC_SAMPLE_CLK,
    KUIPER_CGU_EMMC_DRV_CLK,
    KUIPER_CGU_QSPI_CLK,
    KUIPER_CGU_TYPEC_REF_CLK,
    KUIPER_CGU_ISP1_CLK,
    KUIPER_CGU_ISP2_CLK,
    KUIPER_CGU_GMAC_CORE_CLK,
    KUIPER_CGU_GMAC_PHY_CLK,
    KUIPER_CGU_SENSOR_MCLK0,
    KUIPER_CGU_SENSOR_MCLK1,
    KUIPER_CGU_SENSOR_MCLK2,
    KUIPER_CGU_SENSOR_MCLK3,
    KUIPER_CGU_SENSOR_MCLK4,
    KUIPER_CGU_DVP_PATTERN_CLK,
    KUIPER_CGU_DVP_PIX0_CLK,
    KUIPER_CGU_NUC_CLK,
    KUIPER_CGU_VIF1_AXI_CLK,
    KUIPER_CGU_VIF2_AXI_CLK,
    KUIPER_CGU_PDM_CLK,
    KUIPER_CGU_CAN_PE_CLK,
    KUIPER_CGU_IFC_CLK,
    KUIPER_CGU_DP_VID_CLK,
    KUIPER_CGU_SCALER0_CLK,
    KUIPER_CGU_SCALER1_CLK,
    KUIPER_CGU_SCALER2_CLK,
    KUIPER_CGU_GDC0_CLK,
    KUIPER_CGU_GDC1_CLK,
    KUIPER_CGU_GDC2_CLK,
    KUIPER_CGU_GDI_AXI_CLK,
    KUIPER_CGU_GE2D_CLK,
    KUIPER_CGU_INFUSION_CLK,
    KUIPER_CGU_IVE_CLK,
    KUIPER_CGU_DPU_CLK,
    KUIPER_CGU_ISP_AXI_CLK,
    KUIPER_CGU_GDG_AXI_CLK,
    KUIPER_CGU_DE1_CLK,
    KUIPER_CGU_DP_BIST_CLK,
    KUIPER_CGU_DVP_PIX1_CLK,
    KUIPER_CGU_VENC_BPU_CLK,
    KUIPER_CGU_TS_CLK,
    KUIPER_CGU_TS_BG_CLK,
    KUIPER_CGU_SARADC_CLK,
    KUIPER_CGU_MCU_DBG_CLK,
    KUIPER_CGU_MIPI_CSI_2_CLK,
    KUIPER_CGU_MIPI_CSI_3_CLK,
    KUIPER_CGU_MIPI_CSI_4_CLK,
    KUIPER_CGU_OPT_CLK,
    KUIPER_CGU_AUDIO_MCLK0,
    KUIPER_CGU_AUDIO_MCLK1,
    KUIPER_CGU_AUDIO_MCLK2,
    KUIPER_CGU_AUDIO_MCLK3,
    KUIPER_CGU_AUDIO_MCLK4,
    KUIPER_CGU_MCLK_32K,
    KUIPER_CGU_GEN_CLK,
    KUIPER_CGU_ATE_FIX_PLL_1000,
    KUIPER_CGU_ATE_FIX_PLL_750,
    KUIPER_CGU_ATE_FIX_PLL_800,
    KUIPER_CGU_ATE_FIX_PLL_600,
    KUIPER_CGU_ATE_PIXEL_PLL0_CLKA,
    KUIPER_CGU_ATE_PIXEL_PLL0_CLKB,
    KUIPER_CGU_ATE_PIXEL_PLL1_CLKA,
    KUIPER_CGU_ATE_PIXEL_PLL1_CLKB,
    KUIPER_CGU_ATE_AUDIO_PLL_CLKA,
    KUIPER_CGU_ATE_AUDIO_PLL_CLKB,
    KUIPER_CGU_USB_PHY0_CLK,
    KUIPER_CGU_USB3_REF_CLK,
    KUIPER_CGU_EFUSE_CLK,
    KUIPER_CGU_DDR_REF_CLK,
    KUIPER_CGU_DSIPHY0_REFCLK,
    KUIPER_CGU_DSIPHY1_REFCLK,
    KUIPER_CGU_DSIPHY2_REFCLK,
    KUIPER_CGU_DSIPHY3_REFCLK,
    KUIPER_CGU_DP_REF_CLK,
    KUIPER_CGU_MCU_TIMER_CLK,
    KUIPER_CGU_CAN_TS_CLK,
    KUIPER_CGU_MCLK0_32K,
    KUIPER_CGU_MCLK1_32K,
    KUIPER_CGU_CPU_CLK,
    KUIPER_CGU_CPU_CLK_PRE,
    KUIPER_CGU_CPU_CLK_PRE_A,
    KUIPER_CGU_CPU_CLK_PRE_B,
    KUIPER_CGU_CS_DBG_CLK,

    /* system clock gating list */
    KUIPER_SYS_MAILBOX_PCLK,
    KUIPER_SYS_TS_PCLK,
    KUIPER_SYS_SARADC_PCLK,
    KUIPER_SYS_USB3_HCLK,
    KUIPER_SYS_GLBTIMER_PCLK,
    KUIPER_SYS_IRQCTRL_PCLK,
    KUIPER_SYS_PDM_PCLK,
    KUIPER_SYS_DDR_PCLK,
    KUIPER_SYS_SYS_CFG_CLK,
    KUIPER_SYS_ANALOG_PCLK,
    KUIPER_SYS_RGU_PCLK,
    KUIPER_SYS_NOCMAIN_SYS_CLK,
    KUIPER_SYS_RTCDIG_PCLK,
    KUIPER_SYS_SYSCTRL_PCLK,
    KUIPER_SYS_AXI_DEC_CLK,
    KUIPER_SYS_AXI_COMB_PERIPH_CLK,
    KUIPER_SYS_SPACC_SYS_CLK,
    KUIPER_SYS_AHB_COMB_CLK,
    KUIPER_SYS_GIC_CLK,
    KUIPER_SYS_SPIDBG_CFG_CLK,
    KUIPER_SYS_PERIP_CFG_CLK,
    KUIPER_SYS_ROM_CFG_CLK,
    KUIPER_SYS_VENC_CFG_CLK,
    KUIPER_SYS_CEVAXM6_APB_CLK,
    KUIPER_SYS_EMMC_CFG_CLK,
    KUIPER_SYS_SEC_CFG_CLK,
    KUIPER_SYS_PMUX_CFG_CLK,
    KUIPER_SYS_GBE_CFG_CLK,
    KUIPER_SYS_USBOTG0_HCLK,
    KUIPER_SYS_DLA_CFG_CLK,
    KUIPER_SYS_VISION_CFG_CLK,
    KUIPER_SYS_A53_APB_CLK,
    KUIPER_SYS_MCU_SYS_CLK,
    KUIPER_SYS_DP_SYS_CLK,
    KUIPER_SYS_AUDIO_PCLK,
    KUIPER_SYS_VOUT_TOP_CFGCLK,
    KUIPER_SYS_FSGEN_PCLK,
    KUIPER_AXI_DLA_ACLK,
    KUIPER_AXI_DSP_ACLK,
    KUIPER_AXI_DDR_ACLK,
    KUIPER_AXI_A53_AXI_CLK,
    KUIPER_AXI_CCI_ACLK,
    KUIPER_AXI_VISION_ACLK,
    KUIPER_AXI_NOCMAIN_ACLK,

    /* Vision cfg clock */
    KUIPER_OPT_CFGCLK,
    KUIPER_MIPI_CSI_PHY0_CFGCLK,
    KUIPER_MIPI_CSI_PHY1_CFGCLK,
    KUIPER_MIPI_CSI_PHY2_CFGCLK,
    KUIPER_MIPI_CSI_PHY3_CFGCLK,
    KUIPER_MIPI_CSI_PHY4_CFGCLK,
    KUIPER_MIPI_CSI_PHY5_CFGCLK,
    KUIPER_TOPDMA_CFGCLK,
    KUIPER_NOC_VISION_CFGCLK,
    KUIPER_GDI_SUB_CFGCLK,
    KUIPER_GDG_SUB_CFGCLK,
    KUIPER_JID_SUB_CFGCLK,
    KUIPER_ISP_SUB_CFGCLK,
    KUIPER_GRI_SUB_CFGCLK,
};

enum kuiper_clk_src {
    KUIPER_CLK_SRC_START = 1000,
    KUIPER_CGU_OSCIN_CLK,
    KUIPER_FIX_PLL0,
    KUIPER_FIX_PLL_800,
    KUIPER_FIX_PLL0_CLKA = KUIPER_FIX_PLL_800,
    KUIPER_FIX_PLL_600,
    KUIPER_FIX_PLL0_CLKB = KUIPER_FIX_PLL_600,
    KUIPER_FIX_PLL1,
    KUIPER_FIX_PLL_1000,
    KUIPER_FIX_PLL1_CLKA = KUIPER_FIX_PLL_1000,
    KUIPER_FIX_PLL_750,
    KUIPER_FIX_PLL1_CLKB = KUIPER_FIX_PLL_750,
    KUIPER_CPU_PLL,
    KUIPER_CPU_PLL_CLKA,
    KUIPER_CPU_PLL_CLKB,
    KUIPER_PIXEL_PLL0,
    KUIPER_PIXEL_PLL0_CLKA,
    KUIPER_PIXEL_PLL0_CLKB,
    KUIPER_PIXEL_PLL1,
    KUIPER_PIXEL_PLL1_CLKA,
    KUIPER_PIXEL_PLL1_CLKB,
    KUIPER_DDR_PLL,
    KUIPER_DDR_PLL_CLKA,
    KUIPER_DDR_PLL_CLKB,
    KUIPER_AUDIO_PLL,
    KUIPER_AUDIO_PLL_CLKA,
    KUIPER_AUDIO_PLL_CLKB,
    KUIPER_MEASURE_MUX_CLK,
};

#endif
