#ifndef _ASM_ARCH_POLESTAR_CLOCK_H_
#define _AS<PERSON>_ARCH_POLESTAR_CLOCK_H_

enum polestar_clk_id {
    /* CLK_SRC_INVAILD */
    CLK_CFG_CS = 1,
    <PERSON>L<PERSON>_CORE_CS,
    CL<PERSON>_CFG_HDECOMP,
    CL<PERSON>_CORE_HDECOMP,
    CLK_CFG_TOPDMAC,
    CLK_AXI_TOPDMAC,
    CLK_CORE_TOPDMAC,
    CLK_CFG_EMMC,
    CLK_EMMC_CARDCLK,
    CLK_CFG_SEC,
    CLK_CFG_EFUSE,
    CLK_CFG_ROM,
    CLK_CFG_SPIDBG,
    CLK_150_NOC_G0,
    CL<PERSON>_CFG_ISP,
    CLK_ISP,
    CLK_AXI_VIF,
    CLK_HDR,
    CLK_CFG_SCALER,
    CL<PERSON>_SCALER,
    CLK_CFG_JPEG,
    CLK_CORE_JPEG,
    CLK_CFG_DISP,
    CL<PERSON>_CORE_DISP,
    <PERSON><PERSON><PERSON>_150_NOC_G1,
    <PERSON>L<PERSON>_CFG_CEVA,
    CLK_150_NOC_G2,
    CL<PERSON>_CFG_DLA,
    CLK_CFG_VENC,
    CLK_AXI_VENC,
    CLK_BPU_VENC,
    CLK_150_NOC_G3,
    CLK_150_NOC_G4,
    CLK_PIXEL_DISP,
    CLK_PATTERN_GEN,
    CLK_SUB_1_1X_PIX,
    CLK_DVP_PIX_HLY,
    CLK_SUB_1_2X_PIX,
    CLK_DVP_PIX,
    CLK_DSI_PIX,
    CLK_CFG_DDR,
    CLK_DDR,
    CLK_CFG_MIPI,
    CLK_VIDEO_MIPI0,
    CLK_VIDEO_MIPI1,
    CLK_VIDEO_MIPI2,
    CLK_VIDEO_MIPI3,
    CLK_PCS_MIPI,
    CLK_CFG_DSI,
    CLK_CFG_VIF,
    CLK_CFG_DVPCTL,
    CLK_CFG_USB2OTG0,
    CLK_PHY_USB2PHY0,
    CLK_CFG_USB2OTG1,
    CLK_PHY_USB2PHY1,
    CLK_CFG_USB2OTG2,
    CLK_PHY_USB2PHY2,
    CLK_150_NOC_G5,
    CLK_CFG_GMAC,
    CLK_CORE_GMAC,
    CLK_PHY_GMAC,
    CLK_CCI,
    CLK_SRAM,
    CLK_600_NOC,
    CLK_SENSOR0,
    CLK_SENSOR1,
    CLK_SENSOR2,
    CLK_SENSOR3,
    CLK_CORE_PERIP,
    CLK_AUDIO_300M,
    CLK_AUDIO_ADC,
    CLK_PERIP_I2SM,
    CLK_SDC_FIXED,
    CLK_SDC_SAMPLE,
    CLK_SDC_DRV,
    CLK_CORE_QSPI,
    CLK_MAX_GATING,
};

enum polestar_clk_src {
    SRC_START_CLK = 1000,
    OSCIN_CLK,
    CLK_PLL_0,
    CLK_PLL_1,
    CLK_PLL_ADDA,
    CLK_PLL_BACK,
    CLK_PLL_PIXEL,
    CLK_PLL_PIXEL_DIV2,
    CLK_PLL_AUDIO,
    CLK_PLL_EMMC,
    AUDIO_PLL1,
    AUDIO_PLL1_DIV2,
    AUDIO_PLL2,
    PLL_DSP0_1000M,
    PLL_DSP0_666M,
    PLL_DSP0_500M,
    PLL_DSP0_400M,
    PLL_DSP0_333M,
    PLL_DSP0_250M,
    PLL_DSP0_200M,
    PLL_DSP0_125M,
    PLL_DSP0_100M,
    PLL_DSP1_900M,
    PLL_DSP1_600M,
    PLL_DSP1_450M,
    PLL_DSP1_360M,
    PLL_ADC_400M,
    PLL_ADC_300M,
    PLL_ADC_150M,
    PLL_ADC_75M,
    PLL_ADC_60M,
    PLL_ADC_50M,
    PLL_ADC_40M,
    PLL_ADC_25M,
    PLL_ADC_24M,
    PLL_OSC_20M,
    PLL_ADC_12M,
    PLL_BACK_DIV2,
    PLL_BACK_DIV4,
    PLL_BACK_DIV5,
    PLL_BACK_DIV8,
    PLL_AUDIO_DIV2,
    PLL_AUDIO_DIV3,
    PLL_AUDIO_DIV4,
    PLL_EMMC_MCLK,
    PLL_EMMC_FIX,
    PLL_EMMC_SAMPLE,
    PLL_EMMC_DRV,
    PLL_SD0_MCLK,
    PLL_SD0_FIX,
    PLL_SD0_SAMPLE,
    PLL_SD0_DRV
};

#endif
