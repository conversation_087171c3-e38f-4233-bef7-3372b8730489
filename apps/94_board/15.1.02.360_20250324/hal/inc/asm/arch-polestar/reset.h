#ifndef _ASM_ARCH_POLESTAR_RESET_H_
#define _ASM_ARCH_POLESTAR_RESET_H_

enum polestar_reset_id {
    RGU_ISP_CLK_RSTN,
    RGU_ISP_CFG_CLK_RSTN,
    RGU_ISP_HDR_CLK_RSTN,
    RGU_JPEG_PCLK_RSTN,
    RGU_JPEG_CCLK_RSTN,
    RGU_VENC_PCLK_RSTN,
    RGU_VENC_BCLK_RSTN,
    RGU_VENC_CCLK_RSTN,
    RGU_DE_HRESETN,
    RGU_DE_RESETN,
    RGU_SCALER_PCLK_RSTN,
    RGU_SCALER_CCLK_RSTN,
    RGU_MIPI_APB_RESETN,
    RGU_MIPI_PCS_RESETN,
    RGU_DSI_APB_RESETN,
    RGU_USB20_CTL0_HRSTN,
    RGU_USB20_CTL0_RSTN,
    RGU_USB20_PHY0_RSTN,
    RGU_USB20_PHY0PORT_RSTN,
    RGU_USB20_CTL1_HRSTN,
    RGU_USB20_CTL1_RSTN,
    RGU_USB20_PHY1_RSTN,
    RGU_USB20_PHY1PORT_RSTN,
    RGU_USB20_CTL2_HRSTN,
    RGU_USB20_CTL2_RSTN,
    RGU_USB20_PHY2_RSTN,
    RGU_USB20_PHY2PORT_RSTN,
    RGU_PERIP_RSTN_QSPI,
    RGU_PERIP_RSTN_MULTI,
    RGU_CS_RSTN,
    RGU_SEC_CPU_RSTN,
    RGU_SEC_SYS_RSTN,
    RGU_NOC_RSTN,
    RGU_DDR_ARSTN,
    RGU_DDR_PHY_ARSTN,
    RGU_DDR_CORE_DDRC_RSTN,
    RGU_DDR_PRSTN,
    RGU_DDR_JTAG_RSTN,
    RGU_GMAC_MAC_RSTN,
    RGU_GMAC_AHB_RSTN,
    RGU_EMMC_AHB_RSTN,
    RGU_EMMC_CORE_RSTN,
    RGU_DMAC_TOP_AHB_RSTN,
    RGU_DMAC_TOP_CORE_RSTN,
    RGU_VIDEO_IF_APB_RSTN,
    RGU_VIDEO_IF_MAIN_RSTN,
    RGU_VIDEO_IF_CFG_DVPCTL_RSTN,
    RGU_TZC_RSTN,
    RGU_BOOT_ROM_RSTN,
    RGU_SRAM_NOC_RSTN,
    RGU_HDECOMP_PRSTN,
    RGU_HDECOMP_ARSTN,
    RGU_DLA_PRESETN,
    RGU_DLA_RSTN,
    RGU_AUDIO_300M_RSTN
};


#endif

