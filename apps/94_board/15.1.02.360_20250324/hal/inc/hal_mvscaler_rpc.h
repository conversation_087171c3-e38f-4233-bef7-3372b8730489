#ifndef __HAL_MVSCALER_RPC_H__
#define __HAL_MVSCALER_RPC_H__
#include "hal_dbglog.h"
#include "hal_errno.h"
#include "hal_mvscaler_api.h"

#ifdef __cplusplus
#if __cplusplus
	extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#define AR_MVSCALER_DEV_NAME             "mvscaler_server"
#define AR_HAL_MOD_MVSCALER              HAL_TAG_ID(AR_SYS_ID_MVSCALER)

typedef enum
{
    MVSCALER_RPC_ID_PROCESS_USR_LUT  = 0,
    MVSCALER_RPC_ID_PROCESS_FRAME,
    MVSCALER_RPC_ID_PROCESS_SUSPEND,
    MVSCALER_RPC_ID_PROCESS_RESUME,
    MVSCALER_RPC_ID_PROCESS_FREQUENCY
} ENUM_MVSCALER_RPC_ID;

typedef struct
{
    AR_U32           u32PhyAddr;
    AR_U32           u32Width;
    AR_U32           u32Height;
    AR_U32           u32Stride;
} AR_MVSCALER_RES_S;

typedef struct
{
    AR_MVSCALER_RES_S stSrcRes;
    AR_U32            u32CropX;
    AR_U32            u32CropY;
    AR_U32            u32CropW;
    AR_U32            u32CropH;
    AR_MVSCALER_RES_S stDstRes[AR_MVSCALER_MAX_OUTPUT_NUM];
    AR_U32            u32OutNum;      // (1 - 4)
    AR_U32            u32ImgFormat;   // 0:single planar; 1:UV packet
} AR_MVSCALER_RESIZE_PARAMS_S;

typedef struct
{
    AR_U32            u32SmoothFactor;
    AR_U32            u32SharpFactor;
    AR_U32            u32Reserved[4];
} AR_MVSCALER_LUT_FACTOR_S;

typedef struct
{
    AR_U32                           u32CoreId;  //mvscaler index 0/1, only mvscaler0 support ld & cf50
    AR_U32                           u32FrameId;
    AR_U32                           u32Pannels;
    AR_U32                           u32OutNum;      // (1 - 4)
    AR_U8                            u8LdEn;
    AR_U8                            u8Cf50En;
    AR_HAL_MVSCALER_IMG_FORMAT_E     enFormat;
    AR_HAL_MVSCALER_LOWDELAY_INFO_S  stLdInfo;
    AR_HAL_MVSCALER_CF50_PARAM_S     stCf50Info;
    AR_MVSCALER_RESIZE_PARAMS_S      stMvParam[AR_MVSCALER_MAX_PANNELS];
} AR_MVSCALER_RPC_PARAMS_S;

typedef enum
{
    MVSCALER_SUCCUESS                   =   0,
    MVSCALER_ERR_CREATE_CLIENT          =   0x40,
    MVSCALER_ERR_DELETE_CLIENT,
    MVSCALER_ERR_MALLOC_FRAME,
    MVSCALER_ERR_FREE_FRAME,
    MVSCALER_ERR_GET_FRAME,
    MVSCALER_ERR_PROCESS_FRAME,
    MVSCALER_ERR_DEV_OPEN,
    MVSCALER_ERR_DEV_CLOSE,
    MVSCALER_ERR_PARAMS,
    MVSCALER_ERR_STATE
} ENUM_MVSCALER_ERRNO;

#define  HAL_ERR_MVSCALER_CREATE_CLIENT                  AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_CREATE_CLIENT)
#define  HAL_ERR_MVSCALER_DELETE_CLIENT                  AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_DELETE_CLIENT)
#define  HAL_ERR_MVSCALER_MALLOC_FRAME                   AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_MALLOC_FRAME)
#define  HAL_ERR_MVSCALER_FREE_FRAME                     AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_FREE_FRAME)
#define  HAL_ERR_MVSCALER_GET_FRAME                      AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_GET_FRAME)
#define  HAL_ERR_MVSCALER_DEV_OPEN                       AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_DEV_OPEN)
#define  HAL_ERR_MVSCALER_DEV_CLOSE                      AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_DEV_CLOSE)
#define  HAL_ERR_MVSCALER_STATE                          AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_STATE)
#define  HAL_ERR_MVSCALER_PARAMS                         AR_HAL_DEF_ERR(AR_SYS_ID_MVSCALER, HAL_ERR_LEVEL_ERROR, MVSCALER_ERR_PARAMS)

#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */

#endif //__HAL_MVSCALER_RPC_H__

