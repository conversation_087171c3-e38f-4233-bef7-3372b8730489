
#ifndef __HAL_CIPHER_H__
#define __HAL_CIPHER_H__

#include "hal_type.h"
#include "hal_errno.h"
#include "hal_comm.h"

#ifdef __cplusplus
extern "C" {
#endif    /* __cplusplus */

/******************************* API Declaration *****************************/
/** \addtogroup      hal */
/** @{ */  /** <!--[hal]*/

/** Cipher work mode */
typedef enum enum_cipher_mode {
    AR_CIPHER_MODE_ECB,        /**<Electronic codebook (ECB) mode*/
    AR_CIPHER_MODE_CBC,        /**<Cipher block chaining (CBC) mode*/
    AR_CIPHER_MODE_CFB,        /**<Cipher feedback (CFB) mode*/
    AR_CIPHER_MODE_OFB,        /**<Output feedback (OFB) mode*/
    AR_CIPHER_MODE_CTR,        /**<Counter (CTR) mode*/
    AR_CIPHER_MODE_CCM,        /**<Counter (GCM) mode*/
    AR_CIPHER_MODE_GCM,        /**<Counter (GCM) mode*/
    AR_CIPHER_MODE_BUTT,
    AR_CIPHER_MODE_INVALID  = 0xffffffff,
}ENUM_CIPHER_MODE_E;

/** Cipher algorithm */
typedef enum enum_cipher_alg {
    AR_CIPHER_ALG_DES           = 0x0,  /**< Data encryption standard (DES) algorithm */
    AR_CIPHER_ALG_3DES          = 0x1,  /**< 3DES algorithm */
    AR_CIPHER_ALG_AES           = 0x2,  /**< Advanced encryption standard (AES) algorithm */
    AR_CIPHER_ALG_BUTT          = 0x3,
    AR_CIPHER_ALG_INVALID       = 0xffffffff,
}ENUM_CIPHER_ALG_E;

/** key source */
typedef enum enum_cipher_key_source {
    AR_CIPHER_KEY_SRC_USER          = 0x0,  /**< User Key */
    AR_CIPHER_KEY_SRC_ROOTKEY_0     = 0x1,  /**< Root Key 0 */
    AR_CIPHER_KEY_SRC_ROOTKEY_1     = 0x2,  /**< Root Key 1 */
    AR_CIPHER_KEY_SRC_ROOTKEY_2     = 0x3,  /**< Root Key 2 */
    AR_CIPHER_KEY_SRC_ROOTKEY_3     = 0x4,  /**< Root Key 3 */
    AR_CIPHER_KEY_SRC_BUTT,
    AR_CIPHER_KEY_SRC_INVALID = 0xffffffff,
}ENUM_CIPHER_KEY_SOURCE_E;

/** address type */
typedef enum enum_cipher_address_type {
    AR_CIPHER_ADDRESS_TYPE_VIRTUAL      = 0x0,  /**< Buffer addresses are virtual */
    AR_CIPHER_ADDRESS_TYPE_PHYSICAL     = 0x1,  /**< Buffer addresses are physical */
    AR_CIPHER_ADDRESS_TYPE_BUTT,
    AR_CIPHER_ADDRESS_TYPE_INVALID = 0xffffffff,
}ENUM_CIPHER_ADDRESS_TYPE_E;

/** Cipher attributes */
typedef struct stru_cipher_attr {
    AR_U8 *key;                                     /**< User input key */
    AR_U32 key_length;                              /**< Key length */
    ENUM_CIPHER_ALG_E alg;                          /**< Cipher algorithm */
    ENUM_CIPHER_MODE_E mode;                        /**< Operating mode */
    ENUM_CIPHER_KEY_SOURCE_E key_source;            /**< Key source, for future */
}STRU_CIPHER_ATTR_S;

/** Hash algrithm algorithm */
typedef enum enum_hash_alg {
    AR_HASH_ALG_MD5,
    AR_HASH_ALG_SHA1,
    AR_HASH_ALG_SHA224,
    AR_HASH_ALG_SHA256,
    AR_HASH_ALG_SHA384,
    AR_HASH_ALG_SHA512,
    AR_HASH_ALG_BUTT,
    AR_HASH_ALG_INVALID  = 0xffffffff,
}ENUM_HASH_ALG_E;

/** Hash algrithm mode */
typedef enum enum_hash_mode {
    AR_HASH_MODE_RAW,
    AR_HASH_MODE_HMAC,
    AR_HASH_MODE_BUTT,
    AR_HASH_MODE_INVALID  = 0xffffffff,
}ENUM_HASH_MODE_E;

/** Hash attribute struct */
typedef struct stru_hash_attr {
    AR_U8 *key;                                     /**< User input key */
    AR_U32 key_length;                              /**< Key length */
    ENUM_HASH_ALG_E alg;                            /**< Hash algorithm */
    ENUM_HASH_MODE_E mode;                          /**< Hash mode */
}STRU_HASH_ATTR_S;

/** mask generation function algrithm */
typedef enum enum_mgf_alg {
    AR_MGF_ALG_MGF1,
    AR_MGF_ALG_BUTT,
    AR_MGF_ALG_INVALID  = 0xffffffff,
}ENUM_MGF_ALG_E;

/** MGF attribute struct */
typedef struct stru_mgf_attr {
    ENUM_MGF_ALG_E alg;                             /**< MGF algorithm */
    ENUM_HASH_ALG_E hash_alg;                       /**< MGF hash algorithm */
}STRU_MGF_ATTR_S;

typedef enum enum_rsa_enc_padding {
    AR_RSA_ENC_NO_PADDING,            /**< no padding */
    AR_RSA_ENC_RSAES_PKCS1_V1_5,      /**< PKCS#1 RSAES-PKCS1_V1_5 padding*/
    AR_RSA_ENC_RSAES_OAEP,            /**< PKCS#1 RSAES-OAEP padding*/
    AR_RSA_ENC_BUTT,
    AR_RSA_ENC_INVALID  = 0xffffffff,
}ENUM_RSA_ENC_PADDING_E;

typedef enum enum_rsa_sign_padding {
    AR_RSA_SIGN_RSASSA_PKCS1_V1_5 = 0x100, /**< PKCS#1 RSASSA_PKCS1_V1_5 signature*/
    AR_RSA_SIGN_RSASSA_PSS,                /**< PKCS#1 RSASSA_PSS signature*/
    AR_RSA_SIGN_BUTT,
    AR_RSA_SIGN_INVALID  = 0xffffffff,
}ENUM_RSA_SIGN_PADDING_E;

typedef enum enum_pss_padding_special_salt_len {
    AR_RSA_PSS_SALT_LEN_MAX = -3,
    AR_RSA_PSS_SALT_LEN_AUTO = -2,
    AR_RSA_PSS_SALT_LEN_DIGEST = -1,
    AR_RSA_PSS_SALT_LEN_INVALID = 0xffffffff,
}ENUM_PSS_PADDING_SPECIAL_SALT_LEN_E;

typedef struct stru_rsa_pub_key {
    AR_U8  *n;              /**< point to public modulus  */
    AR_U8  *e;              /**< point to public exponent */
    AR_U16 n_length;        /**< length of public modulus, max value is 512Byte*/
    AR_U16 e_length;        /**< length of public exponent, max value is 512Byte*/
}STRU_RSA_PUB_KEY_S;

/** RSA private key struct */
typedef struct stru_rsa_pri_key {
    AR_U8 *n;                       /**< public modulus    */
    AR_U8 *d;                       /**< private exponent  */
    AR_U16 n_length;                /**< length of public modulus */
    AR_U16 d_length;                /**< length of private exponent */
}STRU_RSA_PRI_KEY_S;

/** RSA public key encryption struct input */
typedef struct stru_rsa_pub_enc {
    ENUM_RSA_ENC_PADDING_E padding; /**< RSA encryption padding*/
    STRU_RSA_PUB_KEY_S pub_key;     /**< RSA private key struct */
    STRU_MGF_ATTR_S mgf;            /**< RSA mgf struct, for RSAES_OAEP padding */
    AR_U8 *label;                   /**< Optional label to be associated with the message, only for RSAES_OAEP padding */
    AR_U32 label_length;            /**< Length of the optional label to be associated with the message, only for RSAES_OAEP padding */
    ENUM_HASH_ALG_E hash_alg;       /**< Hash algorithm for Optional label to be associated with the message, only for RSAES_OAEP padding */
}STRU_RSA_PUB_ENC_S;

/** RSA private key encryption struct input */
typedef struct stru_rsa_pri_enc {
    ENUM_RSA_ENC_PADDING_E padding; /**< RSA encryption padding*/
    STRU_RSA_PRI_KEY_S pri_key;     /**< RSA public key struct */
    ENUM_HASH_ALG_E hash_alg;       /**< Hash algorithm for Optional label to be associated with the message, only for RSAES_OAEP padding */
    STRU_MGF_ATTR_S mgf;            /**< RSA mgf struct, only for RSAES_OAEP padding */
    AR_U8 *label;                   /**< Optional label to be associated with the message, only for RSAES_OAEP padding */
    AR_U32 label_length;            /**< Length of the optional label to be associated with the message, only for RSAES_OAEP padding */
}STRU_RSA_PRI_ENC_S;

/** RSA signature struct input */
typedef struct stru_rsa_sign {
    ENUM_RSA_SIGN_PADDING_E padding;  /**< RSA signature padding*/
    STRU_RSA_PRI_KEY_S pri_key;       /**< RSA private key struct */
    ENUM_HASH_ALG_E hash_alg;         /**< Hash algorithm */
    STRU_MGF_ATTR_S mgf;              /**< RSA mgf struct, only for RSASSA_PKCS1_PSS padding */
    AR_S32 salt_length;               /**< Intended length in octets of the salt, only for RSASSA_PKCS1_PSS padding.
                                           There are three special salt length, see ENUM_PSS_PADDING_SPECIAL_SALT_LEN_E*/
}STRU_RSA_SIGN_S;

/** RSA signature verify struct input */
typedef struct stru_rsa_verify {
    ENUM_RSA_SIGN_PADDING_E padding;  /**< RSA signature padding*/
    STRU_RSA_PUB_KEY_S pub_key;       /**< RSA public key struct */
    ENUM_HASH_ALG_E hash_alg;         /**< Hash algorithm */
    STRU_MGF_ATTR_S mgf;              /**< RSA mgf struct, only for RSASSA_PKCS1_PSS padding */
    AR_S32 salt_length;               /**< Intended length in octets of the salt, only for RSASSA_PKCS1_PSS padding.
                                           There are three special salt length, see ENUM_PSS_PADDING_SPECIAL_SALT_LEN_E*/
}STRU_RSA_VERIFY_S;

/** @}*/  /** <!-- ==== Structure Definition end ====*/

/******************************* API Code *****************************/
/** \addtogroup      crypto*/
/** @{*/  /** <!-- [link]*/

/**
\brief   hal cipher Init.
\retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_cipher_init(void);

/**
 * \brief   hal cipher Deinit.
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_cipher_deinit(void);

/**
 * \brief   Create cipher handle.
 * \param[out] handle The created handle.
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_cipher_create(AR_HANDLE *handle);

/**
\brief   Destroy cipher handle.
\param[in]  handle The cipher handle.
\retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_cipher_destroy(AR_HANDLE handle);

/**
\brief  set cipher work params.
* \param[in]  handle The cipher handle.
* \param[in]  config cipher work params, see STRU_CIPHER_ATTR_S.
\retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_cipher_config(AR_HANDLE handle, const STRU_CIPHER_ATTR_S *attr);

/**
\brief  set cipher address type.
* \param[in]  handle The cipher handle.
* \param[in]  address_type cipher address type, see ENUM_CIPHER_ADDRESS_TYPE_E.
\retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_cipher_address_type(AR_HANDLE handle, ENUM_CIPHER_ADDRESS_TYPE_E address_type);

/**
 * \brief   cipher encryption.
 *
 * \param[in] handle   The cipher handle.
 * \param[in] input    buffer holding the input data
 * \param[in] output   buffer holding the output data
 * \param[in] length   length of the input data
 * \param[in] iv  buffer holding the iv data, iv will be updated for possible chaining to the next packet
 *
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
 */
AR_S32 hal_cipher_encrypt(AR_HANDLE handle, const AR_VOID *input, AR_VOID *output, AR_U32 length, AR_VOID *iv);

/**
 * \brief   cipher decryption.
 *
 * \param[in] handle   The cipher handle.
 * \param[in] input    buffer holding the input data
 * \param[in] output   buffer holding the output data
 * \param[in] length   length of the input data, iv will be updated for possible chaining to the next packet
 * \param[in] iv  buffer holding the iv data
 *
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
 */
AR_S32 hal_cipher_decrypt(AR_HANDLE handle, const AR_VOID *input, AR_VOID *output, AR_U32 length, AR_VOID *iv);

/**
 * \brief   aead encryption.
 *
 * \param[in] handle         The cipher handle.
 * \param[in] input          buffer holding the input data
 * \param[in] input_length   length of the input data
 * \param[in] output         buffer holding the output data, tag appended to the end
 * \param[out] output_length length of the output data
 * \param[in] nonce          buffer holding the nonce data
 * \param[in] nonce_length   length of the nonce data
 * \param[in] assoc          buffer holding the associated data for authentication
 * \param[in] assoc_length   length of the nonce data
 * \param[in] tag_length     length of the tag data
 *
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
 */
AR_S32 hal_aead_encrypt(AR_HANDLE handle, const AR_VOID *input, AR_U32 input_length,
                        AR_VOID *output, AR_U32 *output_length,
                        const AR_VOID *nonce, AR_U32 nonce_length,
                        const AR_VOID *assoc, AR_U32 assoc_length, AR_U32 tag_length);

/**
 * \brief   aead decryption.
 *
 * \param[in] handle         The cipher handle.
 * \param[in] input          buffer holding the input data
 * \param[in] input_length   length of the input data
 * \param[in] output         buffer holding the output data, tag appended to the end
 * \param[out] output_length length of the output data
 * \param[in] nonce          buffer holding the nonce data
 * \param[in] nonce_length   length of the nonce data
 * \param[in] assoc          buffer holding the associated data for authentication
 * \param[in] assoc_length   length of the nonce data
 * \param[in] tag_length     length of the tag data
 *
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
 */
AR_S32 hal_aead_decrypt(AR_HANDLE handle, const AR_VOID *input, AR_U32 input_length,
                        AR_VOID *output, AR_U32 *output_length,
                        const AR_VOID *nonce, AR_U32 nonce_length,
                        const AR_VOID *assoc, AR_U32 assoc_length, AR_U32 tag_length);

/**
 * \brief   HASH context setup.
 *
 * \param[out] handle The hash handle.
 * \param[in] attr    Hash attributes
 *
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
 */
AR_S32 hal_hash_init(AR_HANDLE *handle, const STRU_HASH_ATTR_S *attr);

/**
 * \brief          HASH process buffer.
 *
 * \param[in] handle   The hash handle.
 * \param[in] input    buffer holding the input data
 * \param[in] length   length of the input data
 * \param[in] src      source of hash message
 *
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
 */
AR_S32 hal_hash_update(AR_HANDLE handle, const AR_VOID *input, AR_U32 length);

/**
 * \brief          HASH final digest.
 *
 * \param[in] handle          The hash handle.
 * \param[out] digest         buffer holding the hash data
 *
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
 */
AR_S32 hal_hash_final(AR_HANDLE handle, AR_VOID *digest);

/**
 * \brief          HASH final digest.
 *
 * \param[in] attr            Hash attributes
 * \param[in] input           buffer holding the input data
 * \param[in] length          length of the input data
 * \param[out] digest         buffer holding the hash data
 *
 * \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
 */
AR_S32 hal_hash_digest(const STRU_HASH_ATTR_S *attr,
                        const AR_VOID *input, AR_U32 length, AR_VOID *digest);

/**
* \brief RSA encryption a plaintext with a RSA public key.
*
* \param[in] key       rsa attr struct.
* \param[in] in        input data to be encrypted, usually a message digest
* \param[out] inlen    length of input data to be encryption
* \param[out] out      encrypted output data
* \param[out] outlen   length of encrypted output data
*
* \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_rsa_public_encrypt(const STRU_RSA_PUB_ENC_S *attr,
                        const AR_VOID *in, AR_U32 inlen, AR_VOID *out, AR_U32 *outlen);

/**
* \brief RSA decryption a ciphertext with a RSA private key.
*
* \param[in] key       rsa attr struct.
* \param[in] in        input data to be decrypted
* \param[in] inlen     length of input data to be decrypted
* \param[out] out      decrypted output data
* \param[out] outlen   length of decrypted output data
*
* \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_rsa_private_decrypt(const STRU_RSA_PRI_ENC_S *attr,
                        const AR_VOID *in, AR_U32 inlen, AR_VOID *out, AR_U32 *outlen);

/**
* \brief RSA encryption a plaintext with a RSA private key.
*
* \param[in] key       rsa attr struct.
* \param[in] in        input data to be encrypted, usually a message digest
* \param[out] inlen    length of input data to be encryption
* \param[out] out      encrypted output data
* \param[out] outlen   length of encrypted output data
*
* \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_rsa_private_encrypt(const STRU_RSA_PRI_ENC_S *attr,
                        const AR_VOID *in, AR_U32 inlen, AR_VOID *out, AR_U32 *outlen);

/**
* \brief RSA decryption a ciphertext with a RSA public key.
*
* \param[in] key       rsa attr struct.
* \param[in] in        input data to be decrypted
* \param[in] inlen     length of input data to be decrypted
* \param[out] out      decrypted output data
* \param[out] outlen   length of decrypted output data
*
* \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_rsa_public_decrypt(const STRU_RSA_PUB_ENC_S *attr,
                        const AR_VOID *in, AR_U32 inlen, AR_VOID *out, AR_U32 *outlen);

/**
* \brief RSA signature a context, where a signer's RSA private key is used.
*
* \param[in] key       rsa attr struct.
* \param[in] in        input data to be signed
* \param[in] inlen     length of input data to be signed
* \param[out] sign     output signature
* \param[out] signlen  length of output signature
*
* \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_rsa_sign(const STRU_RSA_SIGN_S *attr,
                      const AR_VOID *in, AR_U32 inlen, AR_VOID *sign, AR_U32 *signlen);

/**
* \brief RSA verify a ciphertext with a RSA public key.
*
* \param[in]  key      rsa attr struct.
* \param[in]  in       input data to be verified
* \param[in]  inlen    length of input data to be verified
* \param[in]  sign     signature of input data
* \param[in]  signlen  length of signature
*
* \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_rsa_verify(const STRU_RSA_VERIFY_S *attr,
                        const AR_VOID *in, AR_U32 inlen, const AR_VOID *sign, AR_U32 signlen);

/**
* \brief get true random number.
*
* \param[out] out      output random number data
* \param[in]  outlen   length of random number data to fetch
*
* \retval     On success, AR_SUCCESS is returned.  On error, AR_FAILURE is returned.
*/
AR_S32 hal_trng_get_random(AR_U8 *out, AR_U32 size);

/** @}*/  /** <!-- ==== API Code end ====*/

#ifdef __cplusplus
}
#endif    /* __cplusplus */

#endif    /* End of #ifndef __HAL_CIPHER_H__*/
