#ifndef __HAL_INFUSION_H__
#define __HAL_INFUSION_H__

#include "ar_infusion.h"

AR_S32 ar_hal_infusion_get_preset_tuning(ENUM_AR_INFUSION_WORK_MODE_T emMode, STRU_AR_INFUSION_TUNING_T *pstTuning);
AR_S32 ar_hal_infusion_open();
AR_VOID ar_hal_infusion_close(AR_S32 s32Fd);
AR_S32 ar_hal_infusion_set_frame(AR_S32 s32Fd, STRU_AR_INFUSION_FRAME_INFO_T *pstFrameInfo);
AR_S32 ar_hal_infusion_get_frame(AR_S32 s32Fd, STRU_AR_INFUSION_FRAME_INFO_T *pstFrameInfo);
AR_S32 ar_hal_infusion_start(AR_S32 s32Fd, STRU_AR_INFUSION_WORK_MODE_T *pstWorkMode);
AR_S32 ar_hal_infusion_stop(AR_S32 s32Fd);
AR_S32 ar_hal_infusion_set_tuning(AR_S32 s32Fd, STRU_AR_INFUSION_TUNING_T *pstTuning);
AR_S32 ar_hal_infusion_get_tuning(AR_S32 s32Fd, STRU_AR_INFUSION_TUNING_T *pstTuning);
AR_S32 ar_hal_infusion_get_frame_done(AR_S32 s32Fd, AR_U32 *pu32FrameDone);
AR_S32 ar_hal_infusion_get_wline_done(AR_S32 s32Fd, AR_U32 *pu32WlineDone);

#endif

