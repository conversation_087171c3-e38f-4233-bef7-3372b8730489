/**
 * \file
 * \brief 描述图像匹配相关的数据结构和接口.
 */

#ifndef  __HAL_DPU_MATCH_H__
#define  __HAL_DPU_MATCH_H__

#include "hal_comm_dpu_match.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* #ifdef __cplusplus */

/********************************API Definition********************************/
/**
\addtogroup HAL_DPU
 * @brief DPU(Disparity Process Unit)对输入的校正后的左图像和右图像进行匹配计算得出深度图。
 * DPU只能对校正后的左右图像进行匹配；对于未校正的图像，需先通过GDC校正，再进行匹配。
 * @{
*/

/**
\brief 获取辅助内存字节数。
\param[in]  u32_dst_height   :   输出的图像宽
\param[in]  u32_dst_height   :   输出的图像高
\param[out] p_u32_size       :   辅助内存字节数。不能为空。
\retval ::0                  :   成功。
\retval ::non-zero           :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_get_assist_buf_size(AR_U32 u32_dst_width,
                                            AR_U32 u32_dst_height,
                                            AR_U32 *p_u32_size);

/**
\brief 初始化DPU的资源。
\retval ::0                  :   成功。
\retval ::non-zero           :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_init_device();

/**
\brief 创建组。不支持重复创建。
\param[in] match_grp       :   组号。取值范围：[0, 8)。
\param[in] p_st_grp_attr   :   组属性。
\retval ::0                :   成功。
\retval ::non-zero         :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_create_grp(AR_DPU_MATCH_GRP match_grp,
                                   const STRU_DPU_MATCH_GRP_ATTR *p_st_grp_attr);

/**
\brief 销毁组。组必须已创建。
\param[in] match_grp   :   组号。取值范围：[0, 8)。
\retval ::0            :   成功。
\retval ::non-zero     :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_destroy_grp(AR_DPU_MATCH_GRP match_grp);

/**
\brief 设置组属性。组必须已创建。
\param[in] match_grp       :   组号。取值范围：[0, 8)。
\param[in] p_st_grp_attr   :   组属性。
\retval ::0                :   成功。
\retval ::non-zero         :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_set_grp_attr(AR_DPU_MATCH_GRP match_grp,
                                     const STRU_DPU_MATCH_GRP_ATTR *p_st_grp_attr);

/**
\brief 获取组属性。组必须已创建。
\param[in] match_grp        :   组号。取值范围：[0, 8)。
\param[out] p_st_grp_attr   :   组属性。
\retval ::0                 :   成功。
\retval ::non-zero          :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_get_grp_attr(AR_DPU_MATCH_GRP match_grp,
                                     STRU_DPU_MATCH_GRP_ATTR *p_st_grp_attr);

/**
\brief 启用组。组必须已创建。组必须已创建。
\param[in] match_grp   :   组号。取值范围：[0, 8)。
\retval ::0            :   成功。
\retval ::non-zero     :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_start_grp(AR_DPU_MATCH_GRP match_grp);

/**
\brief 禁用组。组必须已创建。
\param[in] match_grp   :   组号。取值范围：[0, 8)。
\retval ::0            :   成功。
\retval ::non-zero     :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_stop_grp(AR_DPU_MATCH_GRP match_grp);

/**
\brief 设置通道属性。组必须已创建。
\param[in] match_grp   :   组号。取值范围：[0, 8)。
\param[in] match_chn   :   通道号。取值范围：[0,1)。
\param[in] match_grp   :   通道属性。不能为空。
\retval ::0            :   成功。
\retval ::non-zero     :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_set_chn_attr(AR_DPU_MATCH_GRP match_grp,
                                     AR_DPU_MATCH_CHN match_chn,
                                     const STRU_DPU_MATCH_CHN_ATTR *p_st_chn_attr);

/**
\brief 获取通道属性。组必须已创建。
\param[in] match_grp    :   组号。取值范围：[0, 8)。
\param[in] match_chn    :   通道号。取值范围：[0,1)。
\param[out] match_grp   :   通道属性。不能为空。
\retval ::0             :   成功。
\retval ::non-zero      :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_get_chn_attr(AR_DPU_MATCH_GRP match_grp,
                                     AR_DPU_MATCH_CHN match_chn,
                                     STRU_DPU_MATCH_CHN_ATTR *p_st_chn_attr);

/**
\brief 启用通道。组必须已创建。
\param[in] match_grp   :   组号。取值范围：[0, 8)。
\param[in] match_chn   :   通道号。取值范围：[0,1)。
\retval ::0            :   成功。
\retval ::non-zero     :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_enable_chn(AR_DPU_MATCH_GRP match_grp,
                                   AR_DPU_MATCH_CHN match_chn);

/**
\brief 禁用通道。组必须已创建。
\param[in] match_grp   :   组号。取值范围：[0, 8)。
\param[in] match_chn   :   通道号。取值范围：[0,1)。
\retval ::0            :   成功。
\retval ::non-zero     :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_disable_chn(AR_DPU_MATCH_GRP match_grp,
                                    AR_DPU_MATCH_CHN match_chn);

/**
\brief 读取组状态。组必须已创建。
\param[in] match_grp          :   组号。取值范围：[0, 8)。
\retval ::0                   :   队列空。
\retval ::non-zero            :   队列满，无法继续送帧。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_buffer_full(AR_DPU_MATCH_GRP match_grp);

/**
\brief 用户发送数据。组必须已创建。
\param[in] match_grp          :   组号。取值范围：[0, 8)。
\param[in] p_st_left_frame    :   左图图像。不能为空。
\param[in] p_st_right_frame   :   右图图像。不能为空。
\param[in] s32_ms             :   超时参数 s32_ms 设为-1 时，为阻塞接口；
                                  0时为非阻塞接口；大于 0 时为超时等待时间，超时时间的单位为毫秒（ms）。
\retval ::0                   :   成功。
\retval ::non-zero            :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_send_frame(AR_DPU_MATCH_GRP match_grp,
                                   const STRU_DPU_FRAME_INFO *p_st_left_frame,
                                   const STRU_DPU_FRAME_INFO *p_st_right_frame,
                                   AR_S32 s32_ms);

/**
\brief 用户从通道获取一帧处理完成的图像。组必须已创建。
\param[in] match_grp           :   组号。取值范围：[0, 8)。
\param[out] p_st_left_frame    :   左图图像。不能为空。
\param[out] p_st_right_frame   :   右图图像。不能为空。
\param[out] p_st_dst_frame     :   匹配后图像。不能为空。
\param[in] s32_ms              :   超时参数 s32_ms 设为-1 时，为阻塞接口；
                                   0时为非阻塞接口；大于 0 时为超时等待时间，超时时间的单位为毫秒（ms）。
\retval ::0                    :   成功。
\retval ::non-zero             :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_get_frame(AR_DPU_MATCH_GRP match_grp,
                                  STRU_DPU_FRAME_INFO *p_st_src_left_frame,
                                  STRU_DPU_FRAME_INFO *p_st_src_right_frame,
                                  STRU_DPU_FRAME_INFO *p_st_dst_frame,
                                  AR_S32 s32_ms);

/**
\brief 用户释放一帧通道图像。组必须已创建。
\param[in] match_grp          :   组号。取值范围：[0, 8)。
\param[in] p_st_left_frame    :   左图图像。不能为空。
\param[in] p_st_right_frame   :   右图图像。不能为空。
\param[in] p_st_dst_frame     :   匹配后图像。不能为空。
\retval ::0                   :   成功。
\retval ::non-zero            :   失败，其值为错误码。
\see \n
N/A
*/
AR_S32 ar_hal_dpu_match_release_frame(AR_DPU_MATCH_GRP match_grp,
                                      const STRU_DPU_FRAME_INFO *p_st_src_left_frame,
                                      const STRU_DPU_FRAME_INFO *p_st_src_right_frame,
                                      const STRU_DPU_FRAME_INFO *p_st_dst_frame);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
/** @} */

#endif /* __HAL_DPU_MATCH_H__ */

