#ifndef __HAL_CLK_H__
#define __HAL_CLK_H__

#include "hal_type.h"
#include "asm/arch/clock.h"
#include "asm/arch/power.h"
#include "asm/arch/reset.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

#define CLK_SRC_INVAILD             0

typedef enum
{
    CLK_AUDIO_RATE_8000_64000, //8000, 16000, 32000, 64000
    CLK_AUDIO_RATE_11025_44100,//11025, 22050, 44100
    CLK_AUDIO_RATE_12000_96000,//12000, 24000, 48000, 96000
    CLK_AUDIO_RATE_BUTT,
} ENUM_CLK_AUDIO;

int ar_hal_clk_set_rate(unsigned int clk_id, unsigned long rate_khz);
int ar_hal_clk_set_rate_safe_hz(unsigned int clk_id, unsigned long rate_hz);
unsigned long ar_hal_clk_get_rate_hz(unsigned int clk_id);
int ar_hal_clk_enable_byid(unsigned int clk_id);
int ar_hal_clk_disable_byid(unsigned int clk_id);
int ar_hal_set_pix_clk(float rate_khz);
int ar_hal_audio_clk_select(ENUM_CLK_AUDIO rate);

#define ar_hal_clk_src_claim(src_id, freq)  ar_hal_clk_src_request(src_id, freq, __func__, __LINE__)
int ar_hal_clk_src_request(unsigned int src_id, unsigned int freq, const char *func, int line);
int ar_hal_clk_src_release(unsigned int src_id);
int ar_hal_clk_src_bind(unsigned int clk_id, unsigned int src_id);
int ar_hal_clk_src_adjust_freq(unsigned int src_id, unsigned int freq);
int ar_hal_clk_set_freq(unsigned int clk_id, unsigned int freq);
int ar_hal_clk_get_freq(unsigned int clk_id, unsigned int *freq);
int ar_hal_clk_get_info(unsigned int index, unsigned int *src_id, unsigned int *freq, char *name_buf, int name_len);
int ar_hal_pwr_ctrl_on(unsigned int pwr_id);
int ar_hal_pwr_ctrl_off(unsigned int pwr_id);
int ar_hal_pwr_state(unsigned int pwr_id, unsigned int *state);
int ar_hal_pwr_get_info(unsigned int index, unsigned int *pwr_id, unsigned int *status_bit, char *name_buf, int name_len);

/**
 * ar_hal_clk_round_freq - adjust a frequency to the exact frequency a clock can provide
 * @src_id: clock id
 * @freq: desired clock frequency in Hz
 * @round_freq: rounded clock frequency in Hz
 *
 * This answers the question "if I were to pass @freq to ar_hal_clk_set_freq(),
 * what clock rate would I end up with?" without changing the hardware
 * in any way.  In other words:
 *
 *   ar_hal_clk_round_freq(clk_id, freq, &round_freq);
 *
 * and:
 *
 *   ar_hal_clk_set_freq(clk_id, freq);
 *   ar_hal_clk_get_freq(clk_id, &round_freq);
 *
 * are equivalent except the former does not modify the clock hardware
 * in any way.
 *
 * Returns zero if no error, or negative errno.
 */
int ar_hal_clk_round_freq(unsigned int clk_id, unsigned int freq, unsigned int *round_freq);

/* similar to ar_hal_clk_round_freq */
int ar_hal_clk_src_round_freq(unsigned int src_id, unsigned int freq, unsigned int *round_freq);
int ar_hal_clk_src_get_freq(unsigned int src_id, unsigned int *freq);
int ar_hal_clk_src_get_info(unsigned int index, unsigned int *src_id, unsigned int *freq, char *name_buf, int name_len);

int ar_hal_rst_assert(unsigned int reset_id);
int ar_hal_rst_deassert(unsigned int reset_id);
int ar_hal_rst_state(unsigned int reset_id, unsigned int *state);
int ar_hal_rst_get_info(unsigned int index, unsigned int *rst_id, unsigned int *state, char *name_buf, int name_len);

#ifdef __cplusplus
#if __cplusplus
	}
#endif
#endif /* End of #ifdef __cplusplus */


#endif //__HAL_CLK_RPC_H__

