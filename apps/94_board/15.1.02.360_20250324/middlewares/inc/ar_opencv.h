#ifdef __cplusplus
extern "C" {
#endif

AR_VOID ArGetPerspectiveTransformMatrix(AR_S32 *srcXY, AR_S32 *dstXY, AR_FLOAT *outMatrix);
AR_VOID ArFindHomographyTransformMatrix(
	AR_S32 matches_cnt,
	AR_FLOAT *pf_irkpt_buf_x,
	AR_FLOAT *pf_irkpt_buf_y,
	AR_FLOAT *pf_viskpt_buf_x,
	AR_FLOAT *pf_viskpt_buf_y,
	AR_S16 *m0,
	AR_S16 *m1,
	AR_FLOAT *outMatrix
);
AR_VOID ArTransformMatrixFilter(
AR_FLOAT *inMatrix,
AR_FLOAT *OutMatrix,
AR_U32 frameId,
AR_S32 Width,
AR_S32 Height,
AR_S32 matchNum);

#ifdef __cplusplus
}
#endif

