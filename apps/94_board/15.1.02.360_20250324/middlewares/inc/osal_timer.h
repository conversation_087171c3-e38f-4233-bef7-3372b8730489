#ifndef __OSAL_TIMER_H__
#define __OSAL_TIMER_H__
#include <time.h>
#include <signal.h>

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* End of #ifdef __cplusplus */

typedef void *ar_timer_id_t;
typedef void(*time_func)(void *arg);

typedef struct {
    /* callback thread policy: 0: SCHED_OTHER, 1: SCHED_FIFO, 2: SCHED_RR */
    int cb_policy;

    /* callback thread priority: for SCHED_FIFO and SCHED_RR, 0-99, for SCHED_OTHER, meanless */
    int cb_priority;

    /* cpu mask: CPU affinity, one bit for one core, 1-15*/
    int cpu_mask;
} ar_timer_attr_t;

// Create timer with callback func
ar_timer_id_t ar_timer_create(time_func func, void *arg, ar_timer_attr_t *attr);

// Set timer interval(ms) and initial(ms), then start timer.
// If interval is 0, callback func will only run once, otherwise
// func will be executed in each interval.
int32_t ar_timer_start(ar_timer_id_t id, uint32_t interval, uint32_t initial);

// similar to ar_timer_start, but interval and initial are in denomination of us
int32_t ar_timer_start_us(ar_timer_id_t id, uint32_t interval, uint32_t initial);

// Stop timer
int32_t ar_timer_stop(ar_timer_id_t id);

// delete timer
int32_t ar_timer_delete(ar_timer_id_t id);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */

#endif //_AR_TIMER_H_
