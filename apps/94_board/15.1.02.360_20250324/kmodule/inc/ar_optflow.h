#ifndef __AR_OPTFLOW_H__
#define __AR_OPTFLOW_H__

typedef struct
{
	unsigned int corner_mode;
	unsigned int corner_harris_k_config;
	unsigned int corner_harris_k_custom_en;
	unsigned int corner_harris_k_custom_value;
	unsigned int corner_thresh_min;
	unsigned int corner_quality_level_enable; 	//not used
	unsigned int corner_quality_level_value;	//not used
	unsigned int corner_sobel_size;				//fix with default 0:3x3
	unsigned int corner_block_size;
	unsigned int corner_tile_loop_order;		//not used
	unsigned int corner_tile_size_norm_x;
	unsigned int corner_tile_size_norm_y;
	unsigned int corner_tile_norm_num_x;
	unsigned int corner_tile_norm_num_y;
	unsigned int corner_tile_size_last_x;
	unsigned int corner_tile_size_last_y;
} ar_corner_setting_st;

typedef struct
{
	unsigned int corner_width;
	unsigned int corner_height;
	unsigned int corner_output_mode;
	ar_corner_setting_st corner_setting;
	unsigned int corner_image_row_step;
	unsigned int corner_image_start_addr_phy;
	unsigned int corner_output_start_addr_phy;
	unsigned int corner_output_end_addr_phy;
	unsigned int corner_output_num;
	unsigned int corner_debug_ctrl;
} ar_corner_params_st;

typedef struct
{
    unsigned int pyramid_width;
	unsigned int pyramid_height;
	unsigned int pyramid_level;
	unsigned int pyramid_image_row_step0;
	unsigned int pyramid_image_row_step1;
	unsigned int pyramid_image_row_step2;
	unsigned int pyramid_image_row_step3;
	unsigned int pyramid_image_start_addr_phy0;
	void * pyramid_image_start_addr_virt0;
	unsigned int pyramid_image_start_addr_phy1;
	void * pyramid_image_start_addr_virt1;
	unsigned int pyramid_image_start_addr_phy2;
	void * pyramid_image_start_addr_virt2;
	unsigned int pyramid_image_start_addr_phy3;
	void * pyramid_image_start_addr_virt3;
	unsigned int pyramid_debug_ctrl;
} ar_pyramid_params_st;

typedef struct
{
	unsigned int klt_min_threshold_en; //not used
	unsigned int klt_min_threshold;	   //not used
	unsigned int klt_flags_use_initial_flow;
	unsigned int klt_flags_err_type;
	unsigned int klt_output_mode;
	unsigned int klt_max_residue;
	unsigned int klt_min_determinant;
	unsigned int klt_win_size;
	unsigned int klt_pyramid_level;
	unsigned int klt_hessian_mu;
	unsigned int klt_trace_scope_pyr3;
	unsigned int klt_trace_scope_pyr2;
	unsigned int klt_trace_scope_pyr1;
	unsigned int klt_trace_scope_pyr0;
	unsigned int klt_criteria_mode;
	unsigned int klt_criteria_epsilon_value;
	unsigned int klt_criteria_iterative_times;
} ar_klt_setting_st;

typedef struct
{
    unsigned int klt_width;
	unsigned int klt_height;
	ar_klt_setting_st klt_setting;
	unsigned int klt_pre_pyramid_start_addr_phy0;
	unsigned int klt_pre_pyramid_start_addr_phy1;
	unsigned int klt_pre_pyramid_start_addr_phy2;
	unsigned int klt_pre_pyramid_start_addr_phy3;
	unsigned int klt_cur_pyramid_start_addr_phy0;
	unsigned int klt_cur_pyramid_start_addr_phy1;
	unsigned int klt_cur_pyramid_start_addr_phy2;
	unsigned int klt_cur_pyramid_start_addr_phy3;
	unsigned int klt_pyramid_row_step0;
	unsigned int klt_pyramid_row_step1;
	unsigned int klt_pyramid_row_step2;
	unsigned int klt_pyramid_row_step3;
	unsigned int klt_pre_pst_start_addr_phy;
	unsigned int klt_cur_pst_start_addr_phy;
	unsigned int klt_input_point_num;
	unsigned int klt_output_point_num;
	unsigned int klt_debug_ctrl;
} ar_klt_params_st;

typedef struct
{
	ar_corner_params_st stCornerParam;
}ar_optflow_corner_ioctl_st;

typedef struct
{
	ar_pyramid_params_st stPyramidParam;
}ar_optflow_pyramid_ioctl_st;

typedef struct
{
	ar_klt_params_st stKltParam;
}ar_optflow_klt_ioctl_st;

#define OPTFLOW_IOC_MAGIC 'G'
#define OPTFLOW_IOC_CORNER_START 			_IOWR(OPTFLOW_IOC_MAGIC, 0, ar_optflow_corner_ioctl_st)
#define OPTFLOW_IOC_PYRAMID_START 			_IOWR(OPTFLOW_IOC_MAGIC, 1, ar_optflow_pyramid_ioctl_st)
#define OPTFLOW_IOC_KLT_START 				_IOWR(OPTFLOW_IOC_MAGIC, 2, ar_optflow_klt_ioctl_st)
#define OPTFLOW_IOC_SET_FREQUENCY  			_IOWR(OPTFLOW_IOC_MAGIC, 3, unsigned int)

#endif

