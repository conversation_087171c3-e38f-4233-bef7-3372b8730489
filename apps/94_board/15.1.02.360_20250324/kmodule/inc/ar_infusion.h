#ifndef __AR_INFUSION_H__
#define __AR_INFUSION_H__

#include "hal_type.h"

#define INFUSION_MAX_PANNELS 3

typedef enum{
    INFUSION_INTF_MODE_Y_2_YC  = 0B00,
    INFUSION_INTF_MODE_Y_2_Y   = 0B01,
    INFUSION_INTF_MODE_YC_2_YC = 0B10,
    INFUSION_INTF_MODE_NUM,
}ENUM_AR_INFUSION_INTF_MODE_T;

typedef enum{
    INFUSION_ALPHA_DST_VIS = 0,
    INFUSION_ALPHA_DST_INF = 1,
    INFUSION_ALPHA_DST_NUM,
}ENUM_AR_INFUSION_ALPHA_DST_T;

typedef enum{
    INFUSION_FRAME_TYPE_OUT = 0,
    INFUSION_FRAME_TYPE_INF = 1,
    INFUSION_FRAME_TYPE_VIS = 2,
    INFUSION_FRAME_TYPE_NUM,
}ENUM_AR_INFUSION_FRAME_TYPE_T;

typedef enum{
    INFUSION_FRAME_FMT_YU12 = 0,
    INFUSION_FRAME_FMT_NV12 = 1,
    INFUSION_FRAME_FMT_YU16 = 2,
    INFUSION_FRAME_FMT_NV16 = 3,
    INFUSION_FRAME_FMT_RAW8,
    INFUSION_FRAME_FMT_NUM,
}ENUM_AR_INFUSION_FRAME_FMT_T;

typedef struct
{
    AR_U8 vis_fixed_y_en;
    AR_U8 vis_fixed_cb_en;
    AR_U8 vis_fixed_cr_en;
    AR_U8 inf_fixed_y_en;
    AR_U8 inf_fixed_cb_en;
    AR_U8 inf_fixed_cr_en;
    AR_U8 vis_fixed_y;
    AR_U8 vis_fixed_cb;
    AR_U8 vis_fixed_cr;
    AR_U8 inf_fixed_y;
    AR_U8 inf_fixed_cb;
    AR_U8 inf_fixed_cr;
}STRU_AR_INFUSION_FIXED_T;

typedef struct
{
    ENUM_AR_INFUSION_INTF_MODE_T intf_mode;

    STRU_AR_INFUSION_FIXED_T color_fixed;
}STRU_AR_INFUSION_INTF_T;

#ifndef __KERNEL__
typedef struct
{
    AR_S32 inf_offset;
    AR_FLOAT inf_gain_f;
    AR_S32 inf_gain;
    AR_S32 vis_offset;
    AR_FLOAT vis_gain_f;
    AR_S32 vis_gain;

    // AR_U32 adjust_quantized;
}STRU_AR_INFUSION_ADJUST_T;
#endif

typedef struct
{
    AR_U32 color_lut_changed;
    AR_U32 color_lut[256];
}STRU_AR_INFUSION_COLOR_T;

#ifndef __KERNEL__
typedef struct
{
    AR_FLOAT alpha_auto_weight_a_f;
    AR_FLOAT alpha_auto_weight_b_f;
}STRU_AR_INFUSION_AUTO_WEIGHT_F_T;
#endif

typedef struct
{
    AR_U16 alpha_auto_weight_a;
    AR_U16 alpha_auto_weight_b;
}STRU_AR_INFUSION_AUTO_WEIGHT_T;

typedef struct
{
    AR_U32 alpha_power_x_sel;
    AR_U32 alpha_power_y_sel;
}STRU_AR_INFUSION_X_POWER_Y_SEL_T;

#ifndef __KERNEL__
typedef struct
{
    AR_FLOAT alpha_coef_y_f;
    AR_FLOAT alpha_coef_uv_f;
}STRU_AR_INFUSION_ALPHA_COEF_F_T;
#endif

typedef struct
{
    AR_U32 alpha_tl_x;
    AR_U32 alpha_tl_y;
    AR_U32 alpha_br_x;
    AR_U32 alpha_br_y;
}STRU_AR_INFUSION_ROI_T;

typedef struct
{
    AR_U16 alpha_coef_y;
    AR_U16 alpha_coef_uv;
}STRU_AR_INFUSION_ALPHA_COEF_T;

#ifndef __KERNEL__
typedef struct
{
    ENUM_AR_INFUSION_ALPHA_DST_T alpha_dst;
    STRU_AR_INFUSION_ROI_T alpha_roi;

    AR_U32 alpha_weight_mode;
    AR_U32 alpha_weight_sel;
    AR_U32 alpha_y_en;
    AR_U32 alpha_uv_en;

    STRU_AR_INFUSION_X_POWER_Y_SEL_T alpha_x_power_y_sel;
    STRU_AR_INFUSION_AUTO_WEIGHT_F_T alpha_auto_weight_f;
    STRU_AR_INFUSION_AUTO_WEIGHT_T alpha_auto_weight;

    STRU_AR_INFUSION_ALPHA_COEF_F_T alpha_coef_f;
    STRU_AR_INFUSION_ALPHA_COEF_T alpha_coef;
    AR_FLOAT alpha_lut_f[128];
    AR_U32 alpha_lut_changed;
    AR_U8 alpha_lut[128];

    // AR_U32 alpha_quantized;
}STRU_AR_INFUSION_ALPHA_T;
#endif

#ifndef __KERNEL__
typedef struct
{
    AR_U32 edge_filter0_abs_en;
    AR_U32 edge_filter1_abs_en;
    AR_U32 edge_filter_sel;

    AR_FLOAT filter_coef_f[18];
    AR_U32 filter_coef_changed;
    AR_S8 filter_coef[18];

    // AR_U32 filter_quantized;
}STRU_AR_INFUSION_FILTER_T;
#endif

#ifndef __KERNEL__
typedef struct
{
    AR_U32 edge_abs_en;
    AR_FLOAT edge_threshhold_f;
    AR_U32 edge_threshhold;
    AR_U32 beta_weight_sel;
    AR_FLOAT edge_beta_f;
    AR_U32 edge_beta;

    AR_FLOAT beta_lut_f[128];
    AR_U32 beta_lut_changed;
    AR_U8 beta_lut[128];

    // AR_U32 beta_quantized;
}STRU_AR_INFUSION_BETA_T;
#endif

#ifndef __KERNEL__
typedef struct
{
    AR_U32 edge_enhangce_enable;

    STRU_AR_INFUSION_FILTER_T filter;
    STRU_AR_INFUSION_BETA_T beta;
}STRU_AR_INFUSION_EDGE_T;
#endif

#ifndef __KERNEL__
typedef struct
{
    AR_U32 tuning_quantized;

    STRU_AR_INFUSION_INTF_T intf;
    STRU_AR_INFUSION_ADJUST_T adjust;
    STRU_AR_INFUSION_COLOR_T color;
    STRU_AR_INFUSION_ALPHA_T alpha;
    STRU_AR_INFUSION_EDGE_T edge;
}STRU_AR_INFUSION_TUNING_T;
#endif

typedef struct
{
    AR_S32 inf_offset;
    AR_S32 inf_gain;
    AR_S32 vis_offset;
    AR_S32 vis_gain;

    // AR_U32 adjust_quantized;
}STRU_AR_INFUSION_ADJUST_K_T;

typedef struct
{
    ENUM_AR_INFUSION_ALPHA_DST_T alpha_dst;
    STRU_AR_INFUSION_ROI_T alpha_roi;

    AR_U32 alpha_weight_mode;
    AR_U32 alpha_weight_sel;
    AR_U32 alpha_y_en;
    AR_U32 alpha_uv_en;

    STRU_AR_INFUSION_X_POWER_Y_SEL_T alpha_x_power_y_sel;
    STRU_AR_INFUSION_AUTO_WEIGHT_T alpha_auto_weight;

    STRU_AR_INFUSION_ALPHA_COEF_T alpha_coef;
    AR_U32 alpha_lut_changed;
    AR_U8 alpha_lut[128];

    // AR_U32 alpha_quantized;
}STRU_AR_INFUSION_ALPHA_K_T;

typedef struct
{
    AR_U32 edge_filter0_abs_en;
    AR_U32 edge_filter1_abs_en;
    AR_U32 edge_filter_sel;

    AR_U32 filter_coef_changed;
    AR_S8 filter_coef[18];

    // AR_U32 filter_quantized;
}STRU_AR_INFUSION_FILTER_K_T;

typedef struct
{
    AR_U32 edge_abs_en;
    AR_U32 edge_threshhold;
    AR_U32 beta_weight_sel;
    AR_U32 edge_beta;

    AR_U32 beta_lut_changed;
    AR_U8 beta_lut[128];

    // AR_U32 beta_quantized;
}STRU_AR_INFUSION_BETA_K_T;

typedef struct
{
    AR_U32 edge_enhangce_enable;

    STRU_AR_INFUSION_FILTER_K_T filter;
    STRU_AR_INFUSION_BETA_K_T beta;
}STRU_AR_INFUSION_EDGE_K_T;

typedef struct
{
    AR_U32 tuning_quantized;

    STRU_AR_INFUSION_INTF_T intf;
    STRU_AR_INFUSION_ADJUST_K_T adjust;
    STRU_AR_INFUSION_COLOR_T color;
    STRU_AR_INFUSION_ALPHA_K_T alpha;
    STRU_AR_INFUSION_EDGE_K_T edge;
}STRU_AR_INFUSION_TUNING_K_T;

typedef struct
{
    AR_U32 w;
    AR_U32 h;
    AR_U32 stride[INFUSION_MAX_PANNELS];
}STRU_AR_INFUSION_FRAME_SIZE_T;

typedef struct
{
    ENUM_AR_INFUSION_FRAME_FMT_T frame_format;
}STRU_AR_INFUSION_FRAME_FORMAT_T;

typedef struct
{
    AR_U32 phy_addr[INFUSION_MAX_PANNELS];
    AR_U64 virt_addr[INFUSION_MAX_PANNELS];
}STRU_AR_INFUSION_FRAME_ADDR_T;

typedef enum{
    INFUSION_LD_SRC_ISP,
    INFUSION_LD_SRC_VIF,
    INFUSION_LD_SRC_DP,
    INFUSION_LD_SRC_GDC,
    INFUSION_LD_SRC_NONE,
    INFUSION_LD_SRC_NUM,
}ENUM_AR_INFUSION_LD_SRC_T;

typedef enum
{
    INFUSION_PORT_MODE_OFFLINE,
    INFUSION_PORT_MODE_ONLINE,
    INFUSION_PORT_MODE_NUM,
}ENUM_AR_INFUSION_PORT_MODE_T;

typedef struct
{
    AR_U32 enable;
    ENUM_AR_INFUSION_LD_SRC_T src;
}STRU_AR_INFUSION_LD_PARAM_T;

typedef struct
{
    AR_U32 enable;
    AR_U32 align;
    AR_U32 lossy;
    AR_U32 ratio_to;
    AR_U32 header_phy_addr[INFUSION_MAX_PANNELS];
    AR_U64 header_virt_addr[INFUSION_MAX_PANNELS];
}STRU_AR_INFUSION_CF50_PARAM_T;

typedef struct
{
    ENUM_AR_INFUSION_PORT_MODE_T port_mode;

    STRU_AR_INFUSION_LD_PARAM_T ld_param;
    STRU_AR_INFUSION_CF50_PARAM_T cf50_param;

    STRU_AR_INFUSION_FRAME_ADDR_T frame_addr;
    STRU_AR_INFUSION_FRAME_FORMAT_T frame_format;
    STRU_AR_INFUSION_FRAME_SIZE_T frame_size;

    ENUM_AR_INFUSION_FRAME_TYPE_T type;
}STRU_AR_INFUSION_FRAME_INFO_T;

typedef struct
{
    ENUM_AR_INFUSION_ALPHA_DST_T alpha_dst;
    STRU_AR_INFUSION_ROI_T alpha_roi;

    AR_U32 alpha_weight_mode;
    AR_U32 alpha_weight_sel;
    AR_U32 alpha_y_en;
    AR_U32 alpha_uv_en;
}STRU_AR_INFUSION_ALPHA_MODE_T;

typedef struct
{
    AR_U32 edge_enhangce_enable;

    AR_U32 edge_abs_en;
    AR_U32 beta_weight_sel;

    AR_U32 edge_filter0_abs_en;
    AR_U32 edge_filter1_abs_en;
    AR_U32 edge_filter_sel;
}STRU_AR_INFUSION_EDGE_MODE_T;

typedef enum{
    INFUSION_WORK_MODE_00,
    INFUSION_WORK_MODE_01,
    INFUSION_WORK_MODE_02,
    INFUSION_WORK_MODE_03,
    INFUSION_WORK_MODE_04,
    INFUSION_WORK_MODE_05,
    INFUSION_WORK_MODE_06,
    INFUSION_WORK_MODE_07,
    INFUSION_WORK_MODE_08,
    INFUSION_WORK_MODE_09,
    INFUSION_WORK_MODE_10,
    INFUSION_WORK_MODE_11,
    INFUSION_WORK_MODE_12,
    INFUSION_WORK_MODE_13,
    INFUSION_WORK_MODE_14,
    INFUSION_WORK_MODE_15,
    INFUSION_WORK_MODE_16,
    INFUSION_WORK_MODE_17,
    INFUSION_WORK_MODE_18,
    INFUSION_WORK_MODE_19,
    INFUSION_WORK_MODE_20,
    INFUSION_WORK_MODE_21,
    INFUSION_WORK_MODE_22,
    INFUSION_WORK_MODE_23,
    INFUSION_WORK_MODE_NUM,
}ENUM_AR_INFUSION_WORK_MODE_T;

typedef enum{
    INFUSION_LD_MODE_NONE,
    INFUSION_LD_MODE_INF_IN,
    INFUSION_LD_MODE_VIS_IN,
    INFUSION_LD_MODE_ALL_IN,
    INFUSION_LD_MODE_OUT,
    INFUSION_LD_MODE_INF_IN_OUT,
    INFUSION_LD_MODE_VIS_IN_OUT,
    INFUSION_LD_MODE_ALL_IN_OUT,
    INFUSION_LD_MODE_NUM,
}ENUM_AR_INFUSION_LD_MODE_T;

typedef struct
{
    ENUM_AR_INFUSION_LD_MODE_T low_delay_mode;
    ENUM_AR_INFUSION_LD_SRC_T low_delay_inf_src;
    ENUM_AR_INFUSION_LD_SRC_T low_delay_vis_src;
}STRU_AR_INFUSION_WORK_MODE_T;

#ifndef __KERNEL__

typedef enum
{
    INFUSION_RPC_CALL_GET_PRESET_TUNING,
    INFUSION_RPC_CALL_OPEN,
    INFUSION_RPC_CALL_CLOSE,
    INFUSION_RPC_CALL_SET_FREQ,
    INFUSION_RPC_CALL_GET_FREQ,
    INFUSION_RPC_CALL_SET_FRAME,
    INFUSION_RPC_CALL_GET_FRAME,
    INFUSION_RPC_CALL_START,
    INFUSION_RPC_CALL_STOP,
    INFUSION_RPC_CALL_SET_TUNING,
    INFUSION_RPC_CALL_GET_TUNING,
    INFUSION_RPC_CALL_GET_FRAME_DONE,
    INFUSION_RPC_CALL_GET_WLINE_DONE,
}ENUM_AR_INFUSION_RPC_CALL_T;

typedef struct
{
    AR_S32 obj;
}STRU_AR_INFUSION_RPC_CALL_T;

typedef struct
{
    AR_S32 obj;
    AR_U32 freq;
}STRU_AR_INFUSION_RPC_CALL_FREQ_T;

typedef struct
{
    ENUM_AR_INFUSION_WORK_MODE_T obj;
    STRU_AR_INFUSION_TUNING_T stTuning;
}STRU_AR_INFUSION_RPC_CALL_PRESET_TUNING_T;

typedef struct
{
    AR_S32 obj;
    STRU_AR_INFUSION_TUNING_T stTuning;
}STRU_AR_INFUSION_RPC_CALL_TUNING_T;

typedef struct
{
    AR_S32 obj;
    STRU_AR_INFUSION_FRAME_INFO_T stFrameInfo;
}STRU_AR_INFUSION_RPC_CALL_FRAME_T;

typedef struct
{
    AR_S32 obj;
    AR_U32 done;
}STRU_AR_INFUSION_RPC_CALL_DONE_T;

typedef struct
{
    AR_S32 obj;
    STRU_AR_INFUSION_WORK_MODE_T stWorkMode;
}STRU_AR_INFUSION_RPC_CALL_MODE_T;

#endif


#define INFUSION_IOC_MAGIC 'f'

#define INFUSION_IOC_SET_FREQUENCY   (0)
#define INFUSION_IOC_SET_FRAME_INFO  (1)
#define INFUSION_IOC_GET_FRAME_INFO  (2)
#define INFUSION_IOC_START           (3)
#define INFUSION_IOC_STOP            (4)
#define INFUSION_IOC_IS_BUSY         (5)
#define INFUSION_IOC_SET_COLOR_TABLE (6)
#define INFUSION_IOC_SET_ALPHA_TABLE (7)
#define INFUSION_IOC_SET_BETA_TABLE  (8)
#define INFUSION_IOC_SET_FILTER_COEF (9)
#define INFUSION_IOC_SET_COLOR_EN    (10)
#define INFUSION_IOC_SET_ALPHA_EN    (11)
#define INFUSION_IOC_SET_BETA_EN     (12)
#define INFUSION_IOC_SET_FILTER_EN   (13)
#define INFUSION_IOC_SET_TUNING      (14)
#define INFUSION_IOC_GET_TUNING      (15)
#define INFUSION_IOC_SET_WORK_MODE   (16)
#define INFUSION_IOC_GET_FRAME_DONE  (17)
#define INFUSION_IOC_GET_WLINE_DONE  (18)

#define AR_INFUSION_IOC_SET_FREQUENCY   _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_FREQUENCY,   AR_U32)
#define AR_INFUSION_IOC_SET_FRAME       _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_FRAME_INFO,  STRU_AR_INFUSION_FRAME_INFO_T)
#define AR_INFUSION_IOC_GET_FRAME       _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_GET_FRAME_INFO,  STRU_AR_INFUSION_FRAME_INFO_T)
#define AR_INFUSION_IOC_START           _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_START,           STRU_AR_INFUSION_WORK_MODE_T)
#define AR_INFUSION_IOC_STOP            _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_STOP,            AR_U32)
#define AR_INFUSION_IOC_IS_BUSY         _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_IS_BUSY,         AR_U32)
#define AR_INFUSION_IOC_SET_COLOR_TABLE _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_COLOR_TABLE, void*)
#define AR_INFUSION_IOC_SET_ALPHA_TABLE _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_ALPHA_TABLE, void*)
#define AR_INFUSION_IOC_SET_BETA_TABLE  _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_BETA_TABLE,  void*)
#define AR_INFUSION_IOC_SET_FILTER_COEF _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_FILTER_COEF, void*)
#define AR_INFUSION_IOC_SET_COLOR_EN    _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_COLOR_EN,    AR_U32)
#define AR_INFUSION_IOC_SET_ALPHA_EN    _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_ALPHA_EN,    AR_U32)
#define AR_INFUSION_IOC_SET_BETA_EN     _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_BETA_EN,     AR_U32)
#define AR_INFUSION_IOC_SET_FILTER_EN   _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_FILTER_EN,   AR_U32)
#define AR_INFUSION_IOC_SET_TUNING      _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_TUNING,      STRU_AR_INFUSION_TUNING_K_T)
#define AR_INFUSION_IOC_GET_TUNING      _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_GET_TUNING,      STRU_AR_INFUSION_TUNING_K_T)
#define AR_INFUSION_IOC_SET_WORK_MODE   _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_SET_WORK_MODE,   STRU_AR_INFUSION_WORK_MODE_T)
#define AR_INFUSION_IOC_GET_FRAME_DONE  _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_GET_FRAME_DONE,  AR_U32)
#define AR_INFUSION_IOC_GET_WLINE_DONE  _IOWR(INFUSION_IOC_MAGIC, INFUSION_IOC_GET_WLINE_DONE,  AR_U32)

#endif

