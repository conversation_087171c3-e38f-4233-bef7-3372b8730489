#ifndef __AR_SYSCTL_DRV_H__
#define __AR_SYSCTL_DRV_H__

#include "osal_ioctl.h"

#define SYSCTL_MAX_PRIORITY  32

#define SYSCTL_NAME_MAX_LEN  32
#define AR_SYSCTL_IOC_MAGIC  'S'

typedef enum ar_sysctl_event
{
    AR_SYSCTL_EVENT_NONE,
    AR_SYSCTL_EVENT_FAST_SUSPEND, //用于一级休眠, 开启的模块进入休眠状态，系统保持原有状态
    AR_SYSCTL_EVENT_FAST_RESUME,
    AR_SYSCTL_EVENT_SUSPEND,      //用于二级休眠，DDR Retention方案
    AR_SYSCTL_EVENT_RESUME,
    AR_SYSCTL_EVENT_EXIT
}ENUM_AR_SYSCTL_EVENT;

typedef enum ar_sysctl_status
{
    SYSCTL_STATUS_RUNNING,
    SYSCTL_STATUS_SUSPEND,
    SYSCTL_STATUS_BUSY,
    SYSCTL_STATUS_ERROR
}ENUM_AR_SYSCTL_STATUS;

typedef struct
{
    char name[SYSCTL_NAME_MAX_LEN];
    unsigned int priority;
    ENUM_AR_SYSCTL_EVENT event;
}STRU_REGISTER_INFO;

#define IOC_SYSCTL_REGISTER        _IOW(AR_SYSCTL_IOC_MAGIC, 20, STRU_REGISTER_INFO)
#define IOC_SYSCTL_UNREGISTER      _IOW(AR_SYSCTL_IOC_MAGIC, 21, STRU_REGISTER_INFO)
#define IOC_SYSCTL_WAIT_EVENT      _IOWR(AR_SYSCTL_IOC_MAGIC, 22, STRU_REGISTER_INFO)
#define IOC_SYSCTL_EVENT_DONE      _IOWR(AR_SYSCTL_IOC_MAGIC, 23, STRU_REGISTER_INFO)
#define IOC_SYSCTL_SUSPEND         _IOW(AR_SYSCTL_IOC_MAGIC, 24, STRU_REGISTER_INFO)
#define IOC_SYSCTL_RESUME          _IOW(AR_SYSCTL_IOC_MAGIC, 25, int)
#define IOC_SYSCTL_QUERY_STATUS    _IOWR(AR_SYSCTL_IOC_MAGIC, 26, int)

#endif
