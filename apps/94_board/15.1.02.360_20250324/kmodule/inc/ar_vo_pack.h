#ifndef __AR_VO_PACK_H__
#define __AR_VO_PACK_H__

#define AR_VO_PACK_DEV_PATH "/dev/ar_vo_pack"

typedef enum
{
    ENUM_AR_VO_PACK_IRQ_TYPE_INTERVAL_LINE      = 1 << 0,
    ENUM_AR_VO_PACK_IRQ_TYPE_FIX_LINE           = 1 << 1,
    ENUM_AR_VO_PACK_IRQ_TYPE_WRITE_BACK         = 1 << 2,
    ENUM_AR_VO_PACK_IRQ_TYPE_WB_LOAD            = 1 << 3,
    ENUM_AR_VO_PACK_IRQ_TYPE_DE_DMA_FRAME_DONE  = 1 << 4,
    ENUM_AR_VO_PACK_IRQ_TYPE_VSYNC_START        = 1 << 5,
}AR_VO_PACK_IRQ_TYPE;

/** 中断订阅信息 */
struct AR_VO_PACK_SUB_INFO_S
{
    int                   dev_id;
    AR_VO_PACK_IRQ_TYPE   irq_type;   /**< 中断类型, 可用或命令组合多个中断类型    */
    unsigned long long    irq_time_ns; /**< 中断时间    */
    int                   block_id;    /**< 中断的附加信息*/
    int                   sub_event;
};

struct AR_VO_SUB_EVENT_INFO_S
{
    int                   dev_id;
    int                   sub_event;
};

#define AR_VO_PACK_IOC_MAGIC 'P'
#define AR_VO_PACK_IOC_REG_IRQ          _IOWR(AR_VO_PACK_IOC_MAGIC, 0, unsigned int)
#define AR_VO_PACK_IOC_UNREG_IRQ        _IOWR(AR_VO_PACK_IOC_MAGIC, 1, unsigned int)
#define AR_VO_PACK_IOC_SUB_CLEAR        _IOWR(AR_VO_PACK_IOC_MAGIC, 2, unsigned int)
#define AR_VO_PACK_IOC_GET_SUB_INFO     _IOWR(AR_VO_PACK_IOC_MAGIC, 3, struct AR_VO_PACK_SUB_INFO_S)
#define AR_VO_PACK_IOC_UPDATE_SUB_EVENT _IOWR(AR_VO_PACK_IOC_MAGIC, 4, struct AR_VO_SUB_EVENT_INFO_S)

#endif

