#ifndef __AR_MVSCALER_H__
#define __AR_MVSCALER_H__

//#include <linux/miscdevice.h>

/* Only one single memory region can be handled each time.
*   src_phy_addr -----------------------
*                |                     |
*                |                     |
*                |    crop:[x,y]       |
*                |     ----w---        |
*                |     |      |        |         dst_phy_addr
*                |    h|      |----------------> ---------------
*                |     |      |        |         |             |
*                |     --------        |         |             |
*                |                     |         ---------------
*                |                     |
*                |---------------------|
*
*/

#define MVSCALER_OUTPUT_NUM    4

typedef struct
{
	uint32_t phy_addr;
    uint32_t width;
    uint32_t height;
    uint32_t stride;
} ar_mvscaler_st;

typedef struct
{
    int32_t  index;  //mvscaler index 0/1
    ar_mvscaler_st src_params;
    uint32_t crop_x;
    uint32_t crop_y;
    uint32_t crop_w;
    uint32_t crop_h;
    ar_mvscaler_st dst_params[MVSCALER_OUTPUT_NUM];
    uint32_t output_num; //	(1 - 4)
    uint32_t img_format; // 0:single planar; 1:UV packet
} ar_mvscaler_params_st;


#define AR_MVSCALER_IOC_MAGIC 'M'
#define AR_MVSCALER_IOC_START   _IOWR(AR_MVSCALER_IOC_MAGIC, 0, ar_mvscaler_params_st)

#endif
