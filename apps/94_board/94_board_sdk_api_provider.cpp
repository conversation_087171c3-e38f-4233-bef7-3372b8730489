#define NRPLUGIN // in order to use NR defined types
#define XR_BSP_API __attribute__((visibility("default")))

#include <heron/interface/device_api/nr_extra_dp.h>
#include <heron/interface/device_api/nr_extra_display.h>
#include <heron/interface/device_api/nr_extra_gdc.h>
#include <board_context/board_context.h>
#include <94_board.h>
#include <util/util.h>
#include <log.h>

#include <mpi_vi.h>
#include <mpi_vb.h>
#include <mpi_sys.h>

#include <memory>
#include <queue>

bool LoadBoardSymbols();

extern PFN_AR_MPI_GDC_ADV_Query pfn_AR_MPI_GDC_ADV_Query;
extern PFN_AR_MPI_GDC_ADV_Process pfn_AR_MPI_GDC_ADV_Process;
extern PFN_AR_MPI_GDC_ADV_Config pfn_AR_MPI_GDC_ADV_Config;
extern PFN_AR_MPI_GDC_ADV_Start pfn_AR_MPI_GDC_ADV_Start;
extern PFN_AR_MPI_GDC_ADV_Stop pfn_AR_MPI_GDC_ADV_Stop;
extern PFN_AR_MPI_GDC_ADV_Config_CPUBp_Line pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line;
extern PFN_AR_MPI_GDC_ADV_Get_FrmPts pfn_AR_MPI_GDC_ADV_Get_FrmPts;

extern PFN_AR_MPI_VB_CreatePool pfn_AR_MPI_VB_CreatePool;
extern PFN_AR_MPI_VB_GetBlock pfn_AR_MPI_VB_GetBlock;
extern PFN_AR_MPI_VB_ReleaseBlock pfn_AR_MPI_VB_ReleaseBlock;
extern PFN_AR_MPI_VB_Handle2PoolId pfn_AR_MPI_VB_Handle2PoolId;
extern PFN_AR_MPI_VB_Handle2PhysAddr pfn_AR_MPI_VB_Handle2PhysAddr;

extern PFN_ar_hal_sys_mmz_alloc pfn_ar_hal_sys_mmz_alloc;
extern PFN_ar_hal_sys_mmz_alloc_cached pfn_ar_hal_sys_mmz_alloc_cached;
extern PFN_ar_hal_sys_mmz_flush_cache pfn_ar_hal_sys_mmz_flush_cache;
extern PFN_ar_hal_sys_mmz_free pfn_ar_hal_sys_mmz_free;
extern PFN_AR_MPI_VI_GetChnFrame pfn_AR_MPI_VI_GetChnFrame;
extern PFN_AR_MPI_VI_ReleaseChnFrame pfn_AR_MPI_VI_ReleaseChnFrame;

extern PFN_AR_MPI_SYS_Mmap pfn_AR_MPI_SYS_Mmap;

extern PFN_AR_MPI_VO_EnableLineBuffer pfn_AR_MPI_VO_EnableLineBuffer;
extern PFN_AR_MPI_VO_DisableLineBuffer pfn_AR_MPI_VO_DisableLineBuffer;
extern PFN_AR_MPI_VO_ResetLineBufferPrs pfn_AR_MPI_VO_ResetLineBufferPrs;

typedef AR_S32 (*VO_SUBSCRIBE_FUNC_T)(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info);

static std::shared_ptr<app::BoardContext> s_board_context;

extern "C"
{
    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Process(AR_GDC_ADV_TRANSFORM_S *pstParam)
    {
        return pfn_AR_MPI_GDC_ADV_Process(pstParam);
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Config(AR_GDC_ADV_TRANSFORM_S *pstParam)
    {
        return pfn_AR_MPI_GDC_ADV_Config(pstParam);
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Config_CPUBp_Line(AR_GDC_ADV_LINE_PARAMS_S *pstLineParam)
    {
        return pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line(pstLineParam);
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Get_FrmPts(AR_GDC_ADV_FRMPTS_PARAMS_S *pstGdcFrmPtsParams)
    {
        return pfn_AR_MPI_GDC_ADV_Get_FrmPts(pstGdcFrmPtsParams);
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Query(AR_GDC_ADV_TRANSFORM_S *pstParam)
    {
        return pfn_AR_MPI_GDC_ADV_Query(pstParam);
    }

    XR_BSP_API AR_S32 XR_AR_MPI_GDC_ADV_Start(AR_GDC_ADV_TRANSFORM_S *pstParam)
    {
        return pfn_AR_MPI_GDC_ADV_Start(pstParam);
    }

    XR_BSP_API AR_S32 XR_ar_hal_sys_mmz_alloc(AR_U64 *pu64_phy_addr, AR_VOID **p_vir_addr,
                                              const AR_CHAR *pstr_mmb, const AR_CHAR *pstr_zone, AR_U32 u32_len)
    {
        BOARD_LOG_INFO("calling {}:{}", __FUNCTION__, pstr_mmb);
        return pfn_ar_hal_sys_mmz_alloc(pu64_phy_addr, p_vir_addr, pstr_mmb, pstr_zone, u32_len);
    }

    XR_BSP_API AR_S32 XR_ar_hal_sys_mmz_alloc_cached(AR_U64 *pu64_phy_addr, AR_VOID **p_vir_addr,
                                                     const AR_CHAR *pstr_mmb, const AR_CHAR *pstr_zone, AR_U32 u32_len)
    {
        BOARD_LOG_INFO("calling {}:{}", __FUNCTION__, pstr_mmb);
        return pfn_ar_hal_sys_mmz_alloc_cached(pu64_phy_addr, p_vir_addr, pstr_mmb, pstr_zone, u32_len);
    }

    XR_BSP_API AR_S32 XR_ar_hal_sys_mmz_flush_cache(AR_U64 u64_phy_addr, AR_VOID *p_vir_addr, AR_U32 u32_size)
    {
        // BOARD_LOG_INFO("calling {} size:{}", __FUNCTION__, u32_size);
        return pfn_ar_hal_sys_mmz_flush_cache(u64_phy_addr, p_vir_addr, u32_size);
    }
    XR_BSP_API AR_S32 XR_ar_hal_sys_mmz_free(AR_U64 u64_phy_addr, AR_VOID *p_vir_addr)
    {
        return pfn_ar_hal_sys_mmz_free(u64_phy_addr, p_vir_addr);
    }

    XR_BSP_API NRResult NRLoadAR94APIs(bool log_all_level)
    {
        Logger::GetInstance()->SetLogAllLevel(log_all_level);
        if (!LoadBoardSymbols())
            return NR_RESULT_FAILURE;
        return NR_RESULT_SUCCESS;
    }

    static char *s_vo_mask_data[2] = {nullptr, nullptr};
    XR_BSP_API NRResult NRInitBoardContext()
    {
        s_board_context = std::make_shared<app::BoardContext>();
        GenVOMask(s_vo_mask_data);
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRReleaseBoardContext()
    {
        s_board_context->DeInit();
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDisplayConfigService(const NRDisplayConfig *display_config)
    {
        // s_board_context->RegisterDPUCallback(reinterpret_cast<VO_SUBSCRIBE_FUNC_T>(display_config->callback));
        /**
         * 创建VI,VO
         * 初始化相关寄存器
         * 注：global_board_context是测试Heron Plugin核心功能的helper
         * 其中逻辑不属于Heron Plugin
         */
        if (!s_board_context->Init())
        {
            BOARD_LOG_ERROR("board context init failed.");
            return NR_RESULT_FAILURE;
        }
        s_board_context->Start();
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDpGetFrame(NRDpFrameData *nr_dp_frame, uint32_t timeout_ms)
    {
        VIDEO_FRAME_INFO_S *pstFrameInfo = new VIDEO_FRAME_INFO_S();
        AR_S32 ret = pfn_AR_MPI_VI_GetChnFrame(0, 0, pstFrameInfo, timeout_ms);
        if (AR_SUCCESS != ret)
        {
            BOARD_LOG_ERROR("AR_MPI_VI_GetChnFrame error: {:x}", ret);
            return NR_RESULT_FAILURE;
        }
        nr_dp_frame->ar_frame_handle = (uint64_t)(pstFrameInfo);
        nr_dp_frame->data.x = pstFrameInfo->stVFrame.u64VirAddr[0];
        nr_dp_frame->data.y = pstFrameInfo->stVFrame.u64VirAddr[1];
        nr_dp_frame->data.z = pstFrameInfo->stVFrame.u64VirAddr[2];
        nr_dp_frame->data_ext.x = pstFrameInfo->stVFrame.u64PhyAddr[0];
        nr_dp_frame->data_ext.y = pstFrameInfo->stVFrame.u64PhyAddr[1];
        nr_dp_frame->data_ext.z = pstFrameInfo->stVFrame.u64PhyAddr[2];
        nr_dp_frame->frame_id = pstFrameInfo->stVFrame.u32FrameId;
        nr_dp_frame->width = pstFrameInfo->stVFrame.u32Width;
        nr_dp_frame->height = pstFrameInfo->stVFrame.u32Height;
        nr_dp_frame->strides.x = pstFrameInfo->stVFrame.u32Stride[0];
        nr_dp_frame->strides.y = pstFrameInfo->stVFrame.u32Stride[1];
        nr_dp_frame->strides.z = pstFrameInfo->stVFrame.u32Stride[2];
        nr_dp_frame->pts = pstFrameInfo->stVFrame.u64PTS;
        nr_dp_frame->pixel_format = NR_FRAME_BUFFER_FORMAT_YUV420_PLANAR;
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDpReleaseFrame(const void *frame_info)
    {
        const VIDEO_FRAME_INFO_S *handle = (const VIDEO_FRAME_INFO_S *)(((NRDpFrameData *)frame_info)->ar_frame_handle);
        AR_S32 ret = pfn_AR_MPI_VI_ReleaseChnFrame(0, 0, handle);
        delete (handle);
        if (AR_SUCCESS != ret)
        {
            BOARD_LOG_ERROR("AR_MPI_VI_ReleaseChnFrame error: {:x}", ret);
            return NR_RESULT_FAILURE;
        }
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDisplayStartOSDRender(NRDisplayUsage display_usage, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height)
    {
        uint32_t vo_id = display_usage == NR_DISPLAY_USAGE_LEFT ? 1 : 0;
        if (!s_board_context->EnableOverlay(vo_id, 0, 0, 1920, 1080))
        {
            BOARD_LOG_ERROR("StartOSDRender Error.");
            return NR_RESULT_FAILURE;
        }
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalDisplayStopOSDRender()
    {
        if (!s_board_context->DisableOverlay())
        {
            BOARD_LOG_ERROR("StopOSDRender Error.");
            return NR_RESULT_FAILURE;
        }
        return NR_RESULT_SUCCESS;
    }

    static VB_POOL_CONFIG_S s_stVbPoolCfg;
    static VIDEO_FRAME_INFO_S s_overlay_vb_frame_info[2];
    static VB_POOL s_vb_pool_id;
    static VB_BLK s_hBlkHdl[2];

    static bool PrepareVBPool(uint32_t u32Size)
    {
        memset(&s_stVbPoolCfg, 0, sizeof(VB_POOL_CONFIG_S));
        s_stVbPoolCfg.u64BlkSize = u32Size;
        s_stVbPoolCfg.u32BlkCnt = 3;
        s_stVbPoolCfg.enRemapMode = VB_REMAP_MODE_NONE;
        s_vb_pool_id = pfn_AR_MPI_VB_CreatePool(&s_stVbPoolCfg);
        if (s_vb_pool_id == VB_INVALID_POOLID)
        {
            BOARD_LOG_ERROR("Maybe you not call sys init");
            return false;
        }
        return true;
    }
    static bool s_vb_pool_prepared = false;
    static uint32_t s_overlay_frame_alloc_count = 0; // its just an example
    /// @brief
    /// 创建VIDEO_FRAME_INFO_S实例并分配VB内存块,将虚拟地址信息赋值给NROverlayFrameData实例
    /// \n根据入参中frame->width,frame->height分配VB内存
    /// \n air-like:
    /// \n light:
    /// \n gina: new
    /// @param[in][out] frame
    /// @return 结果
    XR_BSP_API NRResult NRLocalDisplayAllocOverlayFrame(NROverlayFrameData *overlay_frame_info)
    {
        if (s_overlay_frame_alloc_count >= 2)
        { // its just an example, should not implement like this
            BOARD_LOG_ERROR("exceeds maximum overlay frame alloc count: {}", 2);
            return NR_RESULT_FAILURE;
        }
        uint32_t width = 1920;
        uint32_t height = 1080;
        uint32_t stride = AR_ALIGN128(width);
        uint32_t u32Size = stride * height * 2;
        if (!s_vb_pool_prepared)
        {
            if (PrepareVBPool(u32Size))
            {
                s_vb_pool_prepared = true;
            }
            else
            {
                BOARD_LOG_ERROR("{} PrepareVBPool error", __FUNCTION__);
                return NR_RESULT_FAILURE;
            }
        }
        uint32_t i = s_overlay_frame_alloc_count;
        s_hBlkHdl[i] = pfn_AR_MPI_VB_GetBlock(s_vb_pool_id, u32Size, NULL);
        if (s_hBlkHdl[i] == VB_INVALID_HANDLE)
        {
            BOARD_LOG_ERROR("[VOU_MST_File2VO] get vb fail!!! {}", i);
            return NR_RESULT_FAILURE;
        }
        BOARD_LOG_INFO("overlay VB block handle {}: {}, size: {}", i, s_hBlkHdl[i], u32Size);

        s_overlay_vb_frame_info[i].stVFrame.enField = VIDEO_FIELD_INTERLACED;
        s_overlay_vb_frame_info[i].stVFrame.enCompressMode = COMPRESS_MODE_NONE;
        s_overlay_vb_frame_info[i].stVFrame.enPixelFormat = PIXEL_FORMAT_ARGB_4444;
        s_overlay_vb_frame_info[i].stVFrame.enVideoFormat = VIDEO_FORMAT_LINEAR;
        s_overlay_vb_frame_info[i].stVFrame.enColorGamut = COLOR_GAMUT_BT709;
        s_overlay_vb_frame_info[i].stVFrame.u32Width = width;
        s_overlay_vb_frame_info[i].stVFrame.u32Height = height;
        s_overlay_vb_frame_info[i].stVFrame.u32Stride[0] = AR_ALIGN128(width * 2);
        s_overlay_vb_frame_info[i].stVFrame.u32Stride[1] = 0;
        s_overlay_vb_frame_info[i].stVFrame.u32Stride[2] = 0;
        s_overlay_vb_frame_info[i].stVFrame.u32Stride[3] = 0;

        s_overlay_vb_frame_info[i].stVFrame.u32TimeRef = 0;
        s_overlay_vb_frame_info[i].stVFrame.u64PTS = 0;
        s_overlay_vb_frame_info[i].stVFrame.enDynamicRange = DYNAMIC_RANGE_SDR8;

        s_overlay_vb_frame_info[i].u32PoolId = pfn_AR_MPI_VB_Handle2PoolId(s_hBlkHdl[i]);
        s_overlay_vb_frame_info[i].stVFrame.u64PhyAddr[0] = pfn_AR_MPI_VB_Handle2PhysAddr(s_hBlkHdl[i]);

        s_overlay_vb_frame_info[i].stVFrame.u64VirAddr[0] = (AR_U64)pfn_AR_MPI_SYS_Mmap(s_overlay_vb_frame_info[i].stVFrame.u64PhyAddr[0], u32Size);
        memcpy((void *)(s_overlay_vb_frame_info[i].stVFrame.u64VirAddr[0]), s_vo_mask_data[i], 1920 * 1080 * 2);            // data size is different than u32Size

        overlay_frame_info->width = width;
        overlay_frame_info->height = height;
        // overlay_frame_info->data_data = (const char *)(s_overlay_vb_frame_info[i].stVFrame.u64VirAddr[0]);
        char *data_data = new char[u32Size];
        overlay_frame_info->data_data = (const char *)data_data;
        overlay_frame_info->data_size = u32Size;
        overlay_frame_info->ar_frame_handle = (uint64_t)(&s_overlay_vb_frame_info[i]);
        BOARD_LOG_INFO("ar_frame_handle {}: {}", i, (void*)(&s_overlay_vb_frame_info[i]));

        s_overlay_frame_alloc_count++;

        return NR_RESULT_SUCCESS;
    }

    /// @brief 释放与NROverlayFrameData以及相应VIDEO_FRAME_INFO_S实例绑定的VB Memory
    /// \n air-like:
    /// \n light:
    /// \n gina: new
    /// @param[in] frame
    /// @return 结果

    XR_BSP_API NRResult NRLocalDisplayDeallocOverlayFrame(const NROverlayFrameData *overlay_frame_info)
    {
        return NR_RESULT_SUCCESS;
    }

    /// @brief 提交绘制完成的NROverlayFrameData到DPU的Overlay层
    /// \n air-like:
    /// \n light:
    /// \n gina: new
    /// @param[in] frame
    /// @return 结果

    XR_BSP_API NRResult NRLocalDisplaySendOverlayFrame(NRDisplayUsage display_usage, const NROverlayFrameData *overlay_frame_info)
    {
        AR_S32 overlay_0_id = display_usage == NR_DISPLAY_USAGE_LEFT ? VO_LAYER_ID_OVERLAY_1_0 : VO_LAYER_ID_OVERLAY_0_0;
        if (!s_board_context->SendFrameToOverlay(overlay_0_id, (void *)(&s_overlay_vb_frame_info[display_usage])))
        {
            BOARD_LOG_ERROR("SendFrameToOverlay:{} error", overlay_0_id);
            return NR_RESULT_FAILURE;
        }
        return NR_RESULT_SUCCESS;
    }

    static AR_GDC_ADV_TRANSFORM_S s_gdc_config[2];
    XR_BSP_API NRResult NRLocalGdcInit(const NRGdcInitConfig *config)
    {
        BOARD_LOG_INFO("{} using local NRLocalGdcInit", config->display_usage == NR_DISPLAY_USAGE_LEFT ? "left" : "right");
        /************************* 直接写死的配置*********************/
        memset((void *)(&s_gdc_config[config->display_usage]), 0, sizeof(AR_GDC_ADV_TRANSFORM_S));
        s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtCtrl.u8BackPixelCtl = 1; // use padding color when coord w < 0
        // test boarder padding color. black in YUV420
        s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtCtrl.u8CplxPadCtl = 7; // 配置borader color 通道使能。7对应二进制0111,使能3个channel

        s_gdc_config[config->display_usage].stOutBuffer.u32Width = 1920;  // 由眼镜屏幕width决定，这里直接写死
        s_gdc_config[config->display_usage].stOutBuffer.u32Height = 1080; // 同上面的width

        // lowdelay stOutBuffer就是DPU,stride 必须为4096
        s_gdc_config[config->display_usage].stOutBuffer.astChannels[0].u32Stride = 4096;
        s_gdc_config[config->display_usage].stOutBuffer.astChannels[1].u32Stride = 4096;
        s_gdc_config[config->display_usage].stOutBuffer.astChannels[2].u32Stride = 4096;

        // GDC计算通道使能flag
        s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC0Enable = 1;
        s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC1Enable = 1;
        s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC2Enable = 1;

        // 因为yuv420p的uv宽高分量是y分量的一半，所以设置uv通道的downscaler将其宽高下采样2倍
        s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC0Downscaler = 0;
        s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC1Downscaler = 1;
        s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC2Downscaler = 1;

        s_gdc_config[config->display_usage].s32NonBlock = true; // MPI_GDC_ADV_Config方法不阻塞，直接返回
        s_gdc_config[config->display_usage].u8TaskMode = 1;     // 0:normal 1:over-write

        /**
         * 控制GDC是否通过lowdelay硬连线向后端送出已写出的dst_pic行数信息（只控制gdc是否送出此信号，后端display模块是否使用，要看display的具体配置）。
         * out_bp多用于gdc向ddr写数据，display需要知道ddr中已存在的pic行数的应用；或者需要gdc在计算完指定行时，精确启动display的场景。
         */
        s_gdc_config[config->display_usage].stGdcParam.stLdCfg.stOutBp.u8OutBpEn = 1;
        s_gdc_config[config->display_usage].stGdcParam.stStartCfg.u8SafetyStart = 1; // safety_start

        /**
         *配置GDC-->DPU Lowdelay 连接模式下的寄存器
         */
        s_gdc_config[config->display_usage].u8LdEn = 1;
        s_gdc_config[config->display_usage].stLdParam.enLdMode = AR_GDC_LD_MODE_OUT;                                           // Lowdelay 模式必须这样配
        s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.enLowdelayMode = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER; // Lowdelay 模式必须这样配
        s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32PlanarNum = 3;                                          // yuv420
        s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.enFormat = PIXEL_FORMAT_YVU_PLANAR_420;                    // 目前酷芯sdk中YUV420此值为:23
        s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[0] = AR_ALIGN128(1920);
        s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[1] = AR_ALIGN128(960);
        s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[2] = AR_ALIGN128(960);

        // 每个weight单元复用几次,我们用1
        s_gdc_config[config->display_usage].stGdcParam.stWeightCfg.stWeightMode.u32WeightFlush = 1;

        // XXX apb matrix值仅当stWarpCfg.stWarpMode.u32WarpMode = 1时生效
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[0] = 0x3f800000; // 1.0
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[1] = 0;
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[2] = 0;
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[3] = 0;
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[4] = 0x3f800000; // 1.0
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[5] = 0;
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[6] = 0;
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[7] = 0;
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[8] = 0xC0400000; // -1.0

        /************************* 使用入参中的配置值*********************/
        s_gdc_config[config->display_usage].s32CoreId = config->display_usage == NR_DISPLAY_USAGE_LEFT ? 1 : 0;
        s_gdc_config[config->display_usage].stGdcParam.stStartCfg.u8StartMode = config->start_mode; // start_mode: 0 normal; 1 auto; 2 shadow; 3 free run

        s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Lines64Enable = config->lines64_enable;

        s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding0.u16C0 = config->padding_color.x; // GDC采样超出原图区域的颜色(通道1)
        s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding0.u16C1 = config->padding_color.y; // GDC采样超出原图区域的颜色(通道2)
        s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding1.u16C2 = config->padding_color.z; // GDC采样超出原图区域的颜色(通道3)

        // 0: use warp.dat file; 1 use apb matrix; 2 disable。我们用0
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = config->warp_mode;
        // 每个3x3warp矩阵复用几次。每行用同一个矩阵则矩阵一帧共35个矩阵，warp_flush_cnt = 61
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt = config->warp_flush_cnt;
        // 0:32x32 blocking; 3:disable。我们用0
        s_gdc_config[config->display_usage].stGdcParam.stMeshCfg.u32MeshMode = config->mesh_mode;
        // mesh每行1920/32=61个点,每个点两个float,61x8bytes=488
        s_gdc_config[config->display_usage].stGdcParam.stMeshCfg.u32MeshStride = config->mesh_stride;
        // 0:inner bi-linear; 1: weight.dat file;我们用1
        s_gdc_config[config->display_usage].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode = config->weight_mode;
        // warp矩阵list的MMZ起始地址
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32WarpAddr = (uint32_t)((uint64_t)config->metadata.warp_data_data & 0xffffffff);
        s_gdc_config[config->display_usage].stGdcParam.stMeshCfg.u32MeshAddr = (uint32_t)((uint64_t)config->metadata.mesh_data_data & 0xffffffff);
        s_gdc_config[config->display_usage].stGdcParam.stWeightCfg.u32WeightAddr = (uint32_t)((uint64_t)config->metadata.weight_data_data & 0xffffffff);

        // s_gdc_config[config->display_usage].stGdcParam.stAxiCfg.u8AxiCache = 0x0F;

        return NR_RESULT_SUCCESS;
    }

    static uint32_t s_gdc_process_count = 0;
    static NRResult GdcProcessInternal(const NRGdcFrameConfig *config, bool need_reset, bool discard_on_busy)
    {
        s_gdc_process_count++;
        if (s_gdc_process_count % 300 == 0)
        {
            BOARD_LOG_INFO("{} using local GDCProcess", config->display_usage == NR_DISPLAY_USAGE_LEFT ? "left" : "right");
        }
        AR_S32 ret = -1;
        uint8_t frame_start = config->frame_start;
        if (need_reset)
        {
            BOARD_LOG_WARN("resetting linebuffer for id: {}", s_gdc_config[config->display_usage].s32CoreId);
            frame_start = 1;
            if (!pfn_AR_MPI_VO_DisableLineBuffer || !pfn_AR_MPI_VO_EnableLineBuffer || !pfn_AR_MPI_VO_ResetLineBufferPrs)
            {
                BOARD_LOG_WARN("board system doesn't support linebuffer reset");
                need_reset = false;
            }
            else
            {
                /* 1. disable gdc */
                ret = pfn_AR_MPI_GDC_ADV_Stop(s_gdc_config[config->display_usage].s32CoreId);
                if (ret < 0)
                {
                    BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Stop fail: {}", ret);
                }

                /* 2. disablevo lb */
                ret = pfn_AR_MPI_VO_DisableLineBuffer(s_gdc_config[config->display_usage].s32CoreId);
                if (ret < 0)
                {
                    BOARD_LOG_ERROR("AR_MPI_VO_DisableLineBuffer fail: {}", ret);
                }

                /* 3. enable vo lb */
                ret = pfn_AR_MPI_VO_EnableLineBuffer(s_gdc_config[config->display_usage].s32CoreId);
                if (ret < 0)
                {
                    BOARD_LOG_ERROR("AR_MPI_VO_EnableLineBuffer fail: {}", ret);
                }
            }
        }

        s_gdc_config[config->display_usage].stGdcParam.stMeshCfg.u32MeshAddr = (uint32_t)((uint64_t)config->metadata.mesh_data_data & 0xffffffff);

        s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding0.u16C0 = config->padding_color.x; // GDC采样超出原图区域的颜色(通道1)
        s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding0.u16C1 = config->padding_color.y; // GDC采样超出原图区域的颜色(通道2)
        s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding1.u16C2 = config->padding_color.z; // GDC采样超出原图区域的颜色(通道3)

        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode = config->warp_mode;
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[0] = config->apb_matrix[0];
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[1] = config->apb_matrix[1];
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[2] = config->apb_matrix[2];
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[3] = config->apb_matrix[3];
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[4] = config->apb_matrix[4];
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[5] = config->apb_matrix[5];
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[6] = config->apb_matrix[6];
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[7] = config->apb_matrix[7];
        s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[8] = config->apb_matrix[8];
        s_gdc_config[config->display_usage].stGdcParam.stStartCfg.u8FrameStart = frame_start; // start working(auto clean)
        s_gdc_config[config->display_usage].stInBuffer.u32IsVB = config->in_buffer.is_vb;
        s_gdc_config[config->display_usage].stInBuffer.u32Width = config->in_buffer.width;
        s_gdc_config[config->display_usage].stInBuffer.u32Height = config->in_buffer.height;

        s_gdc_config[config->display_usage].stInBuffer.astChannels[0].u32Stride = config->in_buffer.strides.x;
        s_gdc_config[config->display_usage].stInBuffer.astChannels[1].u32Stride = config->in_buffer.strides.y;
        s_gdc_config[config->display_usage].stInBuffer.astChannels[2].u32Stride = config->in_buffer.strides.z;

        // VI获取的Frame每个Chanel的物理地址和虚拟地址, 直接赋值透传给GDC的相应寄存器即可
        s_gdc_config[config->display_usage].stInBuffer.astChannels[0].uptrAddrVirt = config->in_buffer.data.x;
        s_gdc_config[config->display_usage].stInBuffer.astChannels[1].uptrAddrVirt = config->in_buffer.data.y;
        s_gdc_config[config->display_usage].stInBuffer.astChannels[2].uptrAddrVirt = config->in_buffer.data.z;
        s_gdc_config[config->display_usage].stInBuffer.astChannels[0].u32AddrPhy = config->in_buffer.data_ext.x;
        s_gdc_config[config->display_usage].stInBuffer.astChannels[1].u32AddrPhy = config->in_buffer.data_ext.y;
        s_gdc_config[config->display_usage].stInBuffer.astChannels[2].u32AddrPhy = config->in_buffer.data_ext.z;
        if (!discard_on_busy)
        {
            ret = pfn_AR_MPI_GDC_ADV_Process(&s_gdc_config[config->display_usage]);
            if (AR_SUCCESS != ret)
            {
                BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Process error: {}", ret);
                return NR_RESULT_FAILURE;
            }
        }
        else
        {
            ret = pfn_AR_MPI_GDC_ADV_Config(&s_gdc_config[config->display_usage]);
            if (AR_SUCCESS != ret)
            {
                BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Config error: {}", ret);
                return NR_RESULT_FAILURE;
            }

            ret = pfn_AR_MPI_GDC_ADV_Start(&s_gdc_config[config->display_usage]);
            if (AR_SUCCESS != ret)
            {
                BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Start error: {}", ret);
                return NR_RESULT_FAILURE;
            }
        }
        if (need_reset)
        {
            ret = pfn_AR_MPI_VO_ResetLineBufferPrs(s_gdc_config[config->display_usage].s32CoreId);
            if (ret < 0)
            {
                BOARD_LOG_ERROR("AR_MPI_VO_ResetLineBufferPrs fail: {}", ret);
            }
            BOARD_LOG_WARN("reset linebuffer for id: {} done", s_gdc_config[config->display_usage].s32CoreId);
        }
        return NR_RESULT_SUCCESS;
    }

    XR_BSP_API NRResult NRLocalGdcProcess(const NRGdcFrameConfig *config, bool need_reset)
    {
        return GdcProcessInternal(config, need_reset, false);
    }

    XR_BSP_API NRResult NRLocalGdcProcessDiscardOnBusy(const NRGdcFrameConfig *config, bool need_reset)
    {
        return GdcProcessInternal(config, need_reset, true);
    }
    /// @}
}