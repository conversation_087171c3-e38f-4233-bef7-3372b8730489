#ifndef BOARD_CONTEXT
#define BOARD_CONTEXT

#include <memory>
#include <mutex>
#include <thread>

#include <board_context/board_config.h>
#include <pipeline/src_provider.h>
#include <pipeline/result_presenter.h>

namespace app
{

	/**
	 * @brief [BoardContext 类]是对AR94系统API的封装
	 * 参考‘/base/arprj/apps/mpp_sample/ar_dp/dptest.c’
	 * 以及‘/base/arprj/apps/mpp_sample/sample_gdc/sample_gdc_transform.cpp’ 等
	 * AR94 SDK示例文件之后，对API进行分类，封装
	 */
	class BoardContext
	{
	public:
		BoardContext() {}
		~BoardContext() {}
		/*
		 * 1. 初始化vb,开辟94SDK内部管理的mpp内存
		 * 2. 配置vi(dp input)所有寄存器
		 * 3. 配置vo(dpu)初irq以外的所有寄存器
		 * 4. 配置gdc mesh/weight/warp 模式相关寄存器
		 */
		void RegisterDPUCallback(AR_S32(*vo_subscribe_call_back)(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info));
		bool Init();
		bool DeInit();
		bool Start();
		void Stop();

        bool EnableOverlay(uint32_t vo_id, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height);
        bool DisableOverlay();
        bool SendFrameToOverlay(AR_S32 overlay_0_id, void* overlay_frame_info);
		AR94::BoardConfig &GetBoardConfig() { return board_config_; }

	protected:
		/* 负责控制和配置GDC输入图像格式，图像来源，输入Metadata等
		 * 具体的：
		 * ComponentVIDP负责从DP读取图像，存到内存中，并通知xreal_processor实例
		 * ComponentVIFile负责从板子主存上读取文件，存到内存中，并通知xreal_processor[TODO]
		 */
		std::shared_ptr<SrcProvider> src_provider_;
		/**
		 * 负责控制GDC计算结果的储存和展示[TODO]
		 */
		std::shared_ptr<ResultPresenter> result_presenter_;
		/**
		 * 所有 板子 相关的 状态 变量
		 * 包含：
		 * VI，VO，VB，Frame，GDC等
		 */
		AR94::BoardConfig board_config_;

	private:
		/*初始化板子其他参数*/
		void initOtherConfig();

		/**
		 * 根据debug_config_中的配置，创建相应类型的components
		 * 其中包含
		 * src_provider_, gdc_controller_ 和result_presenter_
		 */
		bool genPipelineComponents();
	};

}
#endif