#include <94_board.h>
#include <board_context/board_config.h>
#include <log.h>

extern ISP_SNS_OBJ_S *p_obj;
extern SRTU_SENSOR_DEFAULT_ATTR_T default_attr;

extern PFN_AR_MPI_VIN_GetSensorObj pfn_AR_MPI_VIN_GetSensorObj;
extern PFN_AR_MPI_VIN_CloseSensorObj pfn_AR_MPI_VIN_CloseSensorObj;

extern PFN_AR_MPI_VI_DisableDev pfn_AR_MPI_VI_DisableDev;
extern PFN_AR_MPI_VI_DisableChn pfn_AR_MPI_VI_DisableChn;
extern PFN_AR_MPI_VI_StopPipe pfn_AR_MPI_VI_StopPipe;
extern PFN_AR_MPI_VI_DestroyPipe pfn_AR_MPI_VI_DestroyPipe;
extern PFN_AR_MPI_VI_ReleaseChnFrame pfn_AR_MPI_VI_ReleaseChnFrame;

extern PFN_AR_MPI_ISP_Exit pfn_AR_MPI_ISP_Exit;

extern PFN_AR_MPI_VO_Disable pfn_AR_MPI_VO_Disable;
extern PFN_AR_MPI_VO_DisableVideoLayer pfn_AR_MPI_VO_DisableVideoLayer;

namespace AR94
{

    // 获取板子默认的关键config状态
    bool GetCurrentBoardConfig(BoardConfig &config)
    {
        /**
         * XREAL_QUESTION 1: stSnsdp_txObj 结构体的讲解
         ==》
            typedef struct arISP_SNS_OBJ_S
            {
                AR_S32  (*pfnRegisterCallback)(VI_PIPE ViPipe, ALG_LIB_S *pstAeLib, ALG_LIB_S *pstAwbLib);//向vin驱动之策sensor回调函数和aec回调函数
                AR_S32  (*pfnUnRegisterCallback)(VI_PIPE ViPipe, ALG_LIB_S *pstAeLib, ALG_LIB_S *pstAwbLib);//向vin驱动注销sensor回调函数和aec回调函数
                AR_S32  (*pfnSetBusInfo)(VI_PIPE ViPipe, ISP_SNS_COMMBUS_U unSNSBusInfo); //向sensor驱动程序设置i2c id
                AR_VOID (*pfnStandby)(VI_PIPE ViPipe); //不使用
                AR_VOID (*pfnRestart)(VI_PIPE ViPipe); //不使用
                AR_VOID (*pfnMirrorFlip)(VI_PIPE ViPipe, ISP_SNS_MIRRORFLIP_TYPE_E eSnsMirrorFlip); //不使用
                AR_S32  (*pfnWriteReg)(VI_PIPE ViPipe, AR_S32 s32Addr, AR_S32 s32Data); //不使用
                AR_S32  (*pfnReadReg)(VI_PIPE ViPipe, AR_S32 s32Addr); //不使用
                AR_S32  (*pfnSetInit)(VI_PIPE ViPipe, ISP_INIT_ATTR_S *pstInitAttr);	 //不使用
                AR_S32  (*pfnSetFocus)(VI_PIPE ViPipe, AR_S32 focus);   //不使用
                AR_S32  (*pfnGetDefaultAttr)(AR_U8 u8SnsMode,SRTU_SENSOR_DEFAULT_ATTR_T *pstDefaultAttr); //<通过sensor 模式获取到默认的属性参数，
                u8SnsMode 的值和ISP_PUB_ATTR_S  中的u8SnsMode一一对应，驱动实现的时候注意配合，获取到的属性用户针对需求进行修改，然后设置.
            } ISP_SNS_OBJ_S;
         */
        // p_obj = &stSnsDprxObj;
        const AR_CHAR *sensor_name = "dp_rx";
        void *handle = pfn_AR_MPI_VIN_GetSensorObj((AR_CHAR *)sensor_name, &p_obj);
        if (!handle || !p_obj)
        {
            BOARD_LOG_ERROR("no {} driver , do nothing !!!", sensor_name);
            if (handle)
            {
                pfn_AR_MPI_VIN_CloseSensorObj(handle);
            }
            return 0;
        }

        if (p_obj->pfnGetDefaultAttr)
        {
            /**
             * XREAL_QUESTION 2: sensor_mode的讲解
                ===》主要用来获取sensor 驱动程序（也就是dprx）的默认属性，dp驱动程序提供了两种模式，0x80 表示1920x1080， 0x81表示3840x1080
             * 2.1 哪个Sensor
                ===》指的是dp，vin 把一切输入源都抽象成一个sensor。
             * 2.2 0x80具体对应哪个mode？
                ===》1920x1080p 模式
             */

            /**
            * XREAL_QUESTION 3:
            * 结构体：
            * typedef struct
            {
               STRU_COMBO_DEV_ATTR_T stComboAttr;
               VI_DEV_ATTR_S stDevAttr;
               VI_PIPE_ATTR_S stPipeAttr;
               ISP_PUB_ATTR_S stPubAttr;
               VI_CHN_ATTR_S  stChnAttr;
            }SRTU_SENSOR_DEFAULT_ATTR_T;
            * 3.1 “SRTU”代表什么？
            ==》这是artosyn的命名规则，结构体以STRU 开头
            * 3.2 是否有对应着“DP_IN“不经过DDR，直通数据给GDC得模式？
            ==》不支持linebuffer到gdc
            * 3.3 请提供一个直通模式的Demo
            ==》不支持linebuffer 到gdc
            */
            p_obj->pfnGetDefaultAttr(config.sensor_mode, &default_attr);
            config.stSize = default_attr.stPubAttr.stSnsSize;
            config.stSize_ch = default_attr.stChnAttr.stSize;
        }
        else
        {
            BOARD_LOG_ERROR("pfnGetDefaultAttr is null, exit the test");
            return false;
        }
        /**
         * @brief Xreal needed configs
         *
         */
        config.oled_dp_time_offset = 0;
        return true;
    }

    // 在DPin-DPout模式下，优雅地结束任务
    void BoardConfig::Stop(int entrance)
    {
        AR_S32 s32Ret = AR_SUCCESS;
        if (entrance >= 6)
        {
            s32Ret = pfn_AR_MPI_VI_ReleaseChnFrame(ViPipe, ViChn, &frame);
            if (AR_SUCCESS != s32Ret)
            {
                BOARD_LOG_ERROR("vi release frame failed. s32Ret: {:x}", s32Ret);
            }
        }
        if (entrance >= 5)
        {
            s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer[1]);
            if (AR_SUCCESS != s32Ret)
            {
                BOARD_LOG_ERROR("vo disable layer1 failed. s32Ret: {:x}", s32Ret);
            }
            s32Ret = pfn_AR_MPI_VO_DisableVideoLayer(VoLayer[0]);
            if (AR_SUCCESS != s32Ret)
            {
                BOARD_LOG_ERROR("vo disable layer0 failed. s32Ret: {:x}", s32Ret);
            }
        }
        if (entrance >= 4)
        {
            s32Ret = pfn_AR_MPI_VO_Disable(VoDev[1]);
            if (AR_SUCCESS != s32Ret)
            {
                BOARD_LOG_ERROR("vo1 disable failed. s32Ret: {:x}", s32Ret);
            }
            s32Ret = pfn_AR_MPI_VO_Disable(VoDev[0]);
            if (AR_SUCCESS != s32Ret)
            {
                BOARD_LOG_ERROR("vo0 disable failed. s32Ret: {:x}", s32Ret);
            }
        }
        if (entrance >= 3)
        {
            pfn_AR_MPI_ISP_Exit(ViPipe);
        }
        if (entrance >= 1)
        {
            pfn_AR_MPI_VI_DisableChn(ViPipe, ViChn);
            pfn_AR_MPI_VI_StopPipe(ViPipe);
            pfn_AR_MPI_VI_DestroyPipe(ViPipe);
            pfn_AR_MPI_VI_DisableDev(ViDev);
        }
    }

}