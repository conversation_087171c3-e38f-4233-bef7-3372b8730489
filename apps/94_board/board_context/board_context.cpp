#include <94_board.h>

#include <board_context/board_context.h>
#include <pipeline/vi_dp.h>
#include <pipeline/vo_dp.h>

#include <log.h>

extern PFN_AR_MPI_SYS_Exit pfn_AR_MPI_SYS_Exit;
extern PFN_AR_MPI_VB_Exit pfn_AR_MPI_VB_Exit;
extern PFN_AR_MPI_VB_ExitModCommPool pfn_AR_MPI_VB_ExitModCommPool;


extern PFN_AR_MPI_VO_EnableChn pfn_AR_MPI_VO_EnableChn;
extern PFN_AR_MPI_VO_DisableChn pfn_AR_MPI_VO_DisableChn;
extern PFN_AR_MPI_VO_SetChnAttr pfn_AR_MPI_VO_SetChnAttr;
extern PFN_AR_MPI_VO_EnableVideoLayer pfn_AR_MPI_VO_EnableVideoLayer;
extern PFN_AR_MPI_VO_DisableVideoLayer pfn_AR_MPI_VO_DisableVideoLayer;
extern PFN_AR_MPI_VO_SetVideoLayerAttr pfn_AR_MPI_VO_SetVideoLayerAttr;
extern PFN_AR_MPI_VO_SetVideoLayerPos pfn_AR_MPI_VO_SetVideoLayerPos;
extern PFN_AR_MPI_VO_SendFrame pfn_AR_MPI_VO_SendFrame;

static AR_VOID SAMPLE_COMM_SYS_Exit(void)
{
    pfn_AR_MPI_SYS_Exit();
    pfn_AR_MPI_VB_ExitModCommPool(VB_UID_VDEC);
    pfn_AR_MPI_VB_Exit();
    return;
}

namespace app
{

    void BoardContext::RegisterDPUCallback(AR_S32 (*vo_subscribe_call_back)(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info))
    {
        board_config_.vo_subscribe_call_back = vo_subscribe_call_back;
    }

    bool BoardContext::Init()
    {
        BOARD_LOG_TRACE("BoardContext INITIALIZING!");
        bool result = false;
        do
        {
            if (!AR94::GetCurrentBoardConfig(board_config_))
                break;
            if (!genPipelineComponents())
                break;
            if (!src_provider_->Init(board_config_))
                break;
            if (!result_presenter_->Init(board_config_))
                break;
            result = true;
        } while (0);
        initOtherConfig();
        BOARD_LOG_TRACE("BoardContext INITIALIZED!");
        return result;
    }
    bool BoardContext::DeInit()
    {
        BOARD_LOG_TRACE("BoardContext DEINITIALIZING!");
        SAMPLE_COMM_SYS_Exit();
        BOARD_LOG_TRACE("BoardContext DEINITIALIZED!");
        return true;
    }

    void BoardContext::initOtherConfig()
    {
        board_config_.oled_dp_time_offset = 1;
    }

    bool BoardContext::genPipelineComponents()
    {
        BOARD_LOG_TRACE("BoardContext generating PipelineComponents.");
        src_provider_ = std::make_shared<VIDP>();
        result_presenter_ = std::make_shared<VODP>();
        return true;
    }

    bool BoardContext::Start()
    {
        BOARD_LOG_INFO("BoardContext STARTING!");
        bool result = false;
        do
        {
            if (!src_provider_->Start(board_config_))
                break;
            if (!result_presenter_->Start(board_config_))
                break;
            result = true;
        } while (0);
        BOARD_LOG_INFO("BoardContext STARTED!");
        return result;
    }

    void BoardContext::Stop()
    {
        BOARD_LOG_INFO("BoardContext STOPPIING!");
        result_presenter_->Stop(board_config_);
        src_provider_->Stop(board_config_);
        SAMPLE_COMM_SYS_Exit();
        BOARD_LOG_INFO("BoardContext STOPPED!");
    }

    bool BoardContext::EnableOverlay(uint32_t vo_id, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height)
    {
        BOARD_LOG_INFO("BoardContext EnableOverlay{} {}x{} at ({},{})!", vo_id, width, height, start_x, start_y);
        board_config_.stOverLayer0DevSize.u32Width = width;
        board_config_.stOverLayer0DevSize.u32Height = height;
        board_config_.stOverLayer0Attr[vo_id].bClusterMode = AR_FALSE;
        board_config_.stOverLayer0Attr[vo_id].bDoubleFrame = AR_FALSE;
        board_config_.stOverLayer0Attr[vo_id].enDstDynamicRange = DYNAMIC_RANGE_SDR8;
        board_config_.stOverLayer0Attr[vo_id].stDispRect.s32X = 0;
        board_config_.stOverLayer0Attr[vo_id].stDispRect.s32Y = 0;
        board_config_.stOverLayer0Attr[vo_id].stDispRect.u32Height = board_config_.stOverLayer0DevSize.u32Height;
        board_config_.stOverLayer0Attr[vo_id].stDispRect.u32Width = board_config_.stOverLayer0DevSize.u32Width;
        board_config_.stOverLayer0Attr[vo_id].stImageSize.u32Height = board_config_.stOverLayer0DevSize.u32Height;
        board_config_.stOverLayer0Attr[vo_id].stImageSize.u32Width = board_config_.stOverLayer0DevSize.u32Width;
        board_config_.stOverLayer0Attr[vo_id].enPixFormat = PIXEL_FORMAT_ARGB_4444;
        board_config_.stOverLayer0Attr[vo_id].u32DispFrmRt = 30;
        board_config_.stOverLayer0Attr[vo_id].u32Stride[0] = AR_ALIGN128(board_config_.stOverLayer0DevSize.u32Width * 2);
        board_config_.stOverLayer0Attr[vo_id].u32Stride[1] = 0;
        board_config_.stOverLayer0Attr[vo_id].u32Stride[2] = 0;
        board_config_.stOverLayer0Attr[vo_id].u32Stride[3] = 0;

        board_config_.stOverLayer0Attr[vo_id].memMode = VO_MEMORY_MODE_LOW;

        BOARD_LOG_INFO("BoardContext EnableOverlay 0");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerAttr(board_config_.VoOverLayer0[vo_id], &board_config_.stOverLayer0Attr[vo_id]), "AR_MPI_VO_SetVideoLayerAttr");

        POINT_S stOverlay0Pos = {0};
        stOverlay0Pos.s32X = start_x;
        stOverlay0Pos.s32Y = start_y;
        BOARD_LOG_INFO("BoardContext EnableOverlay 1");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerPos(board_config_.VoOverLayer0[vo_id], &stOverlay0Pos), "AR_MPI_VO_SetVideoLayerPos");

        /* ENABLE VO OVERLAYER0 */
        BOARD_LOG_INFO("BoardContext EnableOverlay 2");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableVideoLayer(board_config_.VoOverLayer0[vo_id]), "AR_MPI_VO_EnableVideoLayer");

        /* SET AND ENABLE VO CHN */
        memset(&board_config_.stOverlay0ChnAttr[vo_id], 0, sizeof(VO_CHN_ATTR_S));
        board_config_.stOverlay0ChnAttr[vo_id].bDeflicker = AR_FALSE;
        board_config_.stOverlay0ChnAttr[vo_id].u32Priority = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.s32X = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.s32Y = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.u32Height = board_config_.stOverLayer0DevSize.u32Height;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.u32Width = board_config_.stOverLayer0DevSize.u32Width;

        BOARD_LOG_INFO("BoardContext EnableOverlay 3");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetChnAttr(board_config_.VoOverLayer0[vo_id], 0, &board_config_.stOverlay0ChnAttr[vo_id]), "AR_MPI_VO_SetChnAttr");
        BOARD_LOG_INFO("BoardContext EnableOverlay 4");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableChn(board_config_.VoOverLayer0[vo_id], 0), "AR_MPI_VO_EnableChn");
        BOARD_LOG_INFO("VODP{} overlay0 enabled.", vo_id);

        return true;
    }

    bool BoardContext::DisableOverlay()
    {
        BOARD_LOG_INFO("BoardContext DisableOverlay!");
        for (int i = 0; i < 2; i++)
        {
            AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_DisableChn(board_config_.VoOverLayer0[i], 0), "AR_MPI_VO_DisableChn");
            AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_DisableVideoLayer(board_config_.VoOverLayer0[i]), "AR_MPI_VO_DisableVideoLayer");
            BOARD_LOG_INFO("VODP{} overlay0 disabled.", i);
        }
        return true;
    }

    bool BoardContext::SendFrameToOverlay(AR_S32 overlay_0_id, void *overlay_frame_info)
    {
        VIDEO_FRAME_INFO_S* frame_info = (VIDEO_FRAME_INFO_S *)overlay_frame_info;
        BOARD_LOG_INFO("BoardContext SendFrameToOverlay! overlay_0_id: {} width: {} height: {} format: {} frame_info_handle: {}",
            overlay_0_id, frame_info->stVFrame.u32Width, frame_info->stVFrame.u32Height, frame_info->stVFrame.enPixelFormat, overlay_frame_info);
        if (AR_SUCCESS != pfn_AR_MPI_VO_SendFrame(overlay_0_id, 0, (VIDEO_FRAME_INFO_S *)overlay_frame_info, 0))
            return false;
        return true;
    }
}
