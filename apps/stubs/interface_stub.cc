#define NRPLUGIN // in order to use NR defined types

#include <heron/interface/include/common/nr_plugin_generic.h>
#include <heron/interface/include/common/nr_plugin_hmd.h>
#include <heron/interface/include/common/nr_plugin_file.h>
#include <heron/interface/include/common/nr_plugin_device_message.h>
#include <heron/interface/include/flinger/nr_plugin_flinger.h>
#include <heron/interface/include/misc/nr_plugin_misc.h>
#include <heron/interface/include/osd/nr_plugin_device.h>
#include <heron/interface/include/flinger/nr_plugin_space_screen.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>

#include <framework/util/util.h>

static NRInterface *GetInterface(NRInterfaceGUID guid, unsigned long long *out_interface_size);
static NRPluginResult RegisterLifecycleProvider(const char *plugin_name, const char *id, const NRPluginLifecycleProvider *provider, uint32_t provider_size);
static NRPluginResult RegisterProvider(NRPluginHandle handle, const NRFlingerProvider *provider, uint32_t provider_size);
static NRPluginResult RegisterSpaceScreenProvider(NRPluginHandle handle, const NRSpaceScreenProvider *provider, uint32_t provider_size);
static NRPluginResult GetHeadPose(NRPluginHandle handle, NRPerceptionType perception_type, uint64_t hmd_time_nanos, NRTrackingPoseType type, NRTransform *head_pose);
static NRPluginResult BWDecodeBuffer(NRPluginHandle, NRImageFormat, uint32_t input_buffer_size, const char *input_buffer_data, uint32_t *output_buffer_size, char *output_buffer_data);
static NRPluginResult RgbCameraIsOccupied(NRPluginHandle, bool *occupied);

static NRPluginResult GetGlobalConfig(NRPluginHandle handle, const char **data, uint32_t *size);
static NRPluginResult GetDeviceConfig(NRPluginHandle handle, const char **data, uint32_t *size);
static NRPluginResult GetAppDefaultConfig(NRPluginHandle handle, const char **data, uint32_t *size);

static NRPluginResult GetCanvasDepth(NRPluginHandle handle, float *out_depth);
static NRPluginResult GetSpaceMode(NRPluginHandle handle, NRSpaceMode *out_space_mode);

static NRPluginResult RegisterFileProvider(NRPluginHandle handle, const NRFileProvider *provider, uint32_t provider_size);
static NRPluginResult GetUserConfigDirectory(NRPluginHandle handle, const char **data, uint32_t *size);

static NRPluginResult RegisterMiscProvider(NRPluginHandle handle, const NRMiscProvider *provider, uint32_t provider_size);

static NRPluginResult RegisterDeviceMessageProvider(NRPluginHandle handle, const NRDeviceMessageHandleProvider *provider, uint32_t provider_size);

extern NRPluginResult GetComponentPoseFromHead(NRPluginHandle handle, NRComponent component, NRTransform *out_transform);
extern NRPluginResult GetComponentFov(NRPluginHandle handle, NRComponent component, NRFov4f *out_fov);
extern NRPluginResult GetComponentDisplayDistortionSize(NRPluginHandle handle, NRComponent component, NRSize2i *distortion_size);
extern NRPluginResult GetComponentDisplayDistortionData(NRPluginHandle handle, NRComponent component, int size, float *data);

/// global variables
NRInterfaces g_interfaces{&GetInterface};
NRPluginLifecycleProvider g_flinger_lifecycle_provider{};
NRFlingerProvider g_flinger_provider{};
NRFileProvider g_file_provider{};
NRSpaceScreenProvider g_space_screen_provider{};
NRMiscProvider g_misc_provider{};
NRDeviceMessageHandleProvider g_device_message_provider{};

/// static variables
static NRGenericInterface s_generic_interface{};
static NRFlingerInterface s_flinger_interface{};
static NRSpaceScreenInterface s_space_screen_interface{};
static NRHMDInterface s_hmd_interface{};
static NRFileInterface s_file_interface{};
static NRMiscInterface s_misc_interface{};
static NRDeviceInterface s_device_interface{};
static NRDeviceMessageSendInterface s_device_message_send_interface{};

// should not use framework log before lifecycle_provider.Register()
NRInterface *GetInterface(NRInterfaceGUID guid, unsigned long long *out_interface_size)
{
    if (guid == GetNRInterfaceGUID<NRGenericInterface>())
    {
        printf("[INTERFACE_STUB] get generic interface\n");
        s_generic_interface.GetGlobalConfig = GetGlobalConfig;
        s_generic_interface.GetDeviceConfig = GetDeviceConfig;
        s_generic_interface.GetAppDefaultConfig = GetAppDefaultConfig;
        *out_interface_size = sizeof(s_generic_interface);
        return &s_generic_interface;
    }
    else if (guid == GetNRInterfaceGUID<NRFlingerInterface>())
    {
        printf("[INTERFACE_STUB] get flinger interface\n");
        s_flinger_interface.RegisterLifecycleProvider = RegisterLifecycleProvider;
        s_flinger_interface.RegisterProvider = RegisterProvider;
        s_flinger_interface.RegisterSpaceScreenProvider = RegisterSpaceScreenProvider;
        s_flinger_interface.GetHeadPose = GetHeadPose;
        s_flinger_interface.BWDecodeBuffer = BWDecodeBuffer;
        s_flinger_interface.RgbCameraIsOccupied = RgbCameraIsOccupied;
        *out_interface_size = sizeof(s_flinger_interface);
        return &s_flinger_interface;
    }
    else if (guid == GetNRInterfaceGUID<NRSpaceScreenInterface>())
    {
        printf("[INTERFACE_STUB] get space_screen interface\n");
        s_space_screen_interface.GetCanvasDepth = GetCanvasDepth;
        s_space_screen_interface.GetSpaceMode = GetSpaceMode;
        *out_interface_size = sizeof(s_space_screen_interface);
        return &s_space_screen_interface;
    }
    else if (guid == GetNRInterfaceGUID<NRHMDInterface>())
    {
        printf("[INTERFACE_STUB] get hmd interface\n");
        s_hmd_interface.GetComponentPoseFromHead = GetComponentPoseFromHead;
        s_hmd_interface.GetComponentFov = GetComponentFov;
        s_hmd_interface.GetComponentDisplayDistortionSize = GetComponentDisplayDistortionSize;
        s_hmd_interface.GetComponentDisplayDistortionData = GetComponentDisplayDistortionData;
        *out_interface_size = sizeof(s_hmd_interface);
        return &s_hmd_interface;
    }
    else if (guid == GetNRInterfaceGUID<NRFileInterface>())
    {
        printf("[INTERFACE_STUB] get file interface\n");
        s_file_interface.RegisterProvider = RegisterFileProvider;
        s_file_interface.GetUserConfigDirectory = GetUserConfigDirectory;
        *out_interface_size = sizeof(s_file_interface);
        return &s_file_interface;
    }
    else if (guid == GetNRInterfaceGUID<NRMiscInterface>())
    {
        printf("[INTERFACE_STUB] get misc interface\n");
        s_misc_interface.RegisterProvider = RegisterMiscProvider;
        *out_interface_size = sizeof(s_misc_interface);
        return &s_misc_interface;
    }
    else if (guid == GetNRInterfaceGUID<NRDeviceInterface>())
    {
        printf("[INTERFACE_STUB] get device interface\n");
        *out_interface_size = sizeof(s_device_interface);
        return &s_device_interface;
    }
    else if (guid == GetNRInterfaceGUID<NRDeviceMessageSendInterface>())
    {
        printf("[INTERFACE_STUB] get device_message_send interface\n");
        s_device_message_send_interface.RegisterProvider = RegisterDeviceMessageProvider;
        *out_interface_size = sizeof(s_device_message_send_interface);
        return &s_device_message_send_interface;
    }
    else
    {
        printf("[INTERFACE_STUB] invalid guid: %llx, %llx\n", guid.high_value_, guid.low_value_);
    }
    return NULL;
}

NRPluginResult RegisterLifecycleProvider(const char *plugin_id, const char *plugin_version, const NRPluginLifecycleProvider *provider, uint32_t provider_size)
{
    printf("calling stub %s pluginName %s, plugin_version = %s provider_size: %u\n", __func__, plugin_id, plugin_version, provider_size);
    if (provider == NULL || provider_size != sizeof(NRPluginLifecycleProvider))
    {
        return NR_PLUGIN_RESULT_FAILURE;
    }
    void *p = (void *)(&g_flinger_lifecycle_provider);
    memcpy(p, provider, provider_size);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult RegisterProvider(NRPluginHandle /*handle*/, const NRFlingerProvider *provider, uint32_t provider_size)
{
    printf("calling stub %s provider ptr: %p size: %u\n", __func__, provider, provider_size);
    if (provider == NULL || provider_size != sizeof(NRFlingerProvider))
    {
        return NR_PLUGIN_RESULT_FAILURE;
    }
    void *p = (void *)(&g_flinger_provider);
    memcpy(p, provider, provider_size);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult RegisterFileProvider(NRPluginHandle /*handle*/, const NRFileProvider *provider, uint32_t provider_size)
{
    printf("calling stub %s provider ptr: %p size: %u\n", __func__, provider, provider_size);
    if (provider == NULL || provider_size != sizeof(NRFileProvider))
    {
        return NR_PLUGIN_RESULT_FAILURE;
    }
    void *p = (void *)(&g_file_provider);
    memcpy(p, provider, provider_size);
    return NR_PLUGIN_RESULT_SUCCESS;
}

static std::string user_config_dir = ".";
NRPluginResult GetUserConfigDirectory(NRPluginHandle /*handle*/, const char **data, uint32_t *size)
{
    HERON_LOG_INFO("calling stub {}", __func__);
    *data = user_config_dir.data();
    *size = user_config_dir.size();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult RegisterSpaceScreenProvider(NRPluginHandle /*handle*/, const NRSpaceScreenProvider *provider, uint32_t provider_size)
{
    printf("calling stub %s provider ptr: %p size: %u\n", __func__, provider, provider_size);
    if (provider == NULL || provider_size != sizeof(NRSpaceScreenProvider))
    {
        return NR_PLUGIN_RESULT_FAILURE;
    }
    void *p = (void *)(&g_space_screen_provider);
    memcpy(p, provider, provider_size);
    return NR_PLUGIN_RESULT_SUCCESS;
}

static uint64_t period_ns[2] = {500000000, **********}; // 4seconds, 8seconds
NRPluginResult GetHeadPose(NRPluginHandle handle, NRPerceptionType perception_type,
                           uint64_t hmd_time_nanos, NRTrackingPoseType type, NRTransform *head_pose)
{
    HERON_LOG_TRACE("calling stub {}", __func__)
    head_pose->position.x = 0;
    head_pose->position.y = 0;
    head_pose->position.z = 0;

    // head_pose->rotation.qx = 0;
    // head_pose->rotation.qy = 0;
    // head_pose->rotation.qz = 0;
    // head_pose->rotation.qw = 1;

    Vector3f axis(0, 1, 0);

    uint32_t id = perception_type == NR_PERCEPTION_TYPE_EIS ? 1 : 0;
    uint64_t u_tmp = hmd_time_nanos % period_ns[id];
    if (u_tmp > period_ns[id] / 2)
    {
        u_tmp = period_ns[id] - u_tmp;
    }
    int64_t tmp = (int64_t)u_tmp;
    tmp = tmp - period_ns[id] / 4;
    double angle = (double)tmp / period_ns[id];
    if (perception_type == NR_PERCEPTION_TYPE_EIS)
    {
        angle = angle * -1;
    }
    HERON_LOG_TRACE("perception_type:{} angle:{}", perception_type, angle);
    Eigen::Quaternionf quaternion(Eigen::AngleAxisf(angle, axis));
    head_pose->rotation.qx = quaternion.x();
    head_pose->rotation.qy = quaternion.y();
    head_pose->rotation.qz = quaternion.z();
    head_pose->rotation.qw = quaternion.w();

    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult BWDecodeBuffer(NRPluginHandle, NRImageFormat, uint32_t, const char *, uint32_t *, char *) {
    HERON_LOG_TRACE("calling stub {}", __func__);
    return NR_PLUGIN_RESULT_FAILURE;
}

NRPluginResult RgbCameraIsOccupied(NRPluginHandle, bool *occupied) {
    HERON_LOG_TRACE("calling stub {}", __func__);
    *occupied = false;
    return NR_PLUGIN_RESULT_SUCCESS;
}

const static std::string GLOBAL_CONFIG_FILE_NAME = "sdk_global.json";
static std::string global_config_json_string = "";
NRPluginResult GetGlobalConfig(NRPluginHandle /*handle*/, const char **data, uint32_t *size)
{
    HERON_LOG_INFO("calling stub {}", __func__);
    if (global_config_json_string.empty())
    {
        if (!heron::ReadLocalTextFile(GLOBAL_CONFIG_FILE_NAME, global_config_json_string))
            return NR_PLUGIN_RESULT_FAILURE;
    }

    *data = global_config_json_string.data();
    *size = global_config_json_string.size();

    return NR_PLUGIN_RESULT_SUCCESS;
}

const static std::string GLASSES_CONFIG_FILE_NAME = "glasses_config.json";
static std::string glasses_config_json_string = "";
NRPluginResult GetDeviceConfig(NRPluginHandle /*handle*/, const char **data, uint32_t *size)
{
    HERON_LOG_INFO("calling stub {}", __func__);
    if (glasses_config_json_string.empty())
    {
        if (!heron::ReadLocalTextFile(GLASSES_CONFIG_FILE_NAME, glasses_config_json_string))
            return NR_PLUGIN_RESULT_FAILURE;
    }

    *data = glasses_config_json_string.data();
    *size = glasses_config_json_string.size();

    return NR_PLUGIN_RESULT_SUCCESS;
}

const static std::string APP_DEFAULT_CONFIG_FILE_NAME = "pilot_default_config.json";
static std::string app_default_config_str = "";
NRPluginResult GetAppDefaultConfig(NRPluginHandle /*handle*/, const char **data, uint32_t *size)
{
    HERON_LOG_INFO("calling stub {}", __func__);
    if (app_default_config_str.empty())
    {
        if (!heron::ReadLocalTextFile(APP_DEFAULT_CONFIG_FILE_NAME, app_default_config_str))
            return NR_PLUGIN_RESULT_FAILURE;
    }

    *data = app_default_config_str.data();
    *size = app_default_config_str.size();

    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult GetCanvasDepth(NRPluginHandle /*handle*/,
                              float *out_depth)
{
    HERON_LOG_TRACE("calling stub {}", __func__);
    *out_depth = 3.0;
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult GetSpaceMode(NRPluginHandle /*handle*/, NRSpaceMode *out_space_mode)
{
    HERON_LOG_TRACE("calling stub {}", __func__);
    *out_space_mode = NR_SPACE_MODE_HOVER;
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult RegisterMiscProvider(NRPluginHandle /*handle*/, const NRMiscProvider *provider, uint32_t provider_size)
{
    printf("calling stub %s provider ptr: %p size: %u\n", __func__, provider, provider_size);
    if (provider == NULL || provider_size != sizeof(NRMiscProvider))
    {
        return NR_PLUGIN_RESULT_FAILURE;
    }
    void *p = (void *)(&g_misc_provider);
    memcpy(p, provider, provider_size);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult RegisterDeviceMessageProvider(NRPluginHandle /*handle*/, const NRDeviceMessageHandleProvider *provider, uint32_t provider_size)
{
    printf("calling stub %s provider ptr: %p size: %u\n", __func__, provider, provider_size);
    if (provider == NULL || provider_size != sizeof(NRMiscProvider))
    {
        return NR_PLUGIN_RESULT_FAILURE;
    }
    void *p = (void *)(&g_device_message_provider);
    memcpy(p, provider, provider_size);
    return NR_PLUGIN_RESULT_SUCCESS;
}
