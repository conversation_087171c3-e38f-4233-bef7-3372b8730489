#define NRPLUGIN // in order to use NR defined types

#include <heron/interface/include/common/nr_plugin_hmd.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>

#include <framework/util/json.h>

NRPluginResult GetComponentPoseFromHead(NRPluginHandle /*handle*/,
                                        NRComponent component,
                                        NRTransform *out_transform)
{
    out_transform->position.x = out_transform->position.y = out_transform->position.z = 0.0f;
    if (component == NR_COMPONENT_DISPLAY_LEFT)
    {
        out_transform->position.x = -0.032f;
    }
    if (component == NR_COMPONENT_DISPLAY_RIGHT)
    {
        out_transform->position.x = 0.032f;
    }
    out_transform->position.y = 0.0f;
    out_transform->position.z = 0.0f;
    out_transform->rotation.qx = out_transform->rotation.qy = out_transform->rotation.qz = 0.0f;
    out_transform->rotation.qw = 1.0f;
    return NR_PLUGIN_RESULT_SUCCESS;
}

static float left_display_fov[4] = {-0.39990422, 0.37266943, 0.22310436, -0.21339862};
static float right_display_fov[4] = {-0.3729752, 0.39857703, 0.22428653, -0.21179518};
NRPluginResult GetComponentFov(NRPluginHandle /*handle*/,
                               NRComponent component,
                               NRFov4f *out_fov)
{
    if (component == NR_COMPONENT_DISPLAY_LEFT)
    {
        out_fov->left_tan = left_display_fov[0];
        out_fov->right_tan = left_display_fov[1];
        out_fov->top_tan = left_display_fov[2];
        out_fov->bottom_tan = left_display_fov[3];
    }
    if (component == NR_COMPONENT_DISPLAY_RIGHT)
    {
        out_fov->left_tan = right_display_fov[0];
        out_fov->right_tan = right_display_fov[1];
        out_fov->top_tan = right_display_fov[2];
        out_fov->bottom_tan = right_display_fov[3];
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}

const static std::string GLASSES_CONFIG_FILE_NAME = "glasses_config.json";
static std::string glasses_config_json_string = "";
NRPluginResult GetComponentDisplayDistortionSize(NRPluginHandle /*handle*/, NRComponent component, NRSize2i *distortion_size)
{
    if (glasses_config_json_string.empty())
    {
        if (!heron::ReadLocalTextFile(GLASSES_CONFIG_FILE_NAME, glasses_config_json_string))
            return NR_PLUGIN_RESULT_FAILURE;
    }
    Json::Value json_root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    std::istringstream json_stream(glasses_config_json_string);
    if (!parseFromStream(json_builder, json_stream, &json_root, &json_errs))
    {
        HERON_LOG_ERROR("Parse glasses config error, json_errs = {}", json_errs.c_str());
        return NR_PLUGIN_RESULT_FAILURE;
    }
    const Json::Value &distortion_data = json_root["display_distortion"];
    if (!json_root.isMember("display_distortion"))
    {
        HERON_LOG_ERROR("No display_distortion in config");
        return NR_PLUGIN_RESULT_FAILURE;
    }
    int type = 0;
    if (component == NR_COMPONENT_DISPLAY_LEFT)
    {
        const Json::Value &left_distortion_data = distortion_data["left_display"];
        if (left_distortion_data.isMember("type"))
            type = left_distortion_data["type"].asInt();
        if (left_distortion_data.isMember("num_row"))
            distortion_size->height = left_distortion_data["num_row"].asInt();
        if (left_distortion_data.isMember("num_col"))
            distortion_size->width = left_distortion_data["num_col"].asInt();
        HERON_LOG_DEBUG("Parse distortion for left display type:{} cols:{} rows:{}.", type, distortion_size->width, distortion_size->height);
    }
    if (component == NR_COMPONENT_DISPLAY_RIGHT)
    {
        const Json::Value &right_distortion_data = distortion_data["right_display"];
        if (right_distortion_data.isMember("type"))
            type = right_distortion_data["type"].asInt();
        if (right_distortion_data.isMember("num_row"))
            distortion_size->height = right_distortion_data["num_row"].asInt();
        if (right_distortion_data.isMember("num_col"))
            distortion_size->width = right_distortion_data["num_col"].asInt();
        HERON_LOG_DEBUG("Parse distortion for right display type:{} cols:{} rows:{}.", type, distortion_size->width, distortion_size->height);
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult GetComponentDisplayDistortionData(NRPluginHandle /*handle*/, NRComponent component, int size, float *data)
{
    if (glasses_config_json_string.empty())
    {
        if (!heron::ReadLocalTextFile(GLASSES_CONFIG_FILE_NAME, glasses_config_json_string))
            return NR_PLUGIN_RESULT_FAILURE;
    }
    Json::Value json_root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    std::istringstream json_stream(glasses_config_json_string);
    if (!parseFromStream(json_builder, json_stream, &json_root, &json_errs))
    {
        HERON_LOG_ERROR("Parse glasses config error, json_errs = {}", json_errs.c_str());
        return NR_PLUGIN_RESULT_FAILURE;
    }

    const Json::Value &distortion_data = json_root["display_distortion"];
    if (!json_root.isMember("display_distortion"))
    {
        HERON_LOG_ERROR("No display_distortion in config");
        return NR_PLUGIN_RESULT_FAILURE;
    }

    int type = 0, num_rows = 0, num_cols = 0;
    if (component == NR_COMPONENT_DISPLAY_LEFT)
    {
        const Json::Value &left_distortion_data = distortion_data["left_display"];
        if (left_distortion_data.isMember("type"))
            type = left_distortion_data["type"].asInt();
        if (left_distortion_data.isMember("num_row"))
            num_rows = left_distortion_data["num_row"].asInt();
        if (left_distortion_data.isMember("num_col"))
            num_cols = left_distortion_data["num_col"].asInt();
        HERON_LOG_DEBUG("Parse distortion for left display type:{} rows:{} cols:{}.", type, num_rows, num_cols);
        Json::Value distortion_left_mesh_data = left_distortion_data["data"];
        int left_distortion_data_size = (int)distortion_left_mesh_data.size();
        if (left_distortion_data_size != num_rows * num_cols * 4)
        {
            HERON_LOG_ERROR("left distortion data size mismatch. expect{}x{}x4={} got:{}.", num_rows, num_cols, num_rows * num_cols * 4, left_distortion_data_size);
            return NR_PLUGIN_RESULT_FAILURE;
        }
        if (left_distortion_data_size != size)
        {
            HERON_LOG_ERROR("left distortion data size mismatch. expect:{} got:{}.", size, left_distortion_data_size);
            return NR_PLUGIN_RESULT_FAILURE;
        }
        for (int i = 0; i < size; ++i)
            data[i] = distortion_left_mesh_data[i].asFloat();
    }
    if (component == NR_COMPONENT_DISPLAY_RIGHT)
    {
        const Json::Value &right_distortion_data = distortion_data["right_display"];
        if (right_distortion_data.isMember("type"))
            type = right_distortion_data["type"].asInt();
        if (right_distortion_data.isMember("num_row"))
            num_rows = right_distortion_data["num_row"].asInt();
        if (right_distortion_data.isMember("num_col"))
            num_cols = right_distortion_data["num_col"].asInt();
        HERON_LOG_DEBUG("Parse distortion for right display type:{} rows:{} cols:{}.", type, num_rows, num_cols);
        Json::Value distortion_right_mesh_data = right_distortion_data["data"];
        int right_distortion_data_size = (int)distortion_right_mesh_data.size();
        if (right_distortion_data_size != num_rows * num_cols * 4)
        {
            HERON_LOG_ERROR("right distortion data size mismatch. expect{}x{}x4={} got:{}.", num_rows, num_cols, num_rows * num_cols * 4, right_distortion_data_size);
            return NR_PLUGIN_RESULT_FAILURE;
        }
        if (right_distortion_data_size != size)
        {
            HERON_LOG_ERROR("right distortion data size mismatch. expect:{} got:{}.", size, right_distortion_data_size);
            return NR_PLUGIN_RESULT_FAILURE;
        }
        for (int i = 0; i < size; ++i)
            data[i] = distortion_right_mesh_data[i].asFloat();
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}