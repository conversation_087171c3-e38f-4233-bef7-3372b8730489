/**
 * @file app_space_screen.cc
 * @brief This file contains main function to do unit test
 *
 * This program mainly tests space-screen apis of flinger plugin.
 * specifically:
 * NRPluginResult SetDpInputMode(NRPluginHandle handle, NRDpInputMode mode);
 * NRPluginResult SetLocalPerceptionType(NRPluginHandle handle, NRPerceptionType type);
 * NRPluginResult GetCanvasDiagonalSize(NRPluginHandle handle, float *out_size);
 * NRPluginResult UpdateCanvasSize(NRPluginHandle handle, NROperationType operation_type, NRStepType step_type);
 * NRPluginResult GetCanvasDepth(NRPluginHandle handle, float *out_depth);
 * NRPluginResult UpdateCanvasDepth(NRPluginHandle handle, NROperationType operation_type, NRStepType step_type);
 * NRPluginResult GetSpaceMode(NRPluginHandle handle, NRSpaceMode *out_space_mode);
 * NRPluginResult SetSpaceMode(NRPluginHandle handle, NRSpaceMode space_mode);
 * NRPluginResult GetPupilLevelCount(NRPluginHandle handle, int32_t *out_level);
 * NRPluginResult GetPupilLevel(NRPluginHandle handle, int32_t *out_level);
 * NRPluginResult SetPupilLevel(NRPluginHandle handle, int32_t level);
 * NRPluginResult StartPupilAdjust(NRPluginHandle handle);
 * NRPluginResult StopPupilAdjust(NRPluginHandle handle);
 * NRPluginResult GetThumbnailPositionType(NRPluginHandle handle, NRThumbnailPositionType *out_thumbnail_position);
 * NRPluginResult SetThumbnailPositionType(NRPluginHandle handle, NRThumbnailPositionType thumbnail_position);
 *
 * <AUTHOR>
 * @date 09/08/2024
 */
#define NRPLUGIN // in order to use NR defined types

#include <heron/interface/include/public/nr_plugin_lifecycle.h>
#include <heron/interface/include/flinger/nr_plugin_flinger.h>
#include <heron/util/log.h>

#include <lifecycle_common_macro.h>

/// global variables
extern NRPluginHandle heron_g_handle;
extern NRInterfaces g_interfaces;
extern NRPluginLifecycleProvider g_flinger_lifecycle_provider;
extern NRSpaceScreenProvider g_space_screen_provider;

static void CheckProviderApiExistance()
{
    CHECK_EXISTANCE(g_space_screen_provider, SetDpInputMode);
    CHECK_EXISTANCE(g_space_screen_provider, SetLocalPerceptionType);
    CHECK_EXISTANCE(g_space_screen_provider, GetCanvasDiagonalSize);
    CHECK_EXISTANCE(g_space_screen_provider, UpdateCanvasSize);
    CHECK_EXISTANCE(g_space_screen_provider, GetCanvasDepth);
    CHECK_EXISTANCE(g_space_screen_provider, UpdateCanvasDepth);
    CHECK_EXISTANCE(g_space_screen_provider, GetSpaceMode);
    CHECK_EXISTANCE(g_space_screen_provider, SetSpaceMode);
    CHECK_EXISTANCE(g_space_screen_provider, GetPupilLevelCount);
    CHECK_EXISTANCE(g_space_screen_provider, GetPupilLevel);
    CHECK_EXISTANCE(g_space_screen_provider, SetPupilLevel);
    CHECK_EXISTANCE(g_space_screen_provider, StartPupilAdjust);
    CHECK_EXISTANCE(g_space_screen_provider, StopPupilAdjust);
    CHECK_EXISTANCE(g_space_screen_provider, GetThumbnailPositionType);
    CHECK_EXISTANCE(g_space_screen_provider, SetThumbnailPositionType);
}

int main(int argc, char **argv)
{
    HERON_LOG_INFO("testing NRPluginLifecycleProvider apis ...");

    NRPluginLoad_FLINGER(&g_interfaces);

    NRPluginHandle plugin_handle = 3;

    CALL_LIFECYCLE_FUNC(Register)
    HERON_LOG_INFO("heron_g_handle after register: {}", heron_g_handle);
    CALL_LIFECYCLE_FUNC(Initialize)
    CALL_LIFECYCLE_FUNC(Start)
    uint32_t rounds = 10;
    for (uint32_t i = 0; i <= rounds; i++)
    {
        PLUGIN_API_ABORT_ON_ERROR(g_space_screen_provider.SetDpInputMode(heron_g_handle, NR_DP_INPUT_MODE_STEREO), "SetDpInpotMode");
        HERON_LOG_INFO("SetDpInputMode to STEREO success {}/{}.", i, rounds);
        usleep(2 * 1000 * 1000);
        PLUGIN_API_ABORT_ON_ERROR(g_space_screen_provider.SetDpInputMode(heron_g_handle, NR_DP_INPUT_MODE_MONO), "SetDpInpotMode");
        HERON_LOG_INFO("SetDpInputMode to MONO success {}/{}.", i, rounds);
        usleep(2 * 1000 * 1000);
    }
    usleep(300 * 1000 * 1000); // let stub Dp thread and Dpu threads run for a while
    CALL_LIFECYCLE_FUNC(Update)
    CALL_LIFECYCLE_FUNC(Pause)
    CALL_LIFECYCLE_FUNC(Resume)
    CALL_LIFECYCLE_FUNC(Stop)
    CALL_LIFECYCLE_FUNC(Unregister)
    CALL_LIFECYCLE_FUNC(Release)

    NRPluginUnload_FLINGER();
    HERON_LOG_INFO("test NRPluginLifecycleProvider apis done.");
    framework::util::log::Logger::shutdown();
    return 0;
}