/**
 * @file app_interface_stub.cc
 * @brief This file contains main function to test stub
 *         interface functions in interface_stub.cc
 * <AUTHOR> <EMAIL>
 * @date 05/28/2024
 */

#define NRPLUGIN // in order to use NR defined types

#include <heron/interface/include/public/nr_plugin_lifecycle.h>
#include <heron/interface/include/common/nr_plugin_generic.h>
#include <heron/interface/include/flinger/nr_plugin_flinger.h>
#include <heron/util/log.h>

#include <lifecycle_common_macro.h>

#include <framework/util/plugin_util.h>

/// global variables
extern NRInterfaces g_interfaces;
extern NRPluginLifecycleProvider g_flinger_lifecycle_provider;
extern NRPluginHandle heron_g_handle;
extern std::unique_ptr<NRFlingerInterface> g_flinger_interface;
extern std::unique_ptr<NRGenericInterface> g_generic_interface;

int main(int argc, char **argv)
{
    HERON_LOG_INFO("testing NRPluginLifecycleProvider apis ...");

    NRPluginLoad_FLINGER(&g_interfaces);

    NRPluginHandle plugin_handle = 3; // simulate the logic of Dove calling register and set the handle to us
    CALL_LIFECYCLE_FUNC(Register)
    HERON_LOG_INFO("plugin flinger registered. plugin handle: {}", heron_g_handle);
    CALL_LIFECYCLE_FUNC(Initialize)
    HERON_LOG_INFO("plugin flinger initialized.");

    /// test NRGenericInterface stub funcs
    if (!g_generic_interface)
    {
        HERON_LOG_ERROR("g_generic_interface is null");
    }
    else
    {
        if (g_generic_interface->GetGlobalConfig)
        {
            const char *global_config_str;
            uint32_t global_config_size;
            g_generic_interface->GetGlobalConfig(heron_g_handle, &global_config_str, &global_config_size);
            HERON_LOG_INFO("global config body: {} global_config_size = {}.", global_config_str, global_config_size);
        }
        else
        {
            HERON_LOG_ERROR("GetGlobalConfig not implemented");
        }
    }

    ///// test NRFlingerInterface stub funcs
    if (!g_flinger_interface)
    {
        HERON_LOG_ERROR("g_flinger_interface is null");
    }
    else
    {
    }

    HERON_LOG_INFO("test NRPluginLifecycleProvider apis done.");

    CALL_LIFECYCLE_FUNC(Unregister)
    CALL_LIFECYCLE_FUNC(Release)

    NRPluginUnload_FLINGER();
    framework::util::log::Logger::shutdown();
    return 0;
}