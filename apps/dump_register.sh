#!/bin/sh

de_0_base=0x08820000
current_value=$(devmem $((de_0_base + 0x20c8)))
new_value=$((current_value & ~(0x7 << 16)))
new_value=$((new_value | (0x1 << 16)))
devmem $((de_0_base + 0x20c8)) 32 $new_value
reg0=$(devmem $((de_0_base + 0x2130)))

current_value=$(devmem $((de_0_base + 0x20c8)))
new_value=$((current_value & ~(0x7 << 16)))
new_value=$((new_value | (0x2 << 16)))
devmem $((de_0_base + 0x20c8)) 32 $new_value
reg1=$(devmem $((de_0_base + 0x2130)))

devmem $((de_0_base + 0x14d0)) 32 0xF
reg2=$(devmem $((de_0_base + 0x14d8)))

echo "de0 reg0:$reg0 reg1:$reg1 reg2:$reg2"


de_1_base=0x08840000
current_value=$(devmem $((de_1_base + 0x20c8)))
new_value=$((current_value & ~(0x7 << 16)))
new_value=$((new_value | (0x1 << 16)))
devmem $((de_1_base + 0x20c8)) 32 $new_value
reg0=$(devmem $((de_1_base + 0x2130)))

current_value=$(devmem $((de_1_base + 0x20c8)))
new_value=$((current_value & ~(0x7 << 16)))
new_value=$((new_value | (0x2 << 16)))
devmem $((de_1_base + 0x20c8)) 32 $new_value
reg1=$(devmem $((de_1_base + 0x2130)))

devmem $((de_1_base + 0x14d0)) 32 0xF
reg2=$(devmem $((de_1_base + 0x14d8)))

echo "de1 reg0:$reg0 reg1:$reg1 reg2:$reg2"


