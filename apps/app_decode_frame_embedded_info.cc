/**
 * @file app_decode_frame_embedded_info.cc
 * @brief This file contains main function to do unit test
 *
 * This program mainly tests FrameEmbeddedInfo generated by
 * FRAMEWORK_INIT_PROTO_VARIABLE
 *
 * <AUTHOR> <EMAIL>
 * @date 07/31/2024
 */

#include <heron/util/log.h>

#include <frame_embedded_info.hpp>

using namespace heron;
using namespace msg;

int main(int argc, char **argv)
{
    heron::Logger::GetInstance()->SetLogLevel(1);
    FrameEmbeddedInfo test_embedded_info_obj;
    HERON_LOG_INFO("{}", test_embedded_info_obj.GetName());
    framework::util::log::Logger::shutdown();
    return 0;
}
