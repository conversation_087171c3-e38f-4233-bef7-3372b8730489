/**
 * @file app_gen_debug_warp_bin.cc
 * @brief This file contains main function to do unit test
 *
 * This program generates debug warp bin file of size
 * 35x3x3xsizeof(float) = 1260 Bytes
 *
 * <AUTHOR> <EMAIL>
 * @date 10/18/2024
 */

#include <heron/util/warp.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>

#include <fstream>

using namespace heron;

// warp matrix to trigger "need reset" {1.21567, 0.29471, -1198.38464, -0.27942, 1.20662, -802.58691, -0.00000, 0.00001, 0.2488}
// warp matrix left 90 degree around y axis {0.03267, 0.01645, -693.16766, 0.05305, 0.24968, -176.41278, 0.00010, 0.00000, -0.14078}
// warp matrix right 90 degree around y axis {-0.08139, -0.012, 727.07122, -0.0338, 0.25107, -53.419567, -0.00001, 0.00000, 0.11540}
static float s_test_gdc_matrix33[9]{1.21567, 0.29471, -1198.38464, -0.27942, 1.20662, -802.58691, -0.00000, 0.00001, 0.2488};

int main(int argc, char **argv)
{
    std::string file_name = "debug_matrix.bin";
    HERON_LOG_INFO("generating {} by following matrix", file_name);
    HERON_LOG_INFO("line1: [{:.5f}, {:.5f}, {:.5f}]", s_test_gdc_matrix33[0], s_test_gdc_matrix33[1], s_test_gdc_matrix33[2]);
    HERON_LOG_INFO("line2: [{:.5f}, {:.5f}, {:.5f}]", s_test_gdc_matrix33[3], s_test_gdc_matrix33[4], s_test_gdc_matrix33[5]);
    HERON_LOG_INFO("line3: [{:.5f}, {:.5f}, {:.5f}]", s_test_gdc_matrix33[6], s_test_gdc_matrix33[7], s_test_gdc_matrix33[8]);

    Vector3f p_test(960, 1080, 1);
    Mat3f m_test(s_test_gdc_matrix33);
    Vector3f p_test_result = m_test.transpose() * p_test;
    PrintObject("p_test", p_test);
    PrintObject("m_test", m_test);
    PrintObject("p_test_result", p_test_result);
    HERON_LOG_INFO("src_x:{} src_y:{}", p_test_result.x() / p_test_result.z(), p_test_result.y() / p_test_result.z());

    std::ofstream out_file(file_name.c_str(), std::ios::binary);
    // Check if the file opened successfully
    if (!out_file)
    {
        HERON_LOG_INFO("error opening file {} for writing", file_name);
        return -1;
    }

    // Write the entire vector to the file
    for (uint32_t col = 0; col < 35; col++)
    {
    out_file.write((const char *)(&s_test_gdc_matrix33[0]), sizeof(s_test_gdc_matrix33));
    }

    // Close the file
    out_file.close();
    HERON_LOG_INFO("Data written to {} successfully.", file_name);
    framework::util::log::Logger::shutdown();
    return 0;
}
