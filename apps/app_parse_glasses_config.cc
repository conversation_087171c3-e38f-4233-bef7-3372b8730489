/**
 * @file app_gen_weight_dat.cc
 * @brief This file contains main function to do unit test
 *
 * This program mainly tests mesh generation from glasses config
 *
 * <AUTHOR> <EMAIL>
 * @date 06/06/2024
 */

#define NRPLUGIN // in order to use NR defined types

#include <heron/interface/include/public/nr_plugin_lifecycle.h>
#include <heron/interface_provider/generic.h>
#include <heron/model/model_manager.h>
#include <heron/model/glasses_config.h>
#include <heron/util/types.h>
#include <heron/util/log.h>

#include <framework/util/plugin_util.h>
#include <framework/util/json.h>

#include <lifecycle_common_macro.h>

/// global variables
extern NRInterfaces g_interfaces;
extern NRPluginLifecycleProvider g_flinger_lifecycle_provider;
extern NRPluginHandle heron_g_handle;

using namespace heron;
using namespace interface_provider;

int main(int argc, char **argv)
{
    HERON_LOG_INFO("testing NRPluginLifecycleProvider apis ...");

    NRPluginLoad_FLINGER(&g_interfaces);

    NRPluginHandle plugin_handle = 3;
    CALL_LIFECYCLE_FUNC(Register)
    HERON_LOG_INFO("plugin flinger registered. plugin handle: {}", heron_g_handle);
    CALL_LIFECYCLE_FUNC(Initialize)
    HERON_LOG_INFO("plugin flinger initialized.");

    /// test NRGenericInterface stub funcs
    const char *device_config_str = nullptr;
    uint32_t device_config_size = 0;

    if (!GenericInterface::GetInstance()->GetDeviceConfig(&device_config_str, &device_config_size))
    {
        HERON_LOG_ERROR("GetDeviceConfig error");
        framework::util::log::Logger::shutdown();
        return -1;
    }
    if (!ParseGlassesConfig(device_config_str, device_config_size))
    {
        HERON_LOG_ERROR("ParseGlassesConfig error");
        framework::util::log::Logger::shutdown();
        return -1;
    }
    HERON_LOG_INFO("display resolution parsed. left: {}x{} right: {}x{}",
                   model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_LEFT].width,
                   model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_LEFT].height,
                   model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_RIGHT].width,
                   model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_RIGHT].height);

    model::ModelManager::GetInstance()->InitDistortionMeshData();
    HERON_LOG_INFO("display distortion_info parsed. left: {}x{} right: {}x{}",
                   model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_LEFT].distortion_info.num_columns,
                   model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_LEFT].distortion_info.num_rows,
                   model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_RIGHT].distortion_info.num_columns,
                   model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_RIGHT].distortion_info.num_rows);
    CALL_LIFECYCLE_FUNC(Unregister)
    CALL_LIFECYCLE_FUNC(Release)

    NRPluginUnload_FLINGER();
    HERON_LOG_INFO("ALL PASS!");
    framework::util::log::Logger::shutdown();
    return 0;
}
