/**
 * @file app_pluginload.cc
 * @brief This file contains main function to do unit test
 *
 * This program mainly tests NRPluginLoad_FLINGER()
 * and NRPluginUnload_FLINGER()
 *
 * <AUTHOR> <EMAIL>
 * @date 04/09/2024
 */
#include <heron/interface/include/public/nr_plugin_interface.h>
#include <heron/util/log.h>

extern "C" void NRPluginLoad_FLINGER(NRInterfaces *interfaces);
extern "C" void NRPluginUnload_FLINGER();

/// global variables
extern NRInterfaces g_interfaces;

int main(int argc, char **argv)
{
    const int rounds = 10000;
    HERON_LOG_INFO("testing flinger pluginload/unload for {} rounds...", rounds);

    for (int i = 0; i < rounds; i++)
    {
        NRPluginLoad_FLINGER(&g_interfaces);
        NRPluginUnload_FLINGER();
    }

    HERON_LOG_INFO("test flinger pluginload/unload for {} rounds done.", rounds);
    framework::util::log::Logger::shutdown();
    return 0;
}
