/**
 * @file app_dump_register.cc
 * @brief This file contains main function to print out
 * register values
 *
 * This program is a tool app to print out board register
 * values
 *
 * <AUTHOR> <EMAIL>
 * @date 05/10/2024
 */

#include <heron/util/log.h>

#include <fcntl.h>    // For O_SYNC, O_RDWR, and other file control options
#include <sys/mman.h> // For mmap and related memory management functions

static int fd_dptest_reg_base;
static unsigned long g_va_base_reg_base;
static unsigned long g_va_de_reg_base[2];
static unsigned long g_va_gdc_reg_base[2];

#define AR_DPTEST_SET_REG_BITS(addr, val) (*(volatile unsigned int *)(addr)) = (val)
#define AR_DPTEST_GET_REG_BITS(addr) (*(volatile unsigned int *)(addr))

// map 0x08800000-08900000  len:0x100000
static void ar_dptest_system_register_map()
{
    fd_dptest_reg_base = open("/dev/mem", O_RDWR | O_SYNC);
    g_va_base_reg_base = (unsigned long)mmap(NULL, 0x100000, PROT_READ | PROT_WRITE, MAP_SHARED, fd_dptest_reg_base, 0x08800000);

    g_va_de_reg_base[0] = g_va_base_reg_base + 0x20000;
    g_va_de_reg_base[1] = g_va_base_reg_base + 0x40000;
    g_va_gdc_reg_base[0] = g_va_base_reg_base + 0x30000;
    g_va_gdc_reg_base[1] = g_va_base_reg_base + 0x50000;
}

static void ar_dptest_system_register_unmap()
{
    munmap((unsigned char *)g_va_base_reg_base, 0x100000);
    close(fd_dptest_reg_base);
}


int main(int argc, char **argv)
{
    HERON_LOG_INFO("dumpping registers");
    ar_dptest_system_register_map();

    // XXX read register value
    AR_DPTEST_GET_REG_BITS(g_va_de_reg_base[0] + 0x1518); // dpu0 underflow register

    ar_dptest_system_register_unmap();
    framework::util::log::Logger::shutdown();
    return 0;
}
