/**
 * @file app_flinger_provider_test.cc
 * @brief This file contains main function to do unit test
 *
 * This program mainly tests flinger provider apis.
 * specifically:
 *  NRPluginResult(NR_INTERFACE_API *AllocateOsdFrameBuffers)
 *  NRPluginResult(NR_INTERFACE_API *SubmitOsdFrameBuffer)
 *  NRPluginResult(NR_INTERFACE_API *WaitOsdFrameBuffer)
 *
 * <AUTHOR> <EMAIL>
 * @date 06/14/2024
 */

#define NRPLUGIN // in order to use NR defined types

#include <heron/interface/include/public/nr_plugin_lifecycle.h>
#include <heron/interface/include/flinger/nr_plugin_flinger.h>
#include <heron/util/log.h>

#include <lifecycle_common_macro.h>

#define OSD_IMAGE_WIDTH 960
#define OSD_IMAGE_STRIDE 1024 // align 128
#define OSD_IMAGE_HEIGHT 540
#define OSD_FRAMEBUFFER_QUEUE_BUFFER_COUNT 2
#define OSD_IMAGE_PLANE_SIZE OSD_IMAGE_STRIDE *OSD_IMAGE_HEIGHT
#define OSD_FRAMEBUFFER_SIZE OSD_IMAGE_PLANE_SIZE * 4

/// global variables
extern NRPluginHandle heron_g_handle;
extern NRInterfaces g_interfaces;
extern NRPluginLifecycleProvider g_flinger_lifecycle_provider;
extern NRFlingerProvider g_flinger_provider;
extern NRSpaceScreenProvider g_space_screen_provider;

static NRFrameBufferQueue *s_nr_framebuffer_queues[2] = {nullptr};
static int32_t SAMPLE_VOU_ReadOneFrame(FILE *fp, char *pY, char *pU, char *pV,
                                       uint32_t width, uint32_t height, uint32_t stride, uint32_t stride2);
static void CheckProviderApiExistance()
{
    CHECK_EXISTANCE(g_flinger_provider, StartOsdRender);
    CHECK_EXISTANCE(g_flinger_provider, StopOsdRender);
    CHECK_EXISTANCE(g_flinger_provider, SubmitOsdFrameBuffer);
    CHECK_EXISTANCE(g_flinger_provider, WaitOsdFrameBuffer);
    CHECK_EXISTANCE(g_flinger_provider, ReleaseOsdFrameBufferQueue);
}

int main(int argc, char **argv)
{
    HERON_LOG_INFO("testing NRFlingerProvider apis ...");

    NRPluginLoad_FLINGER(&g_interfaces);

    NRPluginHandle plugin_handle = 3;

    CALL_LIFECYCLE_FUNC(Register)
    HERON_LOG_INFO("heron_g_handle after register: {}", heron_g_handle);

    CheckProviderApiExistance();
    CALL_LIFECYCLE_FUNC(Initialize)
    CALL_LIFECYCLE_FUNC(Start)

    NRFrameBufferAllocateInfo nr_framebuffer_alloc_info;
    nr_framebuffer_alloc_info.format = NR_FRAME_BUFFER_FORMAT_RGBA_PLANAR;
    nr_framebuffer_alloc_info.width = OSD_IMAGE_WIDTH;
    nr_framebuffer_alloc_info.height = OSD_IMAGE_HEIGHT;
    uint32_t count = OSD_FRAMEBUFFER_QUEUE_BUFFER_COUNT;

    PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.AllocateOsdFrameBufferQueue(
                                  heron_g_handle, &nr_framebuffer_alloc_info, count, &s_nr_framebuffer_queues[0]),
                              "AllocateOsdFrameBufferQueue0");
    HERON_LOG_INFO("AllocateOsdFrameBufferQueue0 success.");
    PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.AllocateOsdFrameBufferQueue(
                                  heron_g_handle, &nr_framebuffer_alloc_info, count, &s_nr_framebuffer_queues[1]),
                              "AllocateOsdFrameBufferQueue1");
    HERON_LOG_INFO("AllocateOsdFrameBufferQueue1 success.");

    void *osd_framebuffer_payload[2];
    osd_framebuffer_payload[0] = new char[OSD_FRAMEBUFFER_SIZE];
    std::memset(osd_framebuffer_payload[0], 0x7f, OSD_IMAGE_PLANE_SIZE / 2);                                              // top half of A(panel_0)
    std::memset((void *)((char *)osd_framebuffer_payload[0] + OSD_IMAGE_PLANE_SIZE / 2), 0xff, OSD_IMAGE_PLANE_SIZE / 2); // bottom half of A(panel_0)
    osd_framebuffer_payload[1] = new char[OSD_FRAMEBUFFER_SIZE];
    std::memset(osd_framebuffer_payload[1], 0xff, OSD_IMAGE_PLANE_SIZE / 2);                                              // top half of A(panel_0)
    std::memset((void *)((char *)osd_framebuffer_payload[1] + OSD_IMAGE_PLANE_SIZE / 2), 0x7f, OSD_IMAGE_PLANE_SIZE / 2); // bottom half of A(panel_0)
    FILE *in_file;
    const char *source_image_file_path[2] = {"960_540.rgb", "960_540_2.rgb"};
    // const char *source_image_file_path[2] = {"1920_1080.rgb", "1920_1080_2.rgb"};
    for (uint32_t i = 0; i < 2; i++)
    {
        in_file = fopen(source_image_file_path[i], "rb");
        if (!in_file)
        {
            HERON_LOG_ERROR("open {} failed", source_image_file_path[i]);
            usleep(500 * 1000);
            std::abort();
        }
        if (SAMPLE_VOU_ReadOneFrame(in_file, (char *)osd_framebuffer_payload[i] + OSD_IMAGE_STRIDE * OSD_IMAGE_HEIGHT * 1,
                                    ((char *)osd_framebuffer_payload[i]) + OSD_IMAGE_STRIDE * OSD_IMAGE_HEIGHT * 2,
                                    ((char *)osd_framebuffer_payload[i]) + OSD_IMAGE_STRIDE * OSD_IMAGE_HEIGHT * 3,
                                    OSD_IMAGE_WIDTH, OSD_IMAGE_HEIGHT, OSD_IMAGE_STRIDE, OSD_IMAGE_STRIDE) < 0)
        {
            HERON_LOG_ERROR("Read data from file: {} fail", source_image_file_path[i]);
            usleep(500 * 1000);
            std::abort();
        }
        fclose(in_file);
    }
    PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.StartOsdRender(heron_g_handle), "StartOsdRender");
    HERON_LOG_INFO("StartOsdRender success.");
    usleep(500 * 1000);
    // test disable right after enable
    PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.StopOsdRender(heron_g_handle), "StopOsdRender");
    HERON_LOG_INFO("StopOsdRender success. (right after Start)");
    usleep(500 * 1000);

    uint32_t time_seconds = 1000;
    for (uint32_t i = 0; i < time_seconds; i++)
    {
        PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.StartOsdRender(heron_g_handle), "StartOsdRender");
        HERON_LOG_INFO("StartOsdRender success. {}/{}", i, time_seconds);
        usleep(500 * 1000);

        memcpy(s_nr_framebuffer_queues[0]->buffer_queue[i % 2], osd_framebuffer_payload[i % 2], OSD_FRAMEBUFFER_SIZE);
        memcpy(s_nr_framebuffer_queues[1]->buffer_queue[i % 2], osd_framebuffer_payload[(i+1) % 2], OSD_FRAMEBUFFER_SIZE);
        NRFrameBuffer framebuffer;
        framebuffer.left_buffer = s_nr_framebuffer_queues[0]->buffer_queue[i % 2];
        framebuffer.right_buffer = s_nr_framebuffer_queues[1]->buffer_queue[i % 2];
        PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.SubmitOsdFrameBuffer(heron_g_handle, &framebuffer), "SubmitOsdFrameBuffer");
        HERON_LOG_INFO("SubmitOsdFrameBuffer success. {}/{}", i, time_seconds);

        PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.WaitOsdFrameBuffer(heron_g_handle, &framebuffer), "WaitOsdFrameBuffer");
        HERON_LOG_INFO("WaitOsdFrameBuffer success. {}/{}", i, time_seconds);

        usleep(1 * 1000 * 1000);
        PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.StopOsdRender(heron_g_handle), "StopOsdRender");

        usleep(500 * 1000);
    }
    CALL_LIFECYCLE_FUNC(Update)
    CALL_LIFECYCLE_FUNC(Pause)
    CALL_LIFECYCLE_FUNC(Resume)
    CALL_LIFECYCLE_FUNC(Stop)

    PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.ReleaseOsdFrameBufferQueue(heron_g_handle, s_nr_framebuffer_queues[0]), "ReleaseOsdFrameBufferQueue0");
    HERON_LOG_INFO("ReleaseOsdFrameBufferQueue0 success.");
    PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.ReleaseOsdFrameBufferQueue(heron_g_handle, s_nr_framebuffer_queues[1]), "ReleaseOsdFrameBufferQueue1");
    HERON_LOG_INFO("ReleaseOsdFrameBufferQueue1 success.");

    delete ((char *)(osd_framebuffer_payload[0]));
    delete ((char *)(osd_framebuffer_payload[1]));
    CALL_LIFECYCLE_FUNC(Unregister)
    CALL_LIFECYCLE_FUNC(Release)

    NRPluginUnload_FLINGER();
    HERON_LOG_INFO("test NRFlingerProvider apis done.");
    framework::util::log::Logger::shutdown();
    return 0;
}

int32_t SAMPLE_VOU_ReadOneFrame(FILE *fp, char *pCh1, char *pCh2, char *pCh3,
                                uint32_t width, uint32_t height, uint32_t stride, uint32_t stride2)
{
    char *pDst;
    uint32_t u32Row;
    uint32_t total = 0;
    uint32_t readlen = 0;
    uint32_t bytePerPixel = 1;
    pDst = pCh1;
    for (u32Row = 0; u32Row < height; u32Row++)
    {
        if ((readlen = fread(pDst, 1, width * bytePerPixel, fp)) != (width * bytePerPixel))
        {
            HERON_LOG_ERROR("ret = -1, readlen = {}", readlen);
            return -1;
        }

        pDst += stride;
        total += readlen;
    }

    if (pCh2)
    {
        pDst = pCh2;
        for (u32Row = 0; u32Row < height; u32Row++)
        {
            if ((readlen = fread(pDst, 1, width, fp)) != width)
            {
                HERON_LOG_ERROR("ret = -2, readlen = {}", readlen);
                return -2;
            }
            pDst += stride2;
            total += readlen;
        }
    }

    if (pCh3)
    {
        pDst = pCh3;
        for (u32Row = 0; u32Row < height; u32Row++)
        {
            if ((readlen = fread(pDst, 1, width, fp)) != width)
            {
                HERON_LOG_ERROR("ret = -3, readlen = {}", readlen);
                return -3;
            }
            pDst += stride2;
            total += readlen;
        }
    }
    // printf("*******************u32UVHeight: %d, width: %d, total: %u\n", u32UVHeight, width, total);
    return 0;
}