#pragma once

#define CALL_LIFECYCLE_FUNC(__FUNCTION_NAME__)                                              \
    if (g_flinger_lifecycle_provider.__FUNCTION_NAME__)                                     \
    {                                                                                       \
        NRPluginResult ret = g_flinger_lifecycle_provider.__FUNCTION_NAME__(plugin_handle); \
        if (NR_PLUGIN_RESULT_SUCCESS != ret)                                                \
        {                                                                                   \
            fprintf(stderr, "%s failed going to abort\n", #__FUNCTION_NAME__);              \
            sleep(1);                                                                       \
            std::abort();                                                                   \
        }                                                                                   \
    }                                                                                       \
    else                                                                                    \
    {                                                                                       \
        fprintf(stderr, "Lifecycle %s not implemented.", #__FUNCTION_NAME__);               \
    }

#define PLUGIN_API_ABORT_ON_ERROR(expression, name)      \
    {                                                    \
        NRPluginResult ret = expression;                 \
        if (NR_PLUGIN_RESULT_SUCCESS != ret)             \
        {                                                \
            HERON_LOG_ERROR("{} result: {}", name, ret); \
            usleep(500 * 1000);                          \
            std::abort();                                \
        }                                                \
    }

#define CHECK_EXISTANCE(__PROVIDER__, __API__)                              \
    if (!__PROVIDER__.__API__)                                              \
    {                                                                       \
        HERON_LOG_ERROR("{}.{} not implemented.", #__PROVIDER__, #__API__); \
        usleep(500 * 1000);                                                 \
        std::abort();                                                       \
    }


extern "C" void NRPluginLoad_FLINGER(NRInterfaces *interfaces);
extern "C" void NRPluginUnload_FLINGER();
