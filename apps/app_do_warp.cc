/**
 * @file app_do_warp.cc
 * @brief This file contains main function to do unit test
 *
 * This program mainly tests warp algorithm
 * from pose-->GDC warp matrices
 *
 * <AUTHOR> <EMAIL>
 * @date 04/09/2024
 */
#include <heron/util/warp.h>
#include <heron/util/log.h>

#include <warpcore/warp_interfaces.h>

using namespace heron;
using namespace heron::warp;

// quad_size_x: 1.7777778 y: 1

// inputs
static Vector3f s_quad_position(0, 0, -3);
static Quatf s_quad_rotation(1, 0, 0, 0); // watch the order: [w, x, y, z]
static float s_k_screen[9] = {
    2697.62,
    0.0,
    957.451,
    0.0,
    2671.09,
    537.519,
    0.0,
    0.0,
    1.0};
static float s_left_tan = -0.35492432;
static float s_right_tan = 0.35681415;
static float s_top_tan = 0.20123582;
static float s_bottom_tan = -0.2030935;
static float s_zn = 0.3;
static float s_zf = 100.0;
static Vector3f s_new_pose_position(-0.045154784, 0.058474857, -0.056850716);
static Quatf s_new_pose_rotation(0.9915057, -0.048350833, 0.11949474, -0.017312935);
static Vector2i s_quad_size_pixel(1920, 1080);
static Vector2i s_screen_size_pixel(1920, 1080);
static Vector2f s_quad_size_meters(1.7777778, 1.0);
// static ImgSize2f s_quad_size_meters(1.0, 1.0);

static Vector3f s_plane_normal_oldpose(0, 0, 1);
static Vector3f s_plane_point_oldpose(1, 1, 0);

// expected result
static float s_expected_quad_final_warp_data[16] = { // column major
    0.20784192, -0.0004415363, 9.798635e-09, 0.028449386,
    0.0058506876, 0.20493507, 3.0829932e-09, 0.006870219,
    0, 0, 0, 0,
    0.022190467, 0.090762585, 7.923065e-08, 0.32852426};

int main(int argc, char **argv)
{
    Transform default_transform;
    Mat4f expected_quad_final_warp(s_expected_quad_final_warp_data);
    Transform quad_transform;
    quad_transform.position = s_quad_position;
    quad_transform.rotation = s_quad_rotation;
    Transform new_pose_transform;
    new_pose_transform.position = s_new_pose_position;
    new_pose_transform.rotation = s_new_pose_rotation;

    HERON_LOG_INFO(">>>>>Do final warp mat4 to GDC mat3");
    Mat3f expected_gdc_quad_warp;
    GLWarp2GDCWarp(expected_gdc_quad_warp, expected_quad_final_warp, s_screen_size_pixel, s_quad_size_pixel);
    HERON_LOG_INFO(">>>>>Do GDC3x3 full pipeline(initialize gdc_quad_warp with tangents)");
    // cv method
    Mat3f warp_matrix_gdc;
    std::shared_ptr<GDCQuadWarp> gdc_quad_warp = GDCQuadWarp::Create();
    gdc_quad_warp->WarpInit(s_left_tan, s_right_tan, s_top_tan, s_bottom_tan,
                            s_screen_size_pixel, s_quad_size_pixel, s_quad_size_meters, default_transform);
    gdc_quad_warp->SetQuadTransform(quad_transform);
    gdc_quad_warp->SetToTransform(new_pose_transform);
    gdc_quad_warp->DoWarp();
    gdc_quad_warp->GetGDCWarpMatrix33(warp_matrix_gdc);
    HERON_LOG_INFO(">>>>>comparing...");
    // PrintObject("expected gdc matrix", expected_gdc_quad_warp);
    // PrintObject("warp matrix gdc from cv method:", warp_matrix_gdc);
    if (!CheckObject("compare expected_gdc_quad_warp with warp_matrix_gdc", expected_gdc_quad_warp, warp_matrix_gdc, 1e-4))
    {
        framework::util::log::Logger::shutdown();
        return -1;
    }

    HERON_LOG_INFO(">>>>>Do GDC3x3 full pipeline initialize gdc_quad_warp(initialize gdc_quad_warp with k_screen)");
    float fc[2]{s_k_screen[0], s_k_screen[4]};
    float cc[2]{s_k_screen[2], s_k_screen[5]};
    HERON_LOG_INFO("fc[0]:{}, fc[1]:{}, cc[0]:{}, cc[1]:{}", fc[0], fc[1], cc[0], cc[1]);
    gdc_quad_warp->WarpInit(fc, cc, s_quad_size_pixel, s_quad_size_meters, default_transform);
    gdc_quad_warp->SetQuadTransform(quad_transform);
    gdc_quad_warp->SetToTransform(new_pose_transform);
    gdc_quad_warp->DoWarp();
    gdc_quad_warp->GetGDCWarpMatrix33(warp_matrix_gdc);
    HERON_LOG_INFO(">>>>>comparing...");
    // PrintObject("expected gdc matrix", expected_gdc_quad_warp);
    //  PrintObject("warp matrix gdc from cv method:", warp_matrix_gdc);
    if (!CheckObject("compare expected_gdc_quad_warp with warp_matrix_gdc", expected_gdc_quad_warp, warp_matrix_gdc, 1e-4))
    {
        framework::util::log::Logger::shutdown();
        return -1;
    }
    HERON_LOG_INFO(">>>>>Do GDC3x3 plane warp");
    Mat3f gdc33_plane_warp;
    std::shared_ptr<GDCPlaneWarp> gdc_plane_warp = GDCPlaneWarp::Create();
    gdc_plane_warp->WarpInit(s_left_tan, s_right_tan, s_top_tan, s_bottom_tan, s_quad_size_pixel);
    gdc_plane_warp->SetFromTransform(quad_transform);
    gdc_plane_warp->SetToTransform(new_pose_transform);
    gdc_plane_warp->SetPlaneInfo(s_plane_point_oldpose, s_plane_normal_oldpose);
    gdc_plane_warp->DoWarp();
    gdc_plane_warp->GetGDCWarpMatrix33(gdc33_plane_warp);
    // PrintObject("gdc plane warp result:", gdc33_plane_warp_result);

    HERON_LOG_INFO(">>>>>Do warpcore plane warp");
    Mat4f project_matrix, warpcore_display_warp_calced;
    project_matrix << 2.0 / (s_right_tan - s_left_tan), 0,
        (s_right_tan + s_left_tan) / (s_right_tan - s_left_tan), 0, 0,
        2.0 / (s_top_tan - s_bottom_tan),
        (s_top_tan + s_bottom_tan) / (s_top_tan - s_bottom_tan), 0, 0, 0,
        (s_zn + s_zf) / (s_zn - s_zf), (2 * s_zn * s_zf) / (s_zn - s_zf), 0, 0, -1, 0;
    DisplayWarpPtr display_warper = DisplayWarp::Create();
    display_warper->SetDisplayProjectMatrix(project_matrix);
    display_warper->SetPlaneInfo(s_plane_point_oldpose, s_plane_normal_oldpose, false);
    display_warper->SetFromTransform(quad_transform);
    display_warper->SetToTransform(new_pose_transform);
    display_warper->DoWarp();
    display_warper->GetDisplayWarpMatrix(warpcore_display_warp_calced);
    Mat4f texture_matrix, warpcore_ori_final_warp;
    texture_matrix << 0.5, 0, 0, 0.5, 0, 0.5, 0, 0.5, 0, 0, 1, 0, 0, 0, 0, 1;
    warpcore_ori_final_warp = texture_matrix * warpcore_display_warp_calced;
    Mat3f warpcore_gdc_final_warp;
    GLWarp2GDCWarp(warpcore_gdc_final_warp, warpcore_ori_final_warp, s_screen_size_pixel, s_quad_size_pixel);
    // PrintObject("warpcore gdc plane warp result:", warpcore_gdc_final_warp);
    HERON_LOG_INFO(">>>>>comparing...");
    if (!CheckObject("compare warpcore_gdc_final_warp with gdc33_plane_warp", warpcore_gdc_final_warp, gdc33_plane_warp, 5e-4))
    {
        framework::util::log::Logger::shutdown();
        return -1;
    }

    HERON_LOG_INFO(">>>>>Do Cylinder Warp");
    std::shared_ptr<GDCCylinderWarp> cylinder_warp = GDCCylinderWarp::Create();
    cylinder_warp->WarpInit(fc, cc, s_quad_size_pixel, s_quad_size_meters, default_transform);
    cylinder_warp->SetQuadTransform(quad_transform);
    //cylinder_warp->SetQuadRadius(4);
    cylinder_warp->SetToTransform(new_pose_transform);
    Vector2f pixel_position(0, 0);
    cylinder_warp->SetPixelPosition(pixel_position);
    if (!cylinder_warp->DoWarp())
    {
        HERON_LOG_INFO("cylinder DoWarp error");
    }
    if (!cylinder_warp->GetGDCWarpMatrix33(warp_matrix_gdc)) {
        HERON_LOG_INFO("cylinder GetGDCWarpMatrix33 error");
    }
    HERON_LOG_INFO("ALL PASS!");
    framework::util::log::Logger::shutdown();
    return 0;
}
