#!/bin/bash
set -e
BIN=${OBFUSCATE_BIN}

# Define the file should not be obsfuscated
whitelist=(
    "nr_plugin_interface.h"
    "heron/dispatch/15.1.01.090_20241003"
    # Add more files as needed
)
# Construct the grep pattern dynamically
for item in "${whitelist[@]}"; do
    if [[ -n "$grep_pattern" ]]; then
        grep_pattern+="|"
    fi
    grep_pattern+="$item"
done

command="find heron -type f \( -name "*.cc" -o -name "*.cpp" -o -name "*.h" -o -name "*.hpp" \) | grep -vE \"$grep_pattern\" "
# Find files matching specified extensions, excluding those listed in the whitelist
echo "$grep_pattern"
echo "$command"
files_to_obfuscate=$(eval $command)
echo "$files_to_obfuscate"

echo "$files_to_obfuscate" |xargs $BIN > obfuscate.log