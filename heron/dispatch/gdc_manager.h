#pragma once

#include <memory>
#include <map>
#include <vector>

#include <heron/model/model_manager.h>
#include <heron/util/types.h>
#include <heron/util/nr_types_twin.h>

#include <framework/util/singleton.h>

using GDCStatusPtr = std::unique_ptr<heron::GdcInitConfig>;

namespace heron::dispatch
{

    enum GDC_USAGE : int32_t
    {
        GDC_USAGE_INVALID = -1,
        GDC_USAGE_START = 0,
        GDC_USAGE_LEFT_DISPLAY = 0,
        GDC_USAGE_RIGHT_DISPLAY = 1,
        GDC_USAGE_OSD = 2,
        GDC_USAGE_COUNT = 3,
    };
    class GDC
    {
    public:
        enum
        {
            MATRIX_ROW = 3,
            MATRIX_COLUMN = 3,
            MESH_VERTEX_DATA_SIZE = 2,
        };

    public:
        GDC(GDC_USAGE usage);
        ~GDC();

    public:
        Result RenderFrame(const DpFrameData &src_frame, const model::SpaceScreenStatus &status, bool need_reset, uint8_t frame_start);

        void GetMatrixBuffer(uint64_t* phy, void** vir) {
            *phy = matrices_physical_address_;
            *vir = matrices_virtual_address_;
        }

    private:
        void InitStatus();

    private:
        GDC_USAGE usage_;

    private:
        GDCStatusPtr status_;

    private:
        uint64_t matrices_physical_address_{0};
        void *matrices_virtual_address_{nullptr};
        uint64_t mesh_physical_address_{0};
        void *mesh_virtual_address_{nullptr};
        uint64_t identity_mesh_physical_address_{0};
        void *identity_mesh_virtual_address_{nullptr};

        uint64_t weight_physical_address_{0};
        void *weight_virtual_address_{nullptr};

    private:
        uint32_t output_width_{1920};
        uint32_t output_height_{1080};
        uint32_t mesh_num_rows_{0};
        uint32_t mesh_num_columns_{0};
    };

    using GDCPtr = std::unique_ptr<GDC>;
    using GDCPtrMap = std::map<GDC_USAGE, GDCPtr>;

    class GDCManager : public framework::util::Singleton<GDCManager>
    {
    public:
        GDCManager();
        ~GDCManager();
        void Init();

    public:
        Result RenderFrame(GDC_USAGE usage, const DpFrameData &src_frame, const model::SpaceScreenStatus &status, bool need_reset = false, uint8_t frame_start = 0)
        {
            return gdc_map_[usage] ? gdc_map_[usage]->RenderFrame(src_frame, status, need_reset, frame_start) : Result::RESULT_FAILURE;
        }
        void GetMatrixBuffer(GDC_USAGE usage, uint64_t* phy, void** vir)
        {
            *phy = 0;
            *vir = nullptr;
            if (gdc_map_[usage])
                gdc_map_[usage]->GetMatrixBuffer(phy, vir);
        }
        void GetDummyFrame(DpFrameData &dp_frame_data) { dp_frame_data = dummy_frame_; }
        void GetBlankFrame(DpFrameData &dp_frame_data) { dp_frame_data = blank_frame_; }

    public:
        void PopulateDummyDpFrame(uint32_t width, uint32_t height, const std::vector<uint8_t> &color, DpFrameData &dp_frame_data,
                                  void **virtual_address, uint64_t *physical_address, const char *name);

    private:
        GDCPtrMap gdc_map_;
        DpFrameData dummy_frame_;
        uint64_t dummy_frame_physical_address_{0};
        void *dummy_frame_virtual_address_{nullptr};
        DpFrameData blank_frame_;
        uint64_t blank_frame_physical_address_{0};
        void *blank_frame_virtual_address_{nullptr};
    };

} // namespace heron::dispatch
