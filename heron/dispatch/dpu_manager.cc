#include <heron/dispatch/dpu_manager.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/dispatch/dispatcher.h>
#include <heron/control/control_display.h>
#include <heron/control/warpper.h>
#include <heron/model/model_manager.h>
#include <heron/util/math_tools.h>
#include <heron/util/misc.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

static bool s_reseting[heron::DISPLAY_USAGE_COUNT]{false, false};
static uint32_t s_need_reset_cnt[heron::DISPLAY_USAGE_COUNT]{0, 0};
static uint64_t s_frame_count = 0;

static std::vector<std::pair<uint64_t, uint32_t>> s_block_average_interval_ns = std::vector<std::pair<uint64_t, uint32_t>>(11, {0, 0});

#define DPU_FPS_SAMPLE_SIZE 100
static heron::CallCounter s_call_counter[heron::DISPLAY_USAGE_COUNT]{
    heron::CallCounter("dpu0", DPU_FPS_SAMPLE_SIZE, 90.0f, 5.0f),
    heron::CallCounter("dpu1", DPU_FPS_SAMPLE_SIZE, 90.0f, 5.0f)};
static heron::Average s_delay[heron::DISPLAY_USAGE_COUNT]{
    heron::Average("left_callback"), heron::Average("right_callback")};
static heron::Average s_cost[heron::DISPLAY_USAGE_COUNT]{
    heron::Average("left_cost"), heron::Average("right_cost")};

static uint64_t s_last_callback_ns = 0;

static void ProcessResetRequest(NRDisplayUsage display_usage, NRDisplaySubmitType type, const NRDisplaySubmitData *data, XR_VO_CALLBACK_INFO &info)
{
    info.need_reset = false;
    if (DebugManager::GetInstance()->ignore_linebuffer_reset)
        return;
    switch (data->event)
    {
    case NR_DISPLAY_SUBMIT_EVENT_NEED_RESET:
        if (!s_reseting[display_usage])
        {
            s_reseting[display_usage] = true;
            s_need_reset_cnt[display_usage]++;
            info.need_reset = true;
            HERON_LOG_DEBUG("display: {} linebuffer need reset for the {}th time", display_usage == NR_DISPLAY_USAGE_LEFT ? "left" : "right", s_need_reset_cnt[display_usage]);
        }
        break;
    case NR_DISPLAY_SUBMIT_EVENT_RESET_FINISH:
        if (s_reseting[display_usage])
        {
            HERON_LOG_DEBUG("display: {} linebuffer reset finished type:{} id: {}", display_usage == NR_DISPLAY_USAGE_LEFT ? "left" : "right", type, data->block_id);
            s_reseting[display_usage] = false;
        }
        break;
    default:
        break;
    }
    if (display_usage == NR_DISPLAY_USAGE_LEFT && (type & NR_DISPLAY_SUBMIT_TYPE_VSYNC))
    {
        if (s_frame_count % DebugManager::GetInstance()->print_frame_interval == 0)
        {
            HERON_LOG_DEBUG("reset count left:{} right:{}", s_need_reset_cnt[DISPLAY_USAGE_LEFT], s_need_reset_cnt[DISPLAY_USAGE_RIGHT]);
        }
    }
}

static void ProfileCallbackInterval(NRDisplayUsage display_usage, NRDisplaySubmitType type, const NRDisplaySubmitData *data)
{
    if (s_last_callback_ns == 0)
    {
        s_last_callback_ns = data->callback_timestamp_ns;
        return;
    }
    uint64_t time_diff = data->callback_timestamp_ns - s_last_callback_ns;
    s_last_callback_ns = data->callback_timestamp_ns;
    if (type & NR_DISPLAY_SUBMIT_TYPE_VSYNC)
    {
        s_block_average_interval_ns[0].first += time_diff;
        s_block_average_interval_ns[0].second++;
        if (s_frame_count % DebugManager::GetInstance()->print_frame_interval == 0)
        {
            HERON_LOG_DEBUG("callback_interval last to vsync:{:.1f}",
                            s_block_average_interval_ns[0].first / (double)s_block_average_interval_ns[0].second);
        }
    }
    if (type & NR_DISPLAY_SUBMIT_TYPE_INTERVAL_LINE)
    {
        s_block_average_interval_ns[data->block_id + 1].first += time_diff;
        s_block_average_interval_ns[data->block_id + 1].second++;
        if (s_frame_count % DebugManager::GetInstance()->print_frame_interval == 0)
        {
            HERON_LOG_DEBUG("callback_interval last to callback{}:{:.1f}", data->block_id,
                            s_block_average_interval_ns[data->block_id + 1].first / (double)s_block_average_interval_ns[data->block_id + 1].second);
        }
    }
}

namespace heron::dispatch::internal
{
    void OnDisplayCallback(NRDisplayUsage display_usage, NRDisplaySubmitType type, const NRDisplaySubmitData *data, uint32_t data_size)
    {
        DebugManager* dm_ptr = DebugManager::GetInstance();
        int64_t callback_arrive = GetTimeNano();
        s_delay[display_usage].Update(callback_arrive - data->callback_timestamp_ns);
        if (dm_ptr->immediate_return_on_vo_callback)
            return;
        // HERON_LOG_TRACE("{} Display:{} irqType: {}", __FUNCTION__, display_usage == NR_DISPLAY_USAGE_LEFT ? "left" : "right", type);
        XR_VO_CALLBACK_INFO info;
        ProcessResetRequest(display_usage, type, data, info);
        info.irq_timestamp_ns = data->callback_timestamp_ns;
        if (type & NR_DISPLAY_SUBMIT_TYPE_VSYNC)
        {
            info.callback_type = XR_IRQ_TYPE_VSYNC;
            dm_ptr->CheckUnderflow((DisplayUsage)display_usage);
            control::DisplayCtrl::GetInstance()->OnVsync((DisplayUsage)display_usage, info);
            s_call_counter[display_usage].SetExpectedCallCountsPerSecond(model::ModelManager::GetInstance()->GetDisplayExpectedFps());
            s_call_counter[display_usage].Update();
            if (display_usage == NR_DISPLAY_USAGE_LEFT)
                s_frame_count++;
        }
        if (type & NR_DISPLAY_SUBMIT_TYPE_INTERVAL_LINE)
        {
            int32_t total_callbacks = Warpper::GetInstance()->GetTotalCallbacks();
            if ((int)data->block_id >= total_callbacks)
            {
                HERON_LOG_ERROR("invalid block id: {}. expect [0,{}]", data->block_id, total_callbacks - 1);
                return;
            }
            info.callback_type = XR_IRQ_TYPE_INTERVAL_LINE;
            control::DisplayCtrl::GetInstance()->OnBlock((DisplayUsage)display_usage, data->block_id, info);
        }
        if (type & NR_DISPLAY_SUBMIT_TYPE_FRAME_DONE)
        {
            info.callback_type = XR_IRQ_TYPE_FRAME_DONE;
            control::DisplayCtrl::GetInstance()->OnFrameDone((DisplayUsage)display_usage, info);
        }
        if (display_usage == NR_DISPLAY_USAGE_LEFT || dm_ptr->debug_log.timing_detail)
            s_cost[display_usage].Update(GetTimeNano() - callback_arrive);
        if ((!dm_ptr->profile_callback_interval) || (display_usage != NR_DISPLAY_USAGE_LEFT))
            return;
        ProfileCallbackInterval(display_usage, type, data);
    }

} // namespace heron::dispatch::internal

using namespace heron::dispatch;
using namespace heron::model;

void DpuManager::Start()
{
    HERON_LOG_INFO("DpuManager Starting...");
    if (false)
    {
        if (running_)
            return;

        running_ = true;
        overlay0_pushing_thread_ =
            std::thread(&DpuManager::SendFrameToOverlay0, this);
    }
    HERON_LOG_INFO("DpuManager Started.");
}

void DpuManager::Stop()
{
    HERON_LOG_INFO("DpuManager Stopping.");
    if (!running_)
        return;
    running_ = false;
    if (overlay0_pushing_thread_.joinable())
        overlay0_pushing_thread_.join();
    HERON_LOG_INFO("DpuManager Stopped.");
}

bool DpuManager::WarpOSDFrame(OSDFrameInfo *osd_frame_info)
{
    return true;
}

void DpuManager::SendFrameToOverlay0()
{
    HERON_LOG_INFO("SendFrameToOverlay0 not doing anything");
}
