#pragma once

#include <heron/model/model.h>
#include <heron/util/ringbuffer.h>
#include <heron/util/counter.h>

#include <framework/util/singleton.h>

#include <thread>

typedef struct NRDpFrameData NRDpFrameData;
namespace heron::dispatch
{

    class DpManager : public framework::util::Singleton<DpManager>
    {
    public:
        enum
        {
            VI_DEV = 0,
            VI_PIPE = VI_DEV,
            VI_CHN = 0,
        };
        enum
        {
            FRAME_BUFFER_COUNT = 16,
        };
        using FramePool = std::vector<std::unique_ptr<NRDpFrameData>>;
        using FramePtr = NRDpFrameData *;
        using FramePtrVec = std::vector<FramePtr>;

    public:
        DpManager();
        ~DpManager();
        void StartReceiveThread();
        void StopReceiveThread();

    private:
        void Init();
        void Clear();
        void ReceiveFrame();
        void MaybeConfigureGDC(model::FrameInfo* frame_info);

    private:
        FramePtr AllocFrame();
        void FreeFrame(FramePtr frame);

    private:
        std::thread thread_;
        std::atomic<bool> running_{false};

    private:
        FramePool frames_;
        FramePtrVec fresh_frames_;

    public:
        void SetExpectedFps(float fps) { fps_counter_.SetExpectedCallCountsPerSecond(fps); }
        void ResetConsecutiveValidFrameCount() { consecutive_valid_frame_count_ = 0; }

    private:
        int64_t consecutive_valid_frame_count_ = 0;
        bool gdc_configured_on_single_buffer_ = false;

        const uint32_t DP_FPS_SAMPLE_SIZE = 100;
        CallCounter fps_counter_{"dp", DP_FPS_SAMPLE_SIZE, 90.0f, 5.0f};
    };

} // namespace heron::dispatch
