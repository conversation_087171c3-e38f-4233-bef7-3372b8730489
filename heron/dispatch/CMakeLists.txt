PROJECT_INIT_TARGET_VARIABLE(dispatch)
file( GLOB_RECURSE TARGET_HEADER_FILES_EXT "${CMAKE_CURRENT_BINARY_DIR}/*.h" )
file( GLOB_RECURSE TARGET_SOURCE_FILES_EXT "${CMAKE_CURRENT_BINARY_DIR}/*.cc")

add_library( ${TARGET_NAME} OBJECT )

target_sources( ${TARGET_NAME}
	PRIVATE
	${TARGET_HEADER_FILES}
	${TARGET_SOURCE_FILES}
	${TARGET_HEADER_FILES_EXT}
	${TARGET_SOURCE_FILES_EXT}
	)

target_include_directories( ${TARGET_NAME}
	PUBLIC	
	"${EXTERNAL_INCLUDE_DIR}"
	"${TARGET_INCLUDE_PATH}"
	"${PROJECT_BINARY_DIR}"
	"${PROJECT_SOURCE_DIR}"
	)

target_link_libraries(${TARGET_NAME}
		PRIVATE
		interface
        msg
		framework::framework
		warpcore::warpcore
	)