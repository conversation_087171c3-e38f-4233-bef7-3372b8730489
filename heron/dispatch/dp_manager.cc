#include <heron/dispatch/dp_manager.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/dispatch/dispatcher.h>
#include <heron/control/control_display.h>
#include <heron/control/control_dp.h>
#include <heron/model/model_manager.h>
#include <heron/message/m_type_converter.h>
#include <heron/util/warp.h>
#include <heron/util/debug.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>
#include <heron/message/message.h>
#include <heron/interface_provider/device_message.h>

#define FIRST_FRAMES_TO_IGNORE 5

using namespace heron;
using namespace control;
using namespace dispatch;
using namespace model;
using namespace message;
using namespace interface_provider;

DpManager::DpManager()
{
    // HERON_LOG_TRACE("DpManager constructor");
    frames_.reserve(FRAME_BUFFER_COUNT);
    for (int32_t i = 0; i < FRAME_BUFFER_COUNT; ++i)
        frames_.emplace_back(std::make_unique<NRDpFrameData>());
    fresh_frames_.reserve(FRAME_BUFFER_COUNT);
    Init();
}

DpManager::~DpManager() {}

void DpManager::Init()
{
    for (auto &frame : frames_)
        fresh_frames_.emplace_back(frame.get());
}

void DpManager::Clear()
{
    HERON_LOG_INFO("DpManager Clearing");
    for (int32_t i = 0; i < FrameUsage::FRAME_USAGE_COUNT; ++i)
    {
        RingBuffer<FrameInfo> *frame_infos =
            ModelManager::GetInstance()->GetFrameInfos((FrameUsage)i);
        for (int32_t j = 0; j < frame_infos->GetSize(); ++j)
        {
            // HERON_LOG_TRACE("{}clearing: {}th insert and free", i, j);
            FrameInfo *frame_info = nullptr;
            if (frame_infos->IsIndexValid(j))
                frame_info = frame_infos->GetBuffer(j);
            if (!frame_info)
            {
                HERON_LOG_ERROR("GetBufferByIdx: {} error. should not happen", j);
                continue;
            }
            FramePtr old_frame = (FramePtr)frame_info->nr_dp_frame;
            if (old_frame)
            {
                FreeFrame(old_frame);
                frame_info->nr_dp_frame = nullptr;
            }
            else
            {
                HERON_LOG_INFO("old_frame null");
            }
        }
    }
    fresh_frames_.clear();

    // HERON_LOG_TRACE("DpManager init in clearing");
    Init();

    HERON_LOG_INFO("DpManager clear done");
}

void DpManager::StartReceiveThread()
{
    if (running_)
        return;
    DpFrameData dummy_frame;
    GDCManager::GetInstance()->GetDummyFrame(dummy_frame);
    DisplayCtrl::GetInstance()->GdcDirectConfigure(dummy_frame, *ModelManager::GetInstance()->GetSpaceScreenStatus(), false, 1);
    running_ = true;
    thread_ = std::thread(&DpManager::ReceiveFrame, this);
}

void DpManager::StopReceiveThread()
{
    if (!running_)
        return;
    running_ = false;
    if (thread_.joinable())
        thread_.join();
    Clear();
}

// overall logic of receiving frame:
// if (DpGetFrame()) {
//     if(ParseFrame()) {
//         if(ParseEmbeddedData()) {
//             if(PopulateFrameInfoMetadata()) {
//                 SetSceneMode(WithNebula);
//                 PushFrameToBufferQueue();
//             } else {
//                 DiscardFrame();
//             }
//         } else {
//             SetSceneMode(SpaceScreen);
//             PushFrameToBufferQueue();
//         }
//     }
//     else {
//         PushFrameToBufferQueue();(mark frame as invalid)
//     }
// else {
//     continue;
// }
Average s_dp_latency("dp_latency");
Average s_dp_interval("dp_interval");
Average s_recv_frame_cost("recv_frame_cost");
Average s_gen_vsync("gen_vsync");
Average s_send_vsync("send_vsync");
Average s_parse_frame("parse_frame");
Average s_parse_embedded("parse_embedded");
Average s_populate_frame("populate_frame");
uint64_t s_last_dp_rx_done = 0;
void DpManager::ReceiveFrame()
{
    HERON_LOG_INFO("ReceiveFrame started");
#ifdef HERON_SYSTEM_XRLINUX
    pthread_setname_np(pthread_self(), "receive_frame");
#endif
    while (running_)
    {
        RingBuffer<FrameInfo> *frame_infos =
            ModelManager::GetInstance()->GetFrameInfos(FRAME_USAGE_ANY);
        // free frame fist
        FrameInfo *buffer = frame_infos->GetWritableBuffer();
        if (!buffer)
        {
            HERON_LOG_ERROR("dp manager get writable buffer failed! should not happen.");
            continue;
        }
        FramePtr old_frame = static_cast<FramePtr>(buffer->nr_dp_frame);
        // HERON_LOG_TRACE("returning old frame: {}", buffer->nr_dp_frame);
        if (!old_frame)
        {
            HERON_LOG_INFO("old_frame null");
        }
        else
        {
            DispatcherWrapper::GetInstance()->DpReleaseFrame(buffer);
            FreeFrame(old_frame);
        }
        // get frame
        FramePtr frame = AllocFrame();
        buffer->nr_dp_frame = (void *)frame;
        if (!DispatcherWrapper::GetInstance()->DpGetFrame(buffer, 3000))
        {
            // XXX : no need to release nr_dp_frame on error
            FreeFrame(frame);
            // XXX : should not call DoneWriteBuffer() here, because, we didn't actually got the frame.
            // GetWritableBuffer in the next run will return the same index
            continue;
        }

        buffer->metadata.timing.dp_rx_done_ns = GetTimeNano();
        DebugManager* p_dm = DebugManager::GetInstance();
        if (p_dm->sleep_us_after_dp_rx_done > 0)
            usleep(p_dm->sleep_us_after_dp_rx_done);

        int64_t dp_interval = buffer->metadata.timing.dp_rx_done_ns - s_last_dp_rx_done;
        s_last_dp_rx_done = buffer->metadata.timing.dp_rx_done_ns;
        if (p_dm->gen_fake_vsync)
        {
            ByteBuf buf;
            GenerateDpVsyncInfo(buf);
            buffer->metadata.timing.vsync_generated_ns = GetTimeNano();
            DeviceMessageSendInterface::GetInstance()->BroadcastDeviceMessage((const void *)(buf.begin()), buf.size());
            buffer->metadata.timing.vsync_sent_ns = GetTimeNano();
        }
        // DpGetFrame success
        fps_counter_.Update();
        if (!DpCtrl::GetInstance()->ParseFrame(*buffer))
        { // push in to buffer queue anyway when the frame returned by DpGetFrame is considered invalid
            frame_infos->DoneWriteBuffer();
            continue;
        }
        consecutive_valid_frame_count_++;
        if (p_dm->debug_log.frame_data_ext)
        {
            HERON_LOG_DEBUG("ReceiveFrame valid_count:{} data_ext:{}", consecutive_valid_frame_count_, (void *)buffer->dp_frame_data.data_ext[0]);
        }
        DpCtrl::GetInstance()->UpdateSuitableSrcFrameSize(buffer->dp_frame_data.width, buffer->dp_frame_data.height, buffer->space_screen_status, buffer->target_size_factor);
        buffer->metadata.timing.frame_parsed_ns = GetTimeNano();
        FrameEmbeddedInfoSimpleTwin embedded_info_simple;
        FrameEmbeddedInfoTwin embedded_info;
        bool embedded_simple = false;
        bool bw_decode_result = DpCtrl::GetInstance()->ParseEmbeddedData(*buffer, embedded_simple, embedded_info_simple, embedded_info);
        buffer->metadata.timing.embedded_data_parsed_ns = GetTimeNano();
        DpCtrl::GetInstance()->MaybeDumpFrame(*buffer, bw_decode_result); // for debug purpose
        if (!bw_decode_result)
        {
            if (buffer->space_screen_status.validation.bad_view)
                consecutive_valid_frame_count_ = 0;
            buffer->space_screen_status.validation.first_frames_to_ignore = consecutive_valid_frame_count_ < FIRST_FRAMES_TO_IGNORE;
            DpCtrl::GetInstance()->UpdateSceneMode(SCENE_MODE_SPACE_SCREEN);
            ModelManager::GetInstance()->UpdateQuadStatus();
            frame_infos->DoneWriteBuffer(); // consider the frame as normal space_screen frame when failed to get valid embedded data
            MaybeConfigureGDC(buffer);
            continue;
        }
        // got valid embedded data
        if (!DpCtrl::GetInstance()->PopulateFrameInfoMetadata(*buffer, embedded_simple, embedded_info_simple, embedded_info))
        { // discard this frame when embedded data is valid but failed to populate metadata
            DispatcherWrapper::GetInstance()->DpReleaseFrame(buffer);
            FreeFrame(frame);
            // XXX : should not call DoneWriteBuffer() here, because, we want to discard the frame.
            // GetWritableBuffer in the next run will return the same index
            continue;
        }
        // overwrite space_screen_status while no need to modify ori value in ModelManager
        buffer->space_screen_status.scene_mode = SCENE_MODE_WITH_NEBULA;
        buffer->space_screen_status.perception_type = PERCEPTION_TYPE_REMOTE;
        buffer->space_screen_status.validation.bad_view = false; // always consider as good view in SCENE_MODE_WITH_NEBULA
        DpCtrl::GetInstance()->UpdateSceneMode(SCENE_MODE_WITH_NEBULA);
        frame_infos->DoneWriteBuffer();
        MaybeConfigureGDC(buffer);

        // debug timing
        uint64_t now = GetTimeNano();
        if (p_dm->debug_log.timing_detail)
        {
            s_dp_latency.Update(buffer->metadata.timing.dp_rx_done_ns - buffer->dp_frame_data.pts);
            s_dp_interval.Update(dp_interval);
            if (p_dm->gen_fake_vsync)
            {
                s_gen_vsync.Update(buffer->metadata.timing.vsync_generated_ns - buffer->metadata.timing.dp_rx_done_ns);
                s_send_vsync.Update(buffer->metadata.timing.vsync_sent_ns - buffer->metadata.timing.vsync_generated_ns);
                s_parse_frame.Update(buffer->metadata.timing.frame_parsed_ns - buffer->metadata.timing.vsync_sent_ns);
            }
            s_parse_frame.Update(buffer->metadata.timing.frame_parsed_ns - buffer->metadata.timing.dp_rx_done_ns);
            s_parse_embedded.Update(buffer->metadata.timing.embedded_data_parsed_ns - buffer->metadata.timing.frame_parsed_ns);
            s_populate_frame.Update(now - buffer->metadata.timing.embedded_data_parsed_ns);
        }
        s_recv_frame_cost.Update(now - buffer->metadata.timing.dp_rx_done_ns);
    }
    HERON_LOG_INFO("Dp ReceiveFrame thread quit.");
}

void DpManager::MaybeConfigureGDC(FrameInfo *frame_info)
{
    if (!DebugManager::GetInstance()->dp_rx_single_buffer)
        return;
    if (frame_info->space_screen_status.validation.NotToPresent())
    {
        gdc_configured_on_single_buffer_ = false;
        return;
    }
    static uint64_t s_last_ext0_configured = 0;
    if (s_last_ext0_configured == (uint64_t)frame_info->dp_frame_data.data_ext[0])
        return;
    s_last_ext0_configured = (uint64_t)frame_info->dp_frame_data.data_ext[0];
    gdc_configured_on_single_buffer_ = true;
    HERON_LOG_DEBUG("MaybeConfigureGDC {} size_pixel:{}x{}", (void *)frame_info->dp_frame_data.data_ext[0], frame_info->dp_frame_data.width, frame_info->dp_frame_data.height);
    DisplayCtrl::GetInstance()->GdcDirectConfigure(frame_info->dp_frame_data, frame_info->space_screen_status);
}

DpManager::FramePtr DpManager::AllocFrame()
{
    if (fresh_frames_.empty())
    {
        HERON_LOG_ERROR("No free frame");
        return nullptr;
    }
    FramePtr frame = fresh_frames_.back();
    fresh_frames_.pop_back();
    return frame;
}

void DpManager::FreeFrame(FramePtr frame)
{
    // HERON_LOG_TRACE("{} NRDpFrameData ptr: {}", __FUNCTION__, (void *)frame);
    fresh_frames_.push_back(frame); // push the frame anyway

    // HERON_LOG_TRACE("free frame done");
}