#pragma once

#include <framework/util/singleton.h>

#include <heron/interface/device_api/nr_extra_dp.h>
#include <heron/interface/device_api/nr_extra_display.h>
#include <heron/interface/device_api/nr_extra_gdc.h>

using PFN_NRDpGetFrame = NRResult (*)(NRDpFrameData *nr_dp_frame, uint32_t timeout_ms);
using PFN_NRDpReleaseFrame = NRResult (*)(const void *frame_info);
using PFN_NRDpResizeFrame = NRResult (*)(uint32_t width, uint32_t height);

using PFN_NRDisplayAllocOverlayFrame = NRResult (*)(NROverlayFrameData *);
using PFN_NRDisplayDeallocOverlayFrame = NRResult (*)(NROverlayFrameData *);
using PFN_NRDisplaySendOverlayFrame = NRResult (*)(NRDisplayUsage display_usage, NROverlayFrameData *);
using PFN_NRLocalDisplayAllocOverlayFrame = NRResult (*)(NROverlayFrameData *);
using PFN_NRLocalDisplayDeallocOverlayFrame = NRResult (*)(NROverlayFrameData *);
using PFN_NRLocalDisplaySendOverlayFrame = NRResult (*)(NRDisplayUsage display_usage, NROverlayFrameData *);

using PFN_NRMmzAlloc = NRResult (*)(void **physical_addr, void **virtual_addr, const char *name, const char *zone_name, uint32_t length);
using PFN_NRMmzFree = NRResult (*)(void *physical_addr, void *virtual_addr);

using PFN_NRLoadAR94APIs = NRResult (*)(bool log_all_level);
using PFN_NRInitBoardContext = NRResult (*)();

using PFN_NRReleaseBoardContext = NRResult (*)();
using PFN_NRDisplayConfigService = NRResult (*)(const NRDisplayConfig *display_config);
using PFN_NRDisplaySetCallback = NRResult (*)(NRDisplaySubmit_DispatchCallback callback);

using PFN_NRDisplayStartOSDRender = NRResult (*)(NRDisplayUsage display_usage, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height, NRFrameBufferFormat format);
using PFN_NRDisplayStopOSDRender = NRResult (*)();
using PFN_NRLocalDisplayStartOSDRender = NRResult (*)(NRDisplayUsage display_usage, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height, NRFrameBufferFormat format);
using PFN_NRLocalDisplayStopOSDRender = NRResult (*)();

using PFN_NRGdcInit = NRResult (*)(const NRGdcInitConfig *config);
using PFN_NRGdcProcess = NRResult (*)(const NRGdcFrameConfig *config, bool need_reset);
using PFN_NRGdcProcessDiscardOnBusy = NRResult (*)(const NRGdcFrameConfig *config, bool need_reset);
using PFN_NRLocalGdcInit = NRResult (*)(const NRGdcInitConfig *config);
using PFN_NRLocalGdcProcess = NRResult (*)(const NRGdcFrameConfig *config, bool need_reset);

using PFN_NRDisplaySetScreenEnableBsp = NRResult (*)(NREnableValue);

using PFN_XR_ar_hal_sys_mmz_alloc = NRResult (*)(void **physical_addr, void **virtual_addr, const char *name, const char *zone_name, uint32_t length);
using PFN_XR_ar_hal_sys_mmz_alloc_cached = NRResult (*)(void **physical_addr, void **virtual_addr, const char *name, const char *zone_name, uint32_t length);
using PFN_XR_ar_hal_sys_mmz_flush_cache = NRResult (*)(void *physical_addr, void *virtual_addr, uint32_t size);

namespace heron::dispatch
{

    class Dispatcher : public framework::util::Singleton<Dispatcher>
    {
    public:
        Dispatcher();
        void LoadLibraries();

    public:
        PFN_NRDisplayStartOSDRender NRDisplayStartOSDRender{nullptr};
        PFN_NRDisplayStopOSDRender NRDisplayStopOSDRender{nullptr};

        PFN_NRDpGetFrame NRDpGetFrame{nullptr};
        PFN_NRDpReleaseFrame NRDpReleaseFrame{nullptr};
        PFN_NRDpResizeFrame NRDpResizeFrame{nullptr};

        PFN_NRDisplayAllocOverlayFrame NRDisplayAllocOverlayFrame{nullptr};
        PFN_NRDisplayDeallocOverlayFrame NRDisplayDeallocOverlayFrame{nullptr};
        PFN_NRDisplaySendOverlayFrame NRDisplaySendOverlayFrame{nullptr};

        PFN_NRDisplayConfigService NRDisplayConfigService{nullptr};
        PFN_NRDisplaySetCallback NRDisplaySetCallback{nullptr};

        PFN_NRGdcInit NRGdcInit{nullptr};
        PFN_NRGdcProcess NRGdcProcess{nullptr};
        PFN_NRGdcProcessDiscardOnBusy NRGdcProcessDiscardOnBusy{nullptr};
        PFN_NRMmzAlloc NRMmzAlloc{nullptr};
        PFN_NRMmzFree NRMmzFree{nullptr};

        PFN_NRDisplaySetScreenEnableBsp NRDisplaySetScreenEnableBsp{nullptr};

        PFN_NRLoadAR94APIs NRLoadAR94APIs{nullptr};
        PFN_NRInitBoardContext NRInitBoardContext{nullptr};
        PFN_NRLocalGdcInit NRLocalGdcInit{nullptr};
        PFN_NRLocalGdcProcess NRLocalGdcProcess{nullptr};

        PFN_NRLocalDisplayStartOSDRender NRLocalDisplayStartOSDRender{nullptr};
        PFN_NRLocalDisplayStopOSDRender NRLocalDisplayStopOSDRender{nullptr};
        PFN_NRLocalDisplayAllocOverlayFrame NRLocalDisplayAllocOverlayFrame{nullptr};
        PFN_NRLocalDisplayDeallocOverlayFrame NRLocalDisplayDeallocOverlayFrame{nullptr};
        PFN_NRLocalDisplaySendOverlayFrame NRLocalDisplaySendOverlayFrame{nullptr};

        PFN_XR_ar_hal_sys_mmz_alloc XR_ar_hal_sys_mmz_alloc{nullptr};
        PFN_XR_ar_hal_sys_mmz_alloc_cached XR_ar_hal_sys_mmz_alloc_cached{nullptr};
        PFN_XR_ar_hal_sys_mmz_flush_cache XR_ar_hal_sys_mmz_flush_cache{nullptr};
    };

} // namespace heron::dispatch
