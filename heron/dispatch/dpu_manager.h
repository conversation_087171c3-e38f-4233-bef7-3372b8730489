#pragma once

#include <heron/model/model.h>
#include <heron/util/counter.h>

#include <framework/util/singleton.h>

#include <atomic>
#include <thread>
#include <string>
namespace heron::dispatch
{

    class DpuManager : public framework::util::Singleton<DpuManager>
    {
    public:
        void Start();
        void Stop();

    private:
        std::thread overlay0_pushing_thread_;
        std::atomic<bool> running_{false};

    private:
        void SendFrameToOverlay0();
        bool WarpOSDFrame(model::OSDFrameInfo *osd_frame_info);
    };

} // namespace heron::dispatch
