#pragma once

#include <framework/util/singleton.h>

#include <stdint.h>

namespace heron::interface_provider
{
    class FileInterface : public framework::util::Singleton<FileInterface>
    {
    public:
        void GetInterfaceInstance(void *interfaces);

        bool RegisterProvider(
            const void *provider,
            uint32_t provider_size);

        bool GetUserConfigDirectory(
            const char **user_config_directory,
            uint32_t *directory_size);

        bool SafeReadBufferFromFile(
            const char *file_path,
            uint32_t path_size,
            uint32_t *out_buffer_size,
            char *out_buffer_data);

        bool SafeSaveBufferToFile(
            const char *buffer_data,
            uint32_t buffer_size,
            const char *file_path,
            uint32_t path_size);

        bool SafeRemoveFile(
            const char *file_path,
            uint32_t path_size);

        bool GetDefaultHomeDirectory(
            const char **default_home_directory,
            uint32_t *directory_size);
    };

} // namespace heron::interface_provider