#include <heron/interface_provider/device_message.h>
#include <heron/interface_provider/common_macro.h>

#include <include/common/nr_plugin_device_message.h>
#include <heron/util/nr_type_converter.h>
#include <heron/util/log.h>

#include <framework/util/plugin_util.h>

extern NRPluginHandle heron_g_handle;

static std::unique_ptr<NRDeviceMessageSendInterface> s_interface = nullptr;
namespace heron::interface_provider
{
    void DeviceMessageSendInterface::GetInterfaceInstance(void *interfaces)
    {
        s_interface = framework::util::GetInterface<NRDeviceMessageSendInterface>(static_cast<NRInterfaces *>(interfaces));
        if (!s_interface)
        {
            fprintf(stderr, "error on plugin load: get %s fail. going to abort", "NRDeviceMessageSendInterface");
            usleep(500 * 1000);
            std::abort();
        }
    }

    bool DeviceMessageSendInterface::RegisterProvider(
        const void *provider,
        uint32_t provider_size)
    {
        CALL_INTERFACE_API(NRDeviceMessageSendInterface, RegisterProvider, true, static_cast<const NRDeviceMessageHandleProvider *>(provider), provider_size);
        return true;
    }

    bool DeviceMessageSendInterface::SendDeviceMessage(
        int32_t connid,
        const void *data,
        uint32_t size)
    {
        CALL_INTERFACE_API(NRDeviceMessageSendInterface, SendDeviceMessage, true, connid, data, size);
        return true;
    }

    bool DeviceMessageSendInterface::BroadcastDeviceMessage(
        const void *data,
        uint32_t size)
    {
        CALL_INTERFACE_API(NRDeviceMessageSendInterface, BroadcastDeviceMessage, true, data, size);
        return true;
    }

} // namespace heron::interface_provider