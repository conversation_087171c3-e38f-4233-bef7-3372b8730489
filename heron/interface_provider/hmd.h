#pragma once

#include <heron/util/types.h>

#include <framework/util/singleton.h>

namespace heron::interface_provider
{
    class HMDInterface : public framework::util::Singleton<HMDInterface>
    {
    public:
        void GetInterfaceInstance(void *interfaces);

        bool GetComponentFov(
            Component component,
            Fov4f *out_fov);
        bool GetComponentPoseFromHead(
            Component component,
            Transform *out_transform);
        bool GetComponentExtrinsic(
            Component base_component,
            Component target_component,
            Transform *out_extrinsic);
        bool GetComponentDisplayDistortionSize(
            Component component,
            Vector2i *distortion_size);
        bool GetComponentDisplayDistortionData(
            Component component,
            int size,
            float *data);
    };
} // namespace heron::interface_provider