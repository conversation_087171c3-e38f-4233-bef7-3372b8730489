#include <heron/interface_provider/misc.h>
#include <heron/interface_provider/common_macro.h>
#include <heron/util/log.h>

#include <heron/interface/include/misc/nr_plugin_misc.h>

#include <framework/util/plugin_util.h>

extern NRPluginHandle heron_g_handle;

static std::unique_ptr<NRMiscInterface> s_interface = nullptr;
namespace heron::interface_provider
{
    void MiscInterface::GetInterfaceInstance(void *interfaces)
    {
        s_interface = framework::util::GetInterface<NRMiscInterface>(static_cast<NRInterfaces *>(interfaces));
        if (!s_interface)
        {
            fprintf(stderr, "error on plugin load: get %s fail. going to abort", "NRMiscInterface");
            usleep(500 * 1000);
            std::abort();
        }
    }

    bool MiscInterface::RegisterProvider(
        const void *provider,
        uint32_t provider_size)
    {
        CALL_INTERFACE_API(NRMiscInterface, RegisterProvider, true, static_cast<const NRMiscProvider*>(provider), provider_size);
        return true;
    }
} // namespace heron::interface_provider
