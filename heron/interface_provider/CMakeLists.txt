PROJECT_INIT_TARGET_VARIABLE(interface_provider)

add_library( ${TARGET_NAME} OBJECT )

target_sources( ${TARGET_NAME}
	PRIVATE
	${TARGET_HEADER_FILES}
	${TARGET_SOURCE_FILES}
	${TARGET_HEADER_FILES_EXT}
	${TARGET_SOURCE_FILES_EXT}
	)

target_include_directories( ${TARGET_NAME}
	PUBLIC	
	"${EXTERNAL_INCLUDE_DIR}"
	"${TARGET_INCLUDE_PATH}"
	"${PROJECT_BINARY_DIR}"
	"${PROJECT_SOURCE_DIR}"
	)

target_link_libraries(${TARGET_NAME}
	PUBLIC
	interface
	framework::framework
	warpcore::warpcore
	)