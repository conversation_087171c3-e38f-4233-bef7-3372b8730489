#pragma once

#include <heron/util/types.h>

#include <framework/util/singleton.h>

namespace heron::interface_provider
{
    class DeviceMessageSendInterface : public framework::util::Singleton<DeviceMessageSendInterface>
    {
    public:
        void GetInterfaceInstance(void *interfaces);

        bool RegisterProvider(
            const void *provider,
            uint32_t provider_size);

        bool SendDeviceMessage(
            int32_t connid,
            const void * data,
            uint32_t size);
        bool BroadcastDeviceMessage(
            const void * data,
            uint32_t size);
    };

} // namespace heron::interface_provider