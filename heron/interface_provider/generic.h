#pragma once

#include <heron/util/types.h>

#include <framework/util/singleton.h>

namespace heron::interface_provider
{

    class GenericInterface : public framework::util::Singleton<GenericInterface>
    {
    public:
        void GetInterfaceInstance(void *interfaces);

        bool GetGlobalConfig(const char **data, uint32_t *size);
        bool GetDeviceConfig(const char **data, uint32_t *size);
        bool GetAppDefaultConfig(const char **data, uint32_t *data_size);
        bool GetDeviceDefaultConfig(const char **data, uint32_t *data_size);
        bool GetDisplayResolutionInfo(ResolutionInfo *display_resolution);
    };

} // namespace heron::interface_provider