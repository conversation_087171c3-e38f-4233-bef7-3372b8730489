#define CALL_INTERFACE_API(interface, name, print_error, ...)              \
    if (!s_interface->name)                                                \
    {                                                                      \
        HERON_LOG_ERROR("{} in {} is null", #name, #interface);            \
        return false;                                                      \
    }                                                                      \
    NRPluginResult ret = s_interface->name(heron_g_handle, ##__VA_ARGS__); \
    if (NR_PLUGIN_RESULT_SUCCESS != ret)                                   \
    {                                                                      \
        if (print_error)                                                   \
        {                                                                  \
            HERON_LOG_ERROR("{}->{} error: {}", #interface, #name, ret);   \
        }                                                                  \
        return false;                                                      \
    }

#define CALL_INTERFACE_API_NO_RETURN_ON_ERROR(interface, name, print_error, ...) \
    NRPluginResult ret = NR_PLUGIN_RESULT_FAILURE;                               \
    if (!s_interface->name)                                                      \
    {                                                                            \
        HERON_LOG_ERROR("{} in {} is null", #name, #interface);                  \
    }                                                                            \
    else                                                                         \
    {                                                                            \
        ret = s_interface->name(heron_g_handle, ##__VA_ARGS__);                  \
    }
