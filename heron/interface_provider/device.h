#pragma once

#include <heron/util/types.h>

#include <framework/util/singleton.h>

namespace heron::interface_provider
{
    class DeviceInterface : public framework::util::Singleton<DeviceInterface>
    {
    public:
        void GetInterfaceInstance(void *interfaces);

        bool GetEcLevelCount(int32_t *out_ec_level_count);
        bool GetEcLevel(int32_t *out_ec_level);
        bool UpdateEcLevel(OperationType operation_type);
    };
} // namespace heron::interface_provider
