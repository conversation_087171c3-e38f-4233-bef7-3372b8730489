#include <heron/interface_provider/file.h>
#include <heron/interface_provider/common_macro.h>
#include <heron/util/log.h>

#include <include/common/nr_plugin_file.h>

#include <framework/util/plugin_util.h>

extern NRPluginHandle heron_g_handle;

static std::unique_ptr<NRFileInterface> s_interface = nullptr;
namespace heron::interface_provider
{
    void FileInterface::GetInterfaceInstance(void *interfaces)
    {
        s_interface = framework::util::GetInterface<NRFileInterface>(static_cast<NRInterfaces *>(interfaces));
        if (!s_interface)
        {
            fprintf(stderr, "error on plugin load: get %s fail. going to abort", "NRFileInterface");
            usleep(500 * 1000);
            std::abort();
        }
    }

    bool FileInterface::RegisterProvider(
        const void *provider,
        uint32_t provider_size)
    {
        CALL_INTERFACE_API(NRFileInterface, RegisterProvider, true, static_cast<const NRFileProvider*>(provider), provider_size);
        return true;
    }
    bool FileInterface::GetUserConfigDirectory(
        const char **user_config_directory,
        uint32_t *directory_size)
    {
        CALL_INTERFACE_API(NRFileInterface, GetUserConfigDirectory, true, user_config_directory, directory_size);
        return true;
    }

    bool FileInterface::SafeReadBufferFromFile(
        const char *file_path,
        uint32_t path_size,
        uint32_t *out_buffer_size,
        char *out_buffer_data)
    {
        CALL_INTERFACE_API(NRFileInterface, SafeReadBufferFromFile, false, file_path, path_size, out_buffer_size, out_buffer_data);
        return true;
    }

    bool FileInterface::SafeSaveBufferToFile(
        const char *buffer_data,
        uint32_t buffer_size,
        const char *file_path,
        uint32_t path_size)
    {
        CALL_INTERFACE_API(NRFileInterface, SafeSaveBufferToFile, true, buffer_data, buffer_size, file_path, path_size);
        return true;
    }

    bool FileInterface::SafeRemoveFile(
        const char *file_path,
        uint32_t path_size)
    {
        CALL_INTERFACE_API(NRFileInterface, SafeRemoveFile, true, file_path, path_size);
        return true;
    }

    bool FileInterface::GetDefaultHomeDirectory(
        const char **default_config_directory,
        uint32_t *directory_size)
    {
        CALL_INTERFACE_API(NRFileInterface, GetDefaultHomeDirectory, true, default_config_directory, directory_size);
        return true;
    }

} // namespace heron::interface_provider
