#pragma once

#include <framework/util/singleton.h>

#include <stdint.h>

namespace heron::interface_provider
{
    class MiscInterface : public framework::util::Singleton<MiscInterface>
    {
    public:
        void GetInterfaceInstance(void *interfaces);

        bool RegisterProvider(
            const void *provider,
            uint32_t provider_size);
    };

} // namespace heron::interface_provider
