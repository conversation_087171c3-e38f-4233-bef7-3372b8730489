#include <heron/interface_provider/device.h>
#include <heron/interface_provider/common_macro.h>
#include <heron/util/nr_type_converter.h>
#include <heron/interface/include/osd/nr_plugin_device.h>
#include <heron/util/log.h>

#include <framework/util/plugin_util.h>

extern NRPluginHandle heron_g_handle;

static std::unique_ptr<NRDeviceInterface> s_interface = nullptr;

namespace heron::interface_provider
{
    void DeviceInterface::GetInterfaceInstance(void *interfaces)
    {
        s_interface = framework::util::GetInterface<NRDeviceInterface>(static_cast<NRInterfaces *>(interfaces));
        if (!s_interface)
        {
            fprintf(stderr, "error on plugin load: get %s fail. going to abort", "NRDeviceInterface");
            usleep(500 * 1000);
            std::abort();
        }
    }

    bool DeviceInterface::GetEcLevelCount(int32_t *out_ec_level_count) {
        CALL_INTERFACE_API(NRDeviceInterface, GetEcLevelCount, true, out_ec_level_count);
        return true;
    }
    bool DeviceInterface::GetEcLevel(int32_t *out_ec_level) {
        CALL_INTERFACE_API(NRDeviceInterface, GetEcLevel, true, out_ec_level);
        return true;
    }
    bool DeviceInterface::UpdateEcLevel(OperationType operation_type) {
        CALL_INTERFACE_API(NRDeviceInterface, UpdateEcLevel, true, (NROperationType)operation_type);
        return true;
    }
} // namespace heron::interface_provider