#pragma once

#include "nr_plugin_interface.h"
#include "nr_plugin_types.h"
#include "nr_plugin_glasses_types.h"
typedef struct NRKeyEventProvider {

    NRPluginResult(NR_INTERFACE_API *NotifyKeyState)(
        NRPluginHandle handle,
        const NRKeyStateData * data,
        uint32_t data_size
    );
} NRKeyEventProvider;

NR_DECLARE_INTERFACE(NRKeyEventInterface) {

    NRPluginResult(NR_INTERFACE_API *RegisterProvider)(
        NRPluginHandle handle,
        const NRKeyEventProvider * provider,
        uint32_t provider_size
    );
};

NR_REGISTER_INTERFACE_GUID(0x3E40F4B183E34862ULL, 0xAB5A144A3629EB73ULL,
                            NRKeyEventInterface)

