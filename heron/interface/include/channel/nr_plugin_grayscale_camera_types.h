#pragma once

#include "nr_plugin_types.h"

#ifdef NRAPP

#include "nr_plugin_grayscale_camera_types.inc"

#else

	/*
		
		/// @brief 灰度摄像头图像的像素格式
		/// \n NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_UNKNOWN: 未知格式
		/// \n NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_YUV_420_888: YUV420格式
		/// \n NR_CAMERA_PIXEL_FORMAT_RGB_BAYER_8BPP: bayer raw8格式
		
	*/
NR_PLUGIN_ENUM8(NRGrayscaleCameraPixelFormat) {
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_UNKNOWN = 0,
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_YUV_420_888,
    NR_GRAYSCALE_CAMERA_PIXEL_FORMAT_RGB_BAYER_8BPP,
};

	/*
		
		/// @brief 灰度摄像头的 ID，最多支持 4 个
		/// \n NR_GRAYSCALE_CAMERA_ID_0: 摄像头 0
		/// \n NR_GRAYSCALE_CAMERA_ID_1: 摄像头 1
		/// \n NR_GRAYSCALE_CAMERA_ID_2: 摄像头 2
		/// \n NR_GRAYSCALE_CAMERA_ID_3: 摄像头 3
		
	*/
NR_PLUGIN_ENUM8(NRGrayscaleCameraID) {
    NR_GRAYSCALE_CAMERA_ID_0 = 0x0001,
    NR_GRAYSCALE_CAMERA_ID_1 = 0x0002,
    NR_GRAYSCALE_CAMERA_ID_2 = 0x0004,
    NR_GRAYSCALE_CAMERA_ID_3 = 0x0008,
};

#pragma pack(1)
	/*
		
		/// @brief 每个摄像头的图像的信息数据
		/// \n rolling_shutter_time: 图像刷一帧的时间, 如果不是 rolling shutter 摄像头, 该值为 0
		/// \n exposure_start_time : 图像的曝光起始时间
		/// \n exposure_start_time_sys : 图像的曝光起始时间在当前系统的对时时间，由 SDK 处理
		
	*/
typedef struct NRGrayscaleCameraUnitData {
    union {
        struct {
            uint32_t offset;
            NRGrayscaleCameraID camera_id;
            uint32_t width;
            uint32_t height;
            uint32_t stride;
            uint64_t exposure_start_time_device;
            uint32_t exposure_duration;
            uint32_t rolling_shutter_time;
            uint32_t gain;
            uint64_t exposure_start_time_system;
        };
        uint8_t padding[64];
    };

} NRGrayscaleCameraUnitData;

	/*
		
		/// @brief 每帧图像结构体
		
	*/
typedef struct NRGrayscaleCameraFrameData {
    union {
        struct {
            NRGrayscaleCameraUnitData cameras[4];
            uint8_t * data;
            uint32_t data_bytes;
            uint8_t camera_count;
            uint32_t frame_id;
            NRGrayscaleCameraPixelFormat pixel_format;
        };
        uint8_t padding[320];
    };

} NRGrayscaleCameraFrameData;

#pragma pack()

#endif // NRAPP