#pragma once

#include "nr_plugin_interface.h"
#include "nr_plugin_device_types.h"
#include "nr_plugin_glasses_types.h"
#include "nr_plugin_rgb_camera_types.h"
NR_DECLARE_INTERFACE(NRDeviceStateInterface) {

    NRPluginResult(NR_INTERFACE_API *GetDpWorkingState)(
        NRPluginHandle handle,
        NRDpWorkingState * out_dp_working_state
    );

    NRPluginResult(NR_INTERFACE_API *GetRgbCameraPluginState)(
        NRPluginHandle handle,
        NRRgbCameraPluginState * state
    );
};

NR_REGISTER_INTERFACE_GUID(0xEE5C7861602945CDULL, 0x8FBA9BF8E1AF5A9EULL,
                            NRDeviceStateInterface)

NR_DECLARE_INTERFACE(NRDeviceInterface) {
	/*
		
		glasses相关
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetBrightnessLevelCount)(
        NRPluginHandle handle,
        int32_t * out_level_count
    );

    NRPluginResult(NR_INTERFACE_API *GetBrightnessLevel)(
        NRPluginHandle handle,
        int32_t * out_level
    );

    NRPluginResult(NR_INTERFACE_API *UpdateBrightnessLevel)(
        NRPluginHandle handle,
        NROperationType operation_type
    );
	/*
		
		亮度增强
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetBrightnessEnhance)(
        NRPluginHandle handle,
        bool * out_enable
    );

    NRPluginResult(NR_INTERFACE_API *SetBrightnessEnhance)(
        NRPluginHandle handle,
        bool enable
    );

    NRPluginResult(NR_INTERFACE_API *GetEcLevelCount)(
        NRPluginHandle handle,
        int32_t * out_ec_level_count
    );

    NRPluginResult(NR_INTERFACE_API *GetEcLevel)(
        NRPluginHandle handle,
        int32_t * out_ec_level
    );

    NRPluginResult(NR_INTERFACE_API *UpdateEcLevel)(
        NRPluginHandle handle,
        NROperationType operation_type
    );
	/*
		
		<ifunction name="GetAudioModeCount">
		<param name="handle" type="NRPluginHandle"/>
		<param name="out_count" type="int32_t *"/>
		</ifunction>
		out_list设置为null时，即为获取list的count
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetAudioModeList)(
        NRPluginHandle handle,
        int32_t * in_out_count,
        NRAudioMode * out_list
    );

    NRPluginResult(NR_INTERFACE_API *GetAudioCurrentMode)(
        NRPluginHandle handle,
        NRAudioMode * out_mode
    );

    NRPluginResult(NR_INTERFACE_API *SetAudioCurrentMode)(
        NRPluginHandle handle,
        NRAudioMode mode
    );

    NRPluginResult(NR_INTERFACE_API *GetDpVolumeLevelCount)(
        NRPluginHandle handle,
        int32_t * out_level_count
    );

    NRPluginResult(NR_INTERFACE_API *GetDpVolumeLevel)(
        NRPluginHandle handle,
        int32_t * out_level
    );

    NRPluginResult(NR_INTERFACE_API *UpdateVolumeLevel)(
        NRPluginHandle handle,
        NROperationType operation_type
    );
	/*
		<ifunction name="DecreaseVolume">
		<param name="handle" type="NRPluginHandle"/>
		</ifunction> 
	*/
    NRPluginResult(NR_INTERFACE_API *GetColorCalibrationType)(
        NRPluginHandle handle,
        NRDisplayColorCalibrationType * type
    );

    NRPluginResult(NR_INTERFACE_API *SetColorCalibrationType)(
        NRPluginHandle handle,
        NRDisplayColorCalibrationType type
    );

    NRPluginResult(NR_INTERFACE_API *GetColorTemperatureLevelCount)(
        NRPluginHandle handle,
        int32_t * out_level_count
    );
	/*
		<ifunction name="GetColorTemperatureLevelDes">
		<param name="handle" type="NRPluginHandle"/>
		<param name="level_index" type="int32_t"/>
		<param name="out_description" type="const char **"/>
		<param name="out_description_size" type="int32_t *"/>
		</ifunction> 
	*/
    NRPluginResult(NR_INTERFACE_API *GetColorTemperatureLevel)(
        NRPluginHandle handle,
        int32_t * out_level_index
    );

    NRPluginResult(NR_INTERFACE_API *SetColorTemperatureLevel)(
        NRPluginHandle handle,
        int32_t level_index
    );

    NRPluginResult(NR_INTERFACE_API *GetGlassesModelName)(
        NRPluginHandle handle,
        const char ** out_model_name,
        int32_t * out_name_size
    );

    NRPluginResult(NR_INTERFACE_API *GetGlassesSN)(
        NRPluginHandle handle,
        const char ** out_glasses_sn,
        int32_t * out_sn_size
    );

    NRPluginResult(NR_INTERFACE_API *GetSystemVersion)(
        NRPluginHandle handle,
        const char ** out_system_version,
        int32_t * out_version_size
    );

    NRPluginResult(NR_INTERFACE_API *GetHWVersion)(
        NRPluginHandle handle,
        const char ** out_hw_version,
        int32_t * out_version_size
    );

    NRPluginResult(NR_INTERFACE_API *GetDspVersion)(
        NRPluginHandle handle,
        const char ** out_dsp_version,
        int32_t * out_version_size
    );
	/*
		
		level_value_list为null时，获取count
		level_value_list的单位是秒 
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetTimeBeforePowerSaveLevelList)(
        NRPluginHandle handle,
        int32_t * in_out_level_count,
        int32_t * level_value_list
    );

    NRPluginResult(NR_INTERFACE_API *GetTimeBeforePowerSaveLevel)(
        NRPluginHandle handle,
        int32_t * out_level
    );

    NRPluginResult(NR_INTERFACE_API *SetTimeBeforePowerSaveLevel)(
        NRPluginHandle handle,
        int32_t level
    );

    NRPluginResult(NR_INTERFACE_API *GetRgbCameraSN)(
        NRPluginHandle handle,
        const char ** out_rgb_camera_sn,
        int32_t * out_sn_size
    );
	/*
		
		开关屏
		screen_state: NR_ENABLE_VALUE_DISABLE表示关屏，NR_ENABLE_VALUE_ENABLE表示开屏
		method: NR_DISPLAY_SCREEN_ENABLE_METHOD_STEP_BY_STEP表示渐变开/关屏，NR_DISPLAY_SCREEN_ENABLE_METHOD_DIRECT表示直接开/关屏
		step_count: 表示渐变开/关屏的渐变步数
		last_time: 表示渐变开/关屏的持续时间，单位是秒
		
	*/
    NRPluginResult(NR_INTERFACE_API *SetDisplayScreenState)(
        NRPluginHandle handle,
        NREnableValue screen_state,
        NRDisplayScreenEnableMethod method,
        int32_t step_count,
        float last_time
    );

    NRPluginResult(NR_INTERFACE_API *SetStartupFinished)(
        NRPluginHandle handle
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent1)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id,
        int32_t param
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent2)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id,
        int32_t param,
        int32_t param2
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent3)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id,
        int32_t param,
        int32_t param2,
        int32_t param3
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent4)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id,
        int32_t param,
        int32_t param2,
        int32_t param3,
        int32_t param4
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent5)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id,
        int32_t param,
        int32_t param2,
        int32_t param3,
        int32_t param4,
        int32_t param5
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent6)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id,
        int32_t param,
        int32_t param2,
        int32_t param3,
        int32_t param4,
        int32_t param5,
        int32_t param6
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent7)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id,
        int32_t param,
        int32_t param2,
        int32_t param3,
        int32_t param4,
        int32_t param5,
        int32_t param6,
        int32_t param7
    );

    NRPluginResult(NR_INTERFACE_API *ReportEvent8)(
        NRPluginHandle handle,
        NRStatsCategoryID category_id,
        NRStatsEventID event_id,
        int32_t param,
        int32_t param2,
        int32_t param3,
        int32_t param4,
        int32_t param5,
        int32_t param6,
        int32_t param7,
        int32_t param8
    );
	/*
		
		/// @brief 设置 dp 数据过滤模式数量
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetDpDataFilterModeCount)(
        NRPluginHandle handle,
        int32_t * count
    );
	/*
		
		/// @brief 获取 dp 数据过滤模式
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetDpDataFilterMode)(
        NRPluginHandle handle,
        NRDpDataFilterMode * filter_mode
    );
	/*
		
		/// @brief 设置 dp 数据过滤模式
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *SetDpDataFilterMode)(
        NRPluginHandle handle,
        NRDpDataFilterMode filter_mode
    );
	/*
		
		/// @brief 获取电致变色的模式
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetEcAdjustMode)(
        NRPluginHandle handle,
        NREcAdjustMode * out_ec_adjust_mode
    );
	/*
		
		/// @brief 设置电致变色的模式
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *SetEcAdjustMode)(
        NRPluginHandle handle,
        NREcAdjustMode ec_adjust_mode
    );
	/*
		
		/// @brief 设置对 host 的网络状态
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *SetUsbNetworkEnable)(
        NRPluginHandle handle,
        NREnableValue enable_value
    );
	/*
		
		/// @brief 获取对host 的网络状态
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetUsbNetworkEnable)(
        NRPluginHandle handle,
        NREnableValue * enable_value
    );
	/*
		
		/// @brief 重启眼镜
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *RebootGlasses)(
        NRPluginHandle handle
    );
	/*
		
		/// @brief 获取音频的算法
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetAudioAlgorithm)(
        NRPluginHandle handle,
        NRAudioAlgorithmType * out_algorithm_type
    );
	/*
		
		/// @brief 设置音频的算法
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *SetAudioAlgorithm)(
        NRPluginHandle handle,
        NRAudioAlgorithmType algorithm_type
    );
};

NR_REGISTER_INTERFACE_GUID(0xEE150151BC0B4DB5ULL, 0x98FA4527D168A056ULL,
                            NRDeviceInterface)

