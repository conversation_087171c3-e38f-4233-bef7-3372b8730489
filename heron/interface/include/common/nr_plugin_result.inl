#pragma once

#ifdef NRAPP

#include "nr_plugin_result.inc"

#else


NR_PLUGIN_ENUM(NRPluginResult) {
    NR_PLUGIN_RESULT_SUCCESS = 0,
    NR_PLUGIN_RESULT_FAILURE = 1,
    NR_PLUGIN_RESULT_INVALID_ARGUMENT = 2,
    NR_PLUGIN_RESULT_NOT_ENOUGH_MEMORY = 3,
    NR_PLUGIN_RESULT_UNSUPPORTED = 4,
    NR_PLUGIN_RESULT_GLASSES_DISCONNECTED = 5,
    NR_PLUGIN_RESULT_SDK_VERSION_MISMATCHED = 6,
    NR_PLUGIN_RESULT_NO_PERMISSION_VISITING_FILE_SYSTEM = 7,
    NR_PLUGIN_RESULT_CAN_NOT_FIND_RGB_SENSOR = 8,
    NR_PLUGIN_RESULT_CAN_NOT_FIND_DISPLAY_SCREEN = 9,
    NR_PLU<PERSON>N_RESULT_NO_PERCEPTION = 10,
    NR_PLUGIN_RESULT_GET_DISPLAY_FAILURE = 11,
    NR_PLUGIN_RESULT_GET_DISPLAY_MODE_MISMATCH = 12,
    NR_PLUGIN_RESULT_IN_THE_COOL_DOWN = 13,
    NR_PLUGIN_RESULT_UNSUPPORTED_HAND_TRACKING_CALCULATION = 14,
    NR_PLUGIN_RESULT_BUSY = 15,
    NR_PLUGIN_RESULT_PROCESSING = 16,
    NR_PLUGIN_RESULT_NUMBER_LIMITED = 17,
    NR_PLUGIN_RESULT_DISPLAY_NOT_IN_STEREO_MODE = 18,
    NR_PLUGIN_RESULT_INVALID_DATA = 19,
    NR_PLUGIN_RESULT_NOT_FIND_RUNTIME = 20,
    NR_PLUGIN_RESULT_TIMEOUT = 21,
    NR_PLUGIN_RESULT_MCU_INTERNAL_ERROR = 100,
    NR_PLUGIN_RESULT_MCU_INIT_FAIL = 101,
    NR_PLUGIN_RESULT_MCU_START_FAIL = 102,
    NR_PLUGIN_RESULT_MCU_TRY_AGAIN_LATER = 103,
    NR_PLUGIN_RESULT_IMU_INTERNAL_ERROR = 200,
    NR_PLUGIN_RESULT_IMU_INIT_FAIL = 201,
    NR_PLUGIN_RESULT_IMU_START_FAIL = 202,
    NR_PLUGIN_RESULT_IMU_FREQUENCY_CRITICAL = 203,
    NR_PLUGIN_RESULT_VSYNC_INTERNAL_ERROR = 300,
    NR_PLUGIN_RESULT_VSYNC_INIT_FAIL = 301,
    NR_PLUGIN_RESULT_VSYNC_START_FAIL = 302,
    NR_PLUGIN_RESULT_VSYNC_FREQUENCY_CRITICAL = 303,
    NR_PLUGIN_RESULT_GRAYCAMERA_INTERNAL_ERROR = 400,
    NR_PLUGIN_RESULT_MEDIA_INTERNAL_ERROR = 500,
    NR_PLUGIN_RESULT_MEDIA_RGB_CAMERA_OCCUPIED = 501,
    NR_PLUGIN_RESULT_MEDIA_MICRO_OCCUPIED = 502,
    NR_PLUGIN_RESULT_MEDIA_STORAGE_NOT_AVAILABLE = 503,
    NR_PLUGIN_RESULT_MEDIA_PICTURE_STORAGE_NOT_ENOUGH = 504,
    NR_PLUGIN_RESULT_MEDIA_VIDEO_STORAGE_NOT_ENOUGH = 505,
    NR_PLUGIN_RESULT_MEDIA_IRREGULAR_STORAGE = 506,
    NR_PLUGIN_RESULT_MEDIA_VIDEO_AUDIO_ALGORITHM = 507,
    NR_PLUGIN_RESULT_MEDIA_AUDIO_IS_PLAYING = 508,
    NR_PLUGIN_RESULT_SELF_DEFINED_START = 10000,
};

#endif // NRAPP