#!/bin/bash

# Paths to folders A and B
FOLDER_A="."
FOLDER_B="$1"

# Check if FOLDER_B was provided
if [[ -z "$FOLDER_B" ]]; then
    echo "Usage: $0 /path/to/folderB"
    exit 1
fi

# Find all files in FOLDER_A
find "$FOLDER_A" -type f | while read -r file_in_a; do
    # Get the relative path of the file in FOLDER_A
    relative_path="${file_in_a#$FOLDER_A/}"

    # Construct the corresponding path in FOLDER_B
    file_in_b="$FOLDER_B/$relative_path"

    # Check if the file exists in FOLDER_B
    if [[ -f "$file_in_b" ]]; then
        # Copy the file from FOLDER_B to FOLDER_A (update)
        cp "$file_in_b" "$file_in_a"
        echo "Updated $file_in_a with $file_in_b"
    else
        echo "No matching file for $relative_path in $FOLDER_B, skipping..."
    fi
done
