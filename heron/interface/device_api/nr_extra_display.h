#pragma once
#include "nr_common.h"
#include <stdint.h>

/// @defgroup extra Extra
/// @defgroup extra_display Display
/// @ingroup extra
/// @brief display相关内容

/// @{

/// @brief Display 回调方式
/// \n NR_DISPLAY_SUBMIT_TYPE_VSYNC: 收到VSYNC信号时回调
/// \n NR_DISPLAY_SUBMIT_TYPE_INTERVAL_LINE: 每隔一定行数回调
/// \n NR_DISPLAY_SUBMIT_TYPE_FRAME_DONE: 每帧结束时回调

typedef enum NRDisplaySubmitType {
  NR_DISPLAY_SUBMIT_TYPE_VSYNC = 0x0001,
  NR_DISPLAY_SUBMIT_TYPE_INTERVAL_LINE = 0x0002,
  NR_DISPLAY_SUBMIT_TYPE_FRAME_DONE = 0x0004,
} NRDisplaySubmitType;

/// @brief Display 回调中带有的待处理event类型
/// \n NR_DISPLAY_SUBMIT_EVENT_NULL
/// \n NR_DISPLAY_SUBMIT_EVENT_NEED_RESET
/// \n NR_DISPLAY_SUBMIT_EVENT_RESET_FINISH

typedef enum NRDisplaySubmitEvent {
  NR_DISPLAY_SUBMIT_EVENT_NULL = 0,
  NR_DISPLAY_SUBMIT_EVENT_NEED_RESET = 1,
  NR_DISPLAY_SUBMIT_EVENT_RESET_FINISH = 2,
} NRDisplaySubmitEvent;

#pragma pack(1)

/// @brief Display Service Config
/// \n callback_block_cnt:
/// VO每隔callback_block_cnt个block触发一个callback,对应VO_LINE_IRQ_PARA_S.u32LineCount(callback_block_cnt*32)
/// \n init_line_cnt: dpu
/// 被数据触发的行数，对应,VO_START_AUTO_ATTR_S.u32InitLineCnt \n
/// lines64_enable:
/// GDC与DPU之间linebuffer行数,对应.VO_LOWDELAY_ATTR_S.stHardwareLowdelayInfo.u32Lines64Enable
/// \n display_mode: 0:sdk (default), 1:factory

typedef struct NRDisplayConfig {
  union {
    struct {
      uint32_t
          callback_block_cnt; /**< callback_block_cnt:
                                 VO每隔callback_block_cnt个block触发一个callback,对应VO_LINE_IRQ_PARA_S.u32LineCount(callback_block_cnt*32)*/
      uint32_t
          init_line_cnt; /**< init_line_cnt: dpu
                            被数据触发的行数，对应,VO_START_AUTO_ATTR_S.u32InitLineCnt*/
      uint32_t
          lines64_enable;    /**< lines64_enable:
                                GDC与DPU之间linebuffer行数,对应.VO_LOWDELAY_ATTR_S.stHardwareLowdelayInfo.u32Lines64Enable*/
      uint32_t display_mode; /**< display_mode: 0:sdk (default), 1:factory*/
    };
    uint8_t padding[32];
  };

} NRDisplayConfig;

/// @brief Display 上报数据
/// \n callback_timestamp_ns:
/// callback发生时对应的时间戳，对应VO_SUBSCRIBE_INFO_S->u64IrqTimeNs \n
/// block_id: 图像分块后的块id，只有 submit_type
/// 为NR_DISPLAY_SUBMIT_TYPE_INTERVAL_LINE时才有效，对应VO_SUBSCRIBE_INFO_S.stExtpara.stIntervalLinePara.u32CurBlockID
/// \n event:
/// 可能需要回调进行处理的事件标记,对应VO_SUBSCRIBE_INFO_S.stExtpara.emSubEvent

typedef struct NRDisplaySubmitData {
  union {
    struct {
      uint64_t
          callback_timestamp_ns; /**<
                                    callback发生时对应的时间戳，对应VO_SUBSCRIBE_INFO_S->u64IrqTimeNs*/
      uint32_t
          block_id; /**< 图像分块后的块id，只有 submit_type
                       为NR_DISPLAY_SUBMIT_TYPE_INTERVAL_LINE时才有效，对应VO_SUBSCRIBE_INFO_S.stExtpara.stIntervalLinePara.u32CurBlockID*/
      NRDisplaySubmitEvent
          event; /**<
                    可能需要回调进行处理的事件标记,对应VO_SUBSCRIBE_INFO_S.stExtpara.emSubEvent*/
    };
    uint8_t padding[32];
  };

} NRDisplaySubmitData;

/// @brief 输出到DPU Overlay层的数据
/// \n width: 图像的宽，对应VIDEO_FRAME_INFO_S.stVFrame.u32Width
/// \n height: 图像的高，对应VIDEO_FRAME_INFO_S.stVFrame.u32Height
/// \n format: 图像像素通道排布，对应VIDEO_FRAME_INFO_S.stVFrame.enPixelFormat
/// \n data:
/// 图像chn[0]的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64VirAddr[0]的地址 \n
/// 能在client进程读写数据 \n ar_frame_handle:
/// 构造NROverlayFrameData实例时，需同时构造一个VIDEO_FRAME_INFO_S实例，并保留二者对应关系

typedef struct NROverlayFrameData {
  union {
    struct {
      uint32_t width; /**< 图像的宽，对应VIDEO_FRAME_INFO_S.stVFrame.u32Width*/
      uint32_t
          height; /**< 图像的高，对应VIDEO_FRAME_INFO_S.stVFrame.u32Height*/
      NRFrameBufferFormat
          format; /**<
                     图像像素通道排布，对应VIDEO_FRAME_INFO_S.stVFrame.enPixelFormat*/
      const char *
          data_data; /**<
                        图像chn[0]的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64VirAddr[0]的地址*/
      uint32_t
          data_size; /**<
                        图像chn[0]的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64VirAddr[0]的地址*/
      uint64_t
          ar_frame_handle; /**<
                              构造NROverlayFrameData实例时，需同时构造一个VIDEO_FRAME_INFO_S实例，并保留二者对应关系*/
    };
    uint8_t padding[128];
  };

} NROverlayFrameData;

#pragma pack()

typedef void (*NRDisplaySubmit_DispatchCallback)(
    NRDisplayUsage usage, NRDisplaySubmitType submit_type,
    const NRDisplaySubmitData *data, uint32_t data_size);

/// @brief 注册Display 回调函数给DisplayService
/// \n DisplayService保存该函数指针，当接收到来自OLED回调时，调用之。
/// \n 函数指针定义如下：
/// \n int32_t NRDisplaySubmit_DispatchCallback(NRDisplayUsage usage,
/// \n NRDisplaySubmitType submit_type, NRDisplaySubmitData* data);
/// \n air-like:
/// \n light:
/// \n gina: new
/// \n BSP参考code:
/// @code
/// //先实现酷芯SDK的VO_CALLBACK
/// AR_S32 OnDisplayCallback(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info){
/// 	NRDisplayUsage display_usage = dev_id == 0 ? NR_DISPLAY_USAGE_RIGHT :
/// NR_DISPLAY_USAGE_LEFT; 	NRDisplaySubmitData data;
/// 	data.callback_timestamp_ns = sub_info->u64IrqTimeNs;
///		data.event =
///(NRDisplaySubmitEvent)sub_info->stExtpara.emSubEvent;
/// 	if (sub_info->u32IrqType & IRQ_TYPE_VSYNC)
/// 	{
///			NRDisplaySubmit_DispatchCallback(display_usage,
///NR_DISPLAY_SUBMIT_TYPE_VSYNC, data);
/// 	}
/// 	if (sub_info->u32IrqType & IRQ_TYPE_INTERVAL_LINE)
/// 	{
/// 		data.block_id =
/// sub_info->stExtpara.stIntervalLinePara.u32CurBlockID;
///			NRDisplaySubmit_DispatchCallback(display_usage,
///NR_DISPLAY_SUBMIT_TYPE_INTERVAL_LINE, data);
/// 	}
/// 	if (sub_info->u32IrqType & IRQ_TYPE_FRAME_DONE)
/// 	{
///			NRDisplaySubmit_DispatchCallback(display_usage,
///NR_DISPLAY_SUBMIT_TYPE_FRAME_DONE, data);
/// 	}
/// }
///	int32_t result = AR_SUCCESS;
///	VO_IRQ_ATTR_S irq_attr;
///	irq_attr.enIrqType = IRQ_TYPE_INTERVAL_LINE;
///	irq_attr.stIrqAttr.stLineIrqPara.u32LineCount = {line_count};
///	result = AR_MPI_VO_SetIrqAttr(DPU_ID_LEFT_DISPLAY, &irq_attr); // 左
///display 	if (result != AR_SUCCESS) {
///		...
///	}
///	result = AR_MPI_VO_SetIrqAttr(DPU_ID_RIGHT_DISPLAY, &irq_attr); // 右
///display 	if (result != AR_SUCCESS) {
///		...
///	}
///
///	VO_SUBSCRIBE_ATTR_S subscribe_attr;
///	subscribe_attr.u32SubscribeType = IRQ_TYPE_INTERVAL_LINE |
///IRQ_TYPE_VSYNC | IRQ_TYPE_FRAME_DONE;
///
///	subscribe_attr.subscribe_call_back = {OnDisplayCallback};
///	result =
///Dispatcher::GetInstance()->AR_MPI_VO_SubscribeEnable(DPU_ID_LEFT_DISPLAY,
///&subscribe_attr); // 左 display 	if (result != AR_SUCCESS) {
///		...
///	}
///	result =
///Dispatcher::GetInstance()->AR_MPI_VO_SubscribeEnable(DPU_ID_RIGHT_DISPLAY,
///&subscribe_attr); // 右 display 	if (result != AR_SUCCESS) {
///		...
///	}
/// @endcode
/// @param[in] NRDisplaySubmit_DispatchCallback 注册给VO的函数指针
/// @return 结果

NRResult NRDisplaySetCallback(NRDisplaySubmit_DispatchCallback callback);

/// @brief 配置VO
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param[in] NR
/// @return 结果

NRResult NRDisplayConfigService(const NRDisplayConfig *);

/// @brief 调用VO相关接口，使能板子上两个DE各自的overlay0层
/// \n初始化overlay层显示画布的尺寸和位置
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param[in] display 屏幕ID
/// @param[in] start_x overlay画布左上角点x坐标(屏幕左上角点坐标为(0,0))
/// @param[in] start_y overlay画布左上角点y坐标(屏幕左上角点坐标为(0,0))
/// @param[in] width overlay画布宽度
/// @param[in] height overlay画布高度
/// @param[in] format overlay画布像素通道格式
/// @return 结果
/// \n BSP参考code:
/// @code
/// AR_S32 s_VoOverLayer0[2] = {VO_LAYER_ID_OVERLAY_0_0,
/// VO_LAYER_ID_OVERLAY_1_0}; XR_BSP_API NRResult
/// NRDisplayStartOSDRender(NRDisplayUsage display, uint32_t start_x, uint32_t
/// start_y, uint32_t width, uint32_t height)
/// {
///		if (format != PIXEL_FORMAT_ARGB_8888 && format !=
///PIXEL_FORMAT_ARGB_4444) 			return NR_RESULT_FAILURE; //
///unsupported format!
///     VO_VIDEO_LAYER_ATTR_S stOverLayer0Attr = {0};
///     VO_CHN_ATTR_S stOverlay0ChnAttr = {0};
///     SIZE stOverLayer0DevSize;
///     stOverLayer0DevSize.u32Width = width;
///     stOverLayer0DevSize.u32Height = height;
///     stOverLayer0Attr.bClusterMode = AR_FALSE;
///     stOverLayer0Attr.bDoubleFrame = AR_FALSE;
///     stOverLayer0Attr.enDstDynamicRange = DYNAMIC_RANGE_SDR8;
///     stOverLayer0Attr.stDispRect.s32X = 0;
///     stOverLayer0Attr.stDispRect.s32Y = 0;
///     stOverLayer0Attr.stDispRect.u32Height = stOverLayer0DevSize.u32Height;
///     stOverLayer0Attr.stDispRect.u32Width = stOverLayer0DevSize.u32Width;
///     stOverLayer0Attr.stImageSize.u32Height = stOverLayer0DevSize.u32Height;
///     stOverLayer0Attr.stImageSize.u32Width = stOverLayer0DevSize.u32Width;
///     stOverLayer0Attr.u32DispFrmRt = 30;
///     stOverLayer0Attr.enPixFormat = PIXEL_FORMAT_ARGB_8888;
///     stOverLayer0Attr.u32Stride[0] = AR_ALIGN128(stOverLayer0DevSize.u32Width
///     * 4);
///		if(format == NR_FRAME_BUFFER_FORMAT_BGRA_4444) {
///     	stOverLayer0Attr.enPixFormat = PIXEL_FORMAT_ARGB_4444;
///     	stOverLayer0Attr.u32Stride[0] =
///     AR_ALIGN128(stOverLayer0DevSize.u32Width * 2);
///		}
///     stOverLayer0Attr.u32Stride[1] = 0;
///     stOverLayer0Attr.u32Stride[2] = 0;
///     stOverLayer0Attr.u32Stride[3] = 0;
///     stOverLayer0Attr.memMode = VO_MEMORY_MODE_LOW;
///     AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerAttr(s_VoOverLayer0[i],
///     &stOverLayer0Attr), "AR_MPI_VO_SetVideoLayerAttr"); POINT_S
///     stOverlay0Pos = {0}; stOverlay0Pos.s32X = start_x; stOverlay0Pos.s32Y =
///     start_y;
///     AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerPos(s_VoOverLayer0[i],
///     &stOverlay0Pos), "AR_MPI_VO_SetVideoLayerPos");
///     /* ENABLE VO OVERLAYER0 */
///     AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableVideoLayer(s_VoOverLayer0[i]),
///     "AR_MPI_VO_EnableVideoLayer");
///     /* SET AND ENABLE VO CHN */
///     memset(&stOverlay0ChnAttr, 0, sizeof(VO_CHN_ATTR_S));
///     stOverlay0ChnAttr.bDeflicker = AR_FALSE;
///     stOverlay0ChnAttr.u32Priority = 0;
///     stOverlay0ChnAttr.stRect.s32X = 0;
///     stOverlay0ChnAttr.stRect.s32Y = 0;
///     stOverlay0ChnAttr.stRect.u32Height = stOverLayer0DevSize.u32Height;
///     stOverlay0ChnAttr.stRect.u32Width = stOverLayer0DevSize.u32Width;
///     AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetChnAttr(s_VoOverLayer0[i], 0,
///     &stOverlay0ChnAttr), "AR_MPI_VO_SetChnAttr");
///     AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableChn(s_VoOverLayer0[i], 0),
///     "AR_MPI_VO_EnableChn"); BOARD_LOG_INFO("VODP{} overlay0 enabled.", i);
///     return NR_RESULT_SUCCESS;
/// }
/// @endcode

NRResult NRDisplayStartOSDRender(NRDisplayUsage, uint32_t, uint32_t, uint32_t,
                                 uint32_t, NRFrameBufferFormat);

/// @brief 调用VO相关接口，使能板子上两个DE各自的overlay0层
/// \n根据入参中width, height进行计算，使overlay画面显示在各自display得中央位置
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param[in]
/// @return 结果
/// \n BSP参考code:
/// @code
/// AR_S32 s_VoOverLayer0[2] = {VO_LAYER_ID_OVERLAY_0_0,
/// VO_LAYER_ID_OVERLAY_1_0}; XR_BSP_API NRResult
/// NRDisplayStopOSDRender(uint32_t width, uint32_t height) for (int i = 0; i <
/// 2; i++)
/// {
///     AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_DisableChn(s_VoOverLayer0[i], 0),
///     "AR_MPI_VO_DisableChn");
///     AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_DisableVideoLayer(s_VoOverLayer0[i]),
///     "AR_MPI_VO_DisableVideoLayer"); BOARD_LOG_INFO("VODP{} overlay0
///     disabled.", i);
/// }
/// @endcode

NRResult NRDisplayStopOSDRender();

/// @brief
/// 创建VIDEO_FRAME_INFO_S实例并分配VB内存块,将虚拟地址信息赋值给NROverlayFrameData实例
/// \n根据入参中frame->width,frame->height分配VB内存
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param[in][out] frame
/// @return 结果
/// \n BSP参考code:
/// @code
/// static VB_POOL_CONFIG_S s_stVbPoolCfg;
/// static VIDEO_FRAME_INFO_S s_overlay_vb_frame_info[2];
/// static VB_POOL s_vb_pool_id;
/// static VB_BLK s_hBlkHdl[2];
/// static bool PrepareVBPool(uint32_t u32Size)
/// {
///     memset(&s_stVbPoolCfg, 0, sizeof(VB_POOL_CONFIG_S));
///     s_stVbPoolCfg.u64BlkSize = u32Size;
///     s_stVbPoolCfg.u32BlkCnt = 3;
///     s_stVbPoolCfg.enRemapMode = VB_REMAP_MODE_NONE;
///     s_vb_pool_id = pfn_AR_MPI_VB_CreatePool(&s_stVbPoolCfg);
///     if (s_vb_pool_id == VB_INVALID_POOLID)
///     {
///         BOARD_LOG_ERROR("Maybe you not call sys init");
///         return false;
///     }
///     return true;
/// }
/// static bool s_vb_pool_prepared = false;
/// static uint32_t s_overlay_frame_alloc_count = 0; // XXX: need better
/// implmentation XR_BSP_API NRResult
/// NRDisplayAllocOverlayFrame(NROverlayFrameData * overlay_frame_info)
/// {
///     if (s_overlay_frame_alloc_count >= 4)
///     {
///         BOARD_LOG_ERROR("exceeds maximum overlay frame alloc count: {}", 4);
///         return NR_RESULT_FAILURE;
///     }
///		if (overlay_frame_info.frormat != NR_FRAME_BUFFER_FORMAT_BGRA_8888
///&& overlay_frame_info.frormat != NR_FRAME_BUFFER_FORMAT_BGRA_4444) {
///         BOARD_LOG_ERROR("unsuppported overlay framebuffer format: {}",
///         overlay_frame_info.frormat); return NR_RESULT_FAILURE;
///		}
///     uint32_t width = 960; //
///     width，height必须与enableoverlay接口中的width，height相等 uint32_t
///     height = 540; uint32_t stride = AR_ALIGN128(width); uint32_t u32Size =
///     stride * height * 4; if (!s_vb_pool_prepared)
///     {
///         if (PrepareVBPool(u32Size))
///         {
///             s_vb_pool_prepared = true;
///         }
///         else
///         {
///             BOARD_LOG_ERROR("{} PrepareVBPool error", __FUNCTION__);
///             return NR_RESULT_FAILURE;
///         }
///     }
///     uint32_t i = s_overlay_frame_alloc_count;
///     s_hBlkHdl[i] = pfn_AR_MPI_VB_GetBlock(s_vb_pool_id, u32Size, NULL);
///     if (s_hBlkHdl[i] == VB_INVALID_HANDLE)
///     {
///         BOARD_LOG_ERROR("[VOU_MST_File2VO] get vb fail!!! {}", i);
///         return NR_RESULT_FAILURE;
///     }
///     BOARD_LOG_INFO("overlay VB block handle {}: {}, size: {}", i,
///     s_hBlkHdl[i], u32Size); uint32_t plane_size = stride * height;
///     s_overlay_vb_frame_info[i].stVFrame.enField = VIDEO_FIELD_INTERLACED;
///     s_overlay_vb_frame_info[i].stVFrame.enCompressMode = COMPRESS_MODE_NONE;
///     s_overlay_vb_frame_info[i].stVFrame.enPixelFormat =
///     PIXEL_FORMAT_ARGB_8888;
///     s_overlay_vb_frame_info[i].stVFrame.enVideoFormat = VIDEO_FORMAT_LINEAR;
///     s_overlay_vb_frame_info[i].stVFrame.enColorGamut = COLOR_GAMUT_BT709;
///     s_overlay_vb_frame_info[i].stVFrame.u32Width = width;
///     s_overlay_vb_frame_info[i].stVFrame.u32Height = height;
///     s_overlay_vb_frame_info[i].stVFrame.u32Stride[0] = AR_ALIGN128(width *
///     4); s_overlay_vb_frame_info[i].stVFrame.u32Stride[1] = 0;
///     s_overlay_vb_frame_info[i].stVFrame.u32Stride[2] = 0;
///     s_overlay_vb_frame_info[i].stVFrame.u32Stride[3] = 0;
///     s_overlay_vb_frame_info[i].stVFrame.u32TimeRef = 0;
///     s_overlay_vb_frame_info[i].stVFrame.u64PTS = 0;
///     s_overlay_vb_frame_info[i].stVFrame.enDynamicRange = DYNAMIC_RANGE_SDR8;
///     s_overlay_vb_frame_info[i].u32PoolId =
///     pfn_AR_MPI_VB_Handle2PoolId(s_hBlkHdl[i]);
///     s_overlay_vb_frame_info[i].stVFrame.u64PhyAddr[0] =
///     pfn_AR_MPI_VB_Handle2PhysAddr(s_hBlkHdl[i]);
///     s_overlay_vb_frame_info[i].stVFrame.u64VirAddr[0] =
///     (AR_U64)pfn_AR_MPI_SYS_Mmap(s_overlay_vb_frame_info[i].stVFrame.u64PhyAddr[0],
///     u32Size); overlay_frame_info->width = width; overlay_frame_info->height
///     = height; overlay_frame_info->data_data = (const
///     char*)(s_overlay_vb_frame_info[i].stVFrame.u64VirAddr[0]);
///     overlay_frame_info->data_size = u32Size;
///     overlay_frame_info->ar_frame_handle =
///     (uint64_t)(&s_overlay_vb_frame_info[i]); s_overlay_frame_alloc_count++;
///     return NR_RESULT_SUCCESS;
/// }
/// @endcode

NRResult NRDisplayAllocOverlayFrame(NROverlayFrameData *);

/// @brief 释放与NROverlayFrameData以及相应VIDEO_FRAME_INFO_S实例绑定的VB Memory
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param[in] frame
/// @return 结果

NRResult NRDisplayDeallocOverlayFrame(const NROverlayFrameData *);

/// @brief 提交绘制完成的NROverlayFrameData到DPU的Overlay层
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param[in] display_usage
/// @param[in] frame
/// @return 结果
/// \n BSP参考code:
/// @code
/// XR_BSP_API NRResult NRDisplaySendOverlayFrame(NRDisplayUsage display_usage,
/// const NROverlayFrameData *overlay_frame_info)
/// {
///     AR_S32 ret = 0;
///		(VIDEO_FRAME_INFO_S *) p_video_frame_info = (VIDEO_FRAME_INFO_S
///*)overlay_frame_info->ar_frame_handle; 		switch
///(overlay_frame_info->format) { 		case
///NR_FRAME_BUFFER_FORMAT_BGRA_8888:
///			p_video_frame_info->stVFrame.enPixelFormat =
///PIXEL_FORMAT_ARGB_8888;
///			p_video_frame_info->stVFrame.u32Stride[0] =
///AR_ALIGN128(overlay_frame_info->width * 4); 		break; 		case
///NR_FRAME_BUFFER_FORMAT_BGRA_4444:
///			p_video_frame_info->stVFrame.enPixelFormat =
///PIXEL_FORMAT_ARGB_4444;
///			p_video_frame_info->stVFrame.u32Stride[0] =
///AR_ALIGN128(overlay_frame_info->width * 2); 		break; 		default:
///			p_video_frame_info->stVFrame.enPixelFormat =
///PIXEL_FORMAT_ARGB_8888;
///			p_video_frame_info->stVFrame.u32Stride[0] =
///AR_ALIGN128(overlay_frame_info->width * 4);
///		}
/// 	if (display_usage == NR_DISPLAY_USAGE_LEFT) {
///         ret = pfn_AR_MPI_VO_SendFrame(VO_LAYER_ID_OVERLAY_1_0, 0,
///         p_video_frame_info, 0);
///     }
/// 	if (display_usage == NR_DISPLAY_USAGE_RIGHT) {
///         ret = pfn_AR_MPI_VO_SendFrame(VO_LAYER_ID_OVERLAY_0_0, 0,
///         p_video_frame_info, 0);
///     }
///     if (ret != NR_RESULT_FAILURE)
///     {
///         BOARD_LOG_ERROR("SendFrameToOverlays error");
///         return false;
///     }
///     return NR_RESULT_SUCCESS;
/// }
/// @endcode

NRResult NRDisplaySendOverlayFrame(NRDisplayUsage, const NROverlayFrameData *);
/// @}