#pragma once
#include "nr_common.h"

/// @defgroup extra Extra
/// @defgroup extra_gdc Gdc
/// @ingroup extra
/// @brief gdc相关内容

/// @{

#pragma pack(1)

/// @brief GDC输入/输出的image buffer相关配置
/// \n is_vb: 对应帧数据是否为"vb内存"标记,
/// 对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.u32IsVB \n pixel_format:
/// 图像的宽，对应AR_GDC_ADV_TRANSFORM_S.stLdParam.stOutLowdelay.enFormat \n
/// width: 图像的宽，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.u32Width \n height:
/// 图像的高，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.u32Height \n strides:
/// 图像各通道的stride，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.astChannels[4].u32Stride
/// \n data:
/// 图像各通道的虚拟地址，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.astChannels[4].uptrAddrVirt
/// \n data_ext:
/// 图像各通道的物理地址，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.astChannels[4].u32AddrPhy
//  \n header:
//  图像开启cf50编码后的header虚拟地址，对应AR_GDC_ADV_TRANSFORM_S.stCF50Param.stCF50Decoder.uptrCf50HeaderVirt[0/1/2/3]
//  \n header_ext:
//  图像开启cf50编码后的header虚拟地址，对应AR_GDC_ADV_TRANSFORM_S.stCF50Param.stCF50Decoder.u32Cf50HeaderPhy[0/1/2/3]

typedef struct NRGdcImageBuffer {
  union {
    struct {
      uint32_t is_vb; /**< 对应帧数据是否为"vb内存"标记,
                         对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.u32IsVB*/
      NRFrameBufferFormat
          pixel_format; /**<
                           图像的像素格式，对应AR_GDC_ADV_TRANSFORM_S.stLdParam.stOutLowdelay.enFormat*/
      uint32_t
          width; /**< 图像的宽，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.u32Width*/
      uint32_t
          height; /**<
                     图像的高，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.u32Height*/
      NRVector4u
          strides; /**<
                      对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.astChannels[0/1/2/3].u32Stride*/
      NRVector4u64
          data; /**<
                   对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.astChannels[0/1/2/3].uptrAddrVirt*/
      NRVector4u
          data_ext; /**<
                       对应AR_GDC_ADV_TRANSFORM_S.stInBuffer.astChannels[0/1/2/3].u32AddrPhy*/
      NRVector4u64
          header; /**<
                     对应AR_GDC_ADV_TRANSFORM_S.stCF50Param.stCF50Decoder.uptrCf50HeaderVirt[0/1/2/3]*/
      NRVector4u
          header_ext; /**<
                         对应AR_GDC_ADV_TRANSFORM_S.stCF50Param.stCF50Decoder.u32Cf50HeaderPhy[0/1/2/3]*/
    };
    uint8_t padding[128];
  };

} NRGdcImageBuffer;

/// @brief GDC warp, mesh, weight数据
/// \n warp_data: GDC
/// warp数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.u32WarpAddr
/// \n mesh_data: GDC
/// mesh数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stMeshCfg.u32MeshAddr
/// \n weight_data: GDC
/// weight数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWeightCfg.u32WeightAddr

typedef struct NRGdcMetadata {
  union {
    struct {
      const char *
          warp_data_data; /**< GDC
                             warp数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.u32WarpAddr*/
      uint32_t
          warp_data_size; /**< GDC
                             warp数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.u32WarpAddr*/
      const char *
          mesh_data_data; /**< GDC
                             mesh数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stMeshCfg.u32MeshAddr*/
      uint32_t
          mesh_data_size; /**< GDC
                             mesh数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stMeshCfg.u32MeshAddr*/
      const char *
          weight_data_data; /**< GDC
                               weight数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWeightCfg.u32WeightAddr*/
      uint32_t
          weight_data_size; /**< GDC
                               weight数据起始物理地址，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWeightCfg.u32WeightAddr*/
    };
    uint8_t padding[64];
  };

} NRGdcMetadata;

/// @brief GDC 初始化配置
/// \n core_id:
/// 目标GDC硬件模块id，范围为{0,1,2},对应AR_GDC_ADV_TRANSFORM_S.s32CoreId \n
/// start_mode: 0 normal; 1 auto; 2 shadow; 3
/// free,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stStartCfg.u8StartMode \n
/// lines64_enable:
/// GDC与DPU之间linebuffer行数,对应.VO_LOWDELAY_ATTR_S.stHardwareLowdelayInfo.u32Lines64Enable
/// \n padding_color:
/// GDC不采样frame时，输出的颜色（YUV),对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stRoiCfg.stExtPadding0/1.u16C0/1/2
/// \n warp_mode: 0: use warp.dat file; 1 use apb matrix; 2
/// disable,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.stWarpMode.u32WarpMode
/// \n warp_flush_cnt:
/// 每个3x3warp矩阵复用几次，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt
/// \n mesh_mode: 0:32x32 blocking,
/// 3:disable，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stMeshCfg.u32MeshMode \n
/// mesh_stride:
/// 一行mesh点对应mesh数据byte数，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stMeshCfg.u32MeshStride
/// \n weight_mode: 0:inner bi-linear; 1: weight.dat
/// file,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWeightCfg.stWeightMode.u32WeightMode
/// \n metadata:
/// GDC每一帧用的Metadata，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.[stWarpCfg/stMeshCfg/stWeightCfg]

typedef struct NRGdcInitConfig {
  union {
    struct {
      NRDisplayUsage
          display_usage; /**< 目标GDC对应的display:left core_id=1;right
                            core_id=0;对应AR_GDC_ADV_TRANSFORM_S.s32CoreId*/
      uint8_t
          start_mode; /**< 0 normal; 1 auto; 2 shadow; 3
                         free,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stStartCfg.u8StartMode*/
      uint32_t
          lines64_enable; /**< lines64_enable:
                             GDC与DPU之间linebuffer行数,对应.VO_LOWDELAY_ATTR_S.stHardwareLowdelayInfo.u32Lines64Enable*/
      NRVector4u8
          padding_color; /**<
                            GDC不采样frame时，输出的颜色（YUV),对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stRoiCfg.stExtPadding0/1.u16C0/1/2*/
      uint32_t
          warp_mode; /**< 0: use warp.dat file; 1 use apb matrix; 2
                        disable,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.stWarpMode.u32WarpMode*/
      uint32_t
          warp_flush_cnt; /**<
                             每个3x3warp矩阵复用几次，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt*/
      uint32_t
          mesh_mode; /**< 0:32x32 blocking,
                        3:disable,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stMeshCfg.u32MeshMode*/
      uint32_t
          mesh_stride; /**<
                          一行mesh点对应mesh数据byte数，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stMeshCfg.u32MeshStride*/
      uint32_t
          weight_mode; /**< 0:inner bi-linear; 1: weight.dat
                          file,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWeightCfg.stWeightMode.u32WeightMode*/
      NRGdcMetadata
          metadata; /**<
                       GDC矩阵计算需要查的配置表信息，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.[stWarpCfg/stMeshCfg/stWeightCfg]*/
    };
    uint8_t padding[256];
  };

} NRGdcInitConfig;

/// @brief GDC line Config
/// \n core_id: 目标GDC的id，对应AR_GDC_ADV_LINE_PARAMS_S.s32CoreId
/// \n line_num: GDC处理到目标行数，对应AR_GDC_ADV_LINE_PARAMS_S.s32LineNum

typedef struct NRGdcLineConfig {
  union {
    struct {
      uint32_t
          core_id; /**< 目标GDC的id，对应AR_GDC_ADV_LINE_PARAMS_S.s32CoreId*/
      uint32_t
          line_num; /**<
                       GDC处理到目标行数，对应AR_GDC_ADV_LINE_PARAMS_S.s32LineNum*/
    };
    uint8_t padding[32];
  };

} NRGdcLineConfig;

/// @brief GDC 每帧需要更新的 Config
/// \n display_usage: 目标GDC对应的display:left core_id=1;right
/// core_id=0;对应AR_GDC_ADV_TRANSFORM_S.s32CoreId \n in_buffer: GDC input
/// buffer相关配置，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer \n frame_start:
/// 特殊flag，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stStartCfg.u8FrameStart \n
/// warp_mode: 0: use warp.dat file; 1 use apb matrix; 2
/// disable,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.stWarpMode.u32WarpMode
/// \n apb_matrix: warp_mode = 1时使用的3x3 warp 矩阵
/// \n metadata:
/// GDC每一帧用的Metadata，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.[stWarpCfg/stMeshCfg/stWeightCfg]
/// \n padding_color:
/// GDC不采样frame时，输出的颜色（YUV),对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stRoiCfg.stExtPadding0/1.u16C0/1/2

typedef struct NRGdcFrameConfig {
  union {
    struct {
      NRDisplayUsage
          display_usage; /**< 目标GDC对应的display:left core_id=1;right
                            core_id=0;对应AR_GDC_ADV_TRANSFORM_S.s32CoreId*/
      NRGdcImageBuffer
          in_buffer; /**< GDC input
                        buffer相关配置，对应AR_GDC_ADV_TRANSFORM_S.stInBuffer*/
      uint8_t
          frame_start; /**<
                          特殊flag，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stStartCfg.u8FrameStart*/
      uint32_t
          warp_mode;          /**< 0: use warp.dat file; 1 use apb matrix; 2
                                 disable,对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stWarpCfg.stWarpMode.u32WarpMode*/
      uint32_t apb_matrix[9]; /**< warp_mode = 1时使用的3x3 warp 矩阵*/
      NRGdcMetadata
          metadata; /**<
                       GDC矩阵计算需要查的配置表信息，对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.[stWarpCfg/stMeshCfg/stWeightCfg]*/
      NRVector4u8
          padding_color; /**<
                            GDC不采样frame时，输出的颜色（YUV),对应AR_GDC_ADV_TRANSFORM_S.stGdcParam.stRoiCfg.stExtPadding0/1.u16C0/1/2*/
    };
    uint8_t padding[256];
  };

} NRGdcFrameConfig;

#pragma pack()

/// @brief 将NRGdcInitConfig中的字段赋值配置给AR_GDC_ADV_TRANSFORM_S实例
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param config
/// \n BSP参考code(以yuv420为例):
/// @code
/// //将Xreal接口入参config中的字段逐一赋值给gdc_config
/// static AR_GDC_ADV_TRANSFORM_S s_gdc_config[2];
/// XR_BSP_API NRResult NRGdcInit(const NRGdcInitConfig *config)
/// {
///     /************************* 直接写死的配置*********************/
///     memset((void *)(&s_gdc_config[config->display_usage]), 0,
///     sizeof(AR_GDC_ADV_TRANSFORM_S));
///     s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtCtrl.u8BackPixelCtl
///     = 1; // use padding color when coord w < 0
///     // test boarder padding color. black in YUV420
///     s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtCtrl.u8CplxPadCtl
///     = 7; // 配置borader color 通道使能。7对应二进制0111,使能3个channel
///
///     s_gdc_config[config->display_usage].stOutBuffer.u32Width = 1920;   //
///     由眼镜屏幕width决定，这里直接写死
///     s_gdc_config[config->display_usage].stOutBuffer.u32Height = 1080; //
///     同上面的width
///
///     // lowdelay stOutBuffer就是DPU,stride 必须为4096
///     s_gdc_config[config->display_usage].stOutBuffer.astChannels[0].u32Stride
///     = 4096;
///     s_gdc_config[config->display_usage].stOutBuffer.astChannels[1].u32Stride
///     = 4096;
///     s_gdc_config[config->display_usage].stOutBuffer.astChannels[2].u32Stride
///     = 4096;
///
///     // GDC计算通道使能flag
///     s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC0Enable
///     = 1;
///     s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC1Enable
///     = 1;
///     s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC2Enable
///     = 1;
///
///     //
///     因为yuv420p的uv宽高分量是y分量的一半，所以设置uv通道的downscaler将其宽高下采样2倍
///     s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC0Downscaler
///     = 0;
///     s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC1Downscaler
///     = 1;
///     s_gdc_config[config->display_usage].stGdcParam.stChannelCfg.u8SrcC2Downscaler
///     = 1;
///
///     s_gdc_config[config->display_usage].s32NonBlock = true; //
///     MPI_GDC_ADV_Config方法不阻塞，直接返回
///
///     /**
///      * 控制GDC是否通过lowdelay硬连线向后端送出已写出的dst_pic行数信息（只控制gdc是否送出此信号，后端display模块是否使用，要看display的具体配置）。
///      * out_bp多用于gdc向ddr写数据，display需要知道ddr中已存在的pic行数的应用；或者需要gdc在计算完指定行时，精确启动display的场景。
///      */
///     s_gdc_config[config->display_usage].stGdcParam.stLdCfg.stOutBp.u8OutBpEn
///     = 1;
///     s_gdc_config[config->display_usage].stGdcParam.stStartCfg.u8FrameStart =
///     1;  // start working(auto clean)
///     s_gdc_config[config->display_usage].stGdcParam.stStartCfg.u8SafetyStart
///     = 1; // safety_start
///
///     /**
///      *配置GDC->DPU Lowdelay 连接模式下的寄存器
///      */
///     s_gdc_config[config->display_usage].u8LdEn = 1;
///     s_gdc_config[config->display_usage].stLdParam.enLdMode =
///     AR_GDC_LD_MODE_OUT;                                           //
///     Lowdelay 模式必须这样配
///     s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.enLowdelayMode
///     = AR_SYS_HARDWARE_LOWDEALY_MODE_LINEBUFFER; // Lowdelay 模式必须这样配
///     s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32PlanarNum
///     = 3;                                          // yuv420
///     s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.enFormat =
///     PIXEL_FORMAT_YVU_PLANAR_420;                    //
///     目前酷芯sdk中YUV420此值为:23
///
///     // 每个weight单元复用几次,我们用1
///     s_gdc_config[config->display_usage].stGdcParam.stWeightCfg.stWeightMode.u32WeightFlush
///     = 1;
///
///     // XXX apb matrix值仅当stWarpCfg.stWarpMode.u32WarpMode = 1时生效
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[0]
///     = 0x3f800000; // 1.0
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[1]
///     = 0;
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[2]
///     = 0;
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[3]
///     = 0;
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[4]
///     = 0x3f800000; // 1.0
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[5]
///     = 0;
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[6]
///     = 0;
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[7]
///     = 0;
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[8]
///     = 0xC0400000; // -1.0;
///
///     /************************* 开始使用入参中的配置值*********************/
///     s_gdc_config[config->display_usage].s32CoreId = config->display_usage ==
///     NR_DISPLAY_USAGE_LEFT ? 1 : 0;
///     s_gdc_config[config->display_usage].stGdcParam.stStartCfg.u8StartMode =
///     config->start_mode;
///     s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Lines64Enable
///     = config->lines64_enable;
///
///     s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding0.u16C0
///     = config->padding_color.x; // GDC采样超出原图区域的颜色(通道1)
///     s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding0.u16C1
///     = config->padding_color.y; // GDC采样超出原图区域的颜色(通道2)
///     s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding1.u16C2
///     = config->padding_color.z; // GDC采样超出原图区域的颜色(通道3)
///
///     // 0: use warp.dat file; 1 use apb matrix; 2 disable。我们用0
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode
///     = config->warp_mode;
///     //
///     每个3x3warp矩阵复用几次。每行用同一个矩阵则矩阵一帧共35个矩阵，warp_flush_cnt
///     = 61
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.stWarpMode.u32WarpFlushCnt
///     = config->warp_flush_cnt;
///     // 0:32x32 blocking; 3:disable。我们用0
///     s_gdc_config[config->display_usage].stGdcParam.stMeshCfg.u32MeshMode =
///     config->mesh_mode;
///     // mesh每行1920/32=61个点,每个点两个float,61x8bytes=488
///     s_gdc_config[config->display_usage].stGdcParam.stMeshCfg.u32MeshStride =
///     config->mesh_stride;
///     // 0:inner bi-linear; 1: weight.dat file;我们用1
///     s_gdc_config[config->display_usage].stGdcParam.stWeightCfg.stWeightMode.u32WeightMode
///     = config->weight_mode;
///     // warp矩阵list的MMZ起始地址
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32WarpAddr =
///     (uint32_t)((uint64_t)config->metadata.warp_data_data & 0xffffffff);
///     s_gdc_config[config->display_usage].stGdcParam.stMeshCfg.u32MeshAddr =
///     (uint32_t)((uint64_t)config->metadata.mesh_data_data & 0xffffffff);
///     s_gdc_config[config->display_usage].stGdcParam.stWeightCfg.u32WeightAddr
///     = (uint32_t)((uint64_t)config->metadata.weight_data_data & 0xffffffff);
///
/// 	s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[0]
/// = AR_ALIGN128(1920); // width值是1920
/// 	s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[1]
/// = AR_ALIGN128(960);
/// 	s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[2]
/// = AR_ALIGN128(960);
///     return NR_RESULT_SUCCESS;
///}
/// @endcode
/// @return 结果

NRResult NRGdcInit(const NRGdcInitConfig *);

/// @brief
/// 将NRGdcFrameConfig中的字段配置给GDC，GDC将在进行下一帧计算时使用该配置 \n
/// air-like: \n light: \n gina: new
/// @param config
/// @param need_reset
/// \n BSP参考code(以yuv420为例):
/// @code
/// //将Xreal接口入参config中的字段逐一赋值给gdc_config
/// XR_BSP_API NRResult NRGdcProcessDiscardOnBusy(const NRGdcFrameConfig
/// *config, bool need_reset)
/// {
/// 	AR_S32 ret = -1;
/// 	uint8_t frame_start = config->frame_start;
/// 	if (need_reset)
/// 	{
///         frame_start = 1;
/// 	    BOARD_LOG_WARN("resetting linebuffer for id: {}",
/// s_gdc_config[config->display_usage].s32CoreId);
/// 	    /* 1. disable gdc */
/// 	    ret =
/// pfn_AR_MPI_GDC_ADV_Stop(s_gdc_config[config->display_usage].s32CoreId); 	    if
/// (ret < 0)
/// 	    {
/// 	        BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Stop fail");
/// 	    	return NR_RESULT_FAILURE;
/// 	    }
/// 	    /* 2. disablevo lb */
/// 	    ret =
/// pfn_AR_MPI_VO_DisableLineBuffer(s_gdc_config[config->display_usage].s32CoreId);
/// 	    if (ret < 0)
/// 	    {
/// 	        BOARD_LOG_ERROR("AR_MPI_VO_DisableLineBuffer fail");
/// 	    	return NR_RESULT_FAILURE;
/// 	    }
/// 	    /* 3. enable vo lb */
/// 	    ret =
/// pfn_AR_MPI_VO_EnableLineBuffer(s_gdc_config[config->display_usage].s32CoreId);
/// 	    if (ret < 0)
/// 	    {
/// 	        BOARD_LOG_ERROR("AR_MPI_VO_EnableLineBuffer fail");
/// 	    	return NR_RESULT_FAILURE;
/// 	    }
/// 	}
///
///     s_gdc_config[config->display_usage].stGdcParam.stMeshCfg.u32MeshAddr =
///     (uint32_t)((uint64_t)config->metadata.mesh_data_data & 0xffffffff);
///     s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding0.u16C0
///     = config->padding_color.x; // GDC采样超出原图区域的颜色(通道1)
///     s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding0.u16C1
///     = config->padding_color.y; // GDC采样超出原图区域的颜色(通道2)
///     s_gdc_config[config->display_usage].stGdcParam.stRoiCfg.stExtPadding1.u16C2
///     = config->padding_color.z; // GDC采样超出原图区域的颜色(通道3)
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.stWarpMode.u32WarpMode
///     = config->warp_mode;
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[0]
///     = config->apb_matrix[0];
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[1]
///     = config->apb_matrix[1];
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[2]
///     = config->apb_matrix[2];
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[3]
///     = config->apb_matrix[3];
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[4]
///     = config->apb_matrix[4];
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[5]
///     = config->apb_matrix[5];
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[6]
///     = config->apb_matrix[6];
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[7]
///     = config->apb_matrix[7];
///     s_gdc_config[config->display_usage].stGdcParam.stWarpCfg.u32ApbMatrix[8]
///     = config->apb_matrix[8];
///
///     s_gdc_config[config->display_usage].stGdcParam.stStartCfg.u8FrameStart =
///     frame_start; // start working(auto clean)
/// 	s_gdc_config[config->display_usage].stInBuffer.u32IsVB =
/// config->in_buffer.is_vb;
/// 	s_gdc_config[config->display_usage].stInBuffer.u32Width =
/// config->in_buffer.width;
/// 	s_gdc_config[config->display_usage].stInBuffer.u32Height =
/// config->in_buffer.height;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[0].u32Stride
/// = config->in_buffer.strides.x;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[1].u32Stride
/// = config->in_buffer.strides.y;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[2].u32Stride
/// = config->in_buffer.strides.z;
/// 	// VI获取的Frame每个Chanel的物理地址和虚拟地址,
/// 直接赋值透传给GDC的相应寄存器即可
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[0].uptrAddrVirt
/// = config->in_buffer.data.x;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[1].uptrAddrVirt
/// = config->in_buffer.data.y;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[2].uptrAddrVirt
/// = config->in_buffer.data.z;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[0].u32AddrPhy
/// = config->in_buffer.data_ext.x;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[1].u32AddrPhy
/// = config->in_buffer.data_ext.y;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[2].u32AddrPhy
/// = config->in_buffer.data_ext.z; 	ret =
/// pfn_AR_MPI_GDC_ADV_Config(&s_gdc_config[config->display_usage]); 	if
/// (AR_SUCCESS != ret)
/// 	{
/// 	    BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Config error");
/// 	    return NR_RESULT_FAILURE;
/// 	}
/// 	ret = pfn_AR_MPI_GDC_ADV_Start(&s_gdc_config[config->display_usage]);
/// 	if (AR_SUCCESS != ret)
/// 	{
/// 	    BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Start error");
/// 	    return NR_RESULT_FAILURE;
/// 	}
/// 	if (need_reset)
/// 	{
/// 	    ret =
/// pfn_AR_MPI_VO_ResetLineBufferPrs(s_gdc_config[config->display_usage].s32CoreId);
/// 	    if (ret < 0)
/// 	    {
/// 	        BOARD_LOG_ERROR("AR_MPI_VO_ResetLineBufferPrs fail");
/// 	    	return NR_RESULT_FAILURE;
/// 	    }
/// 	    BOARD_LOG_WARN("reset linebuffer for id: {} done",
/// s_gdc_config[config->display_usage].s32CoreId);
/// 	}
/// 	return NR_RESULT_SUCCESS;
/// }
/// @endcode
/// @return 结果

NRResult NRGdcProcessDiscardOnBusy(const NRGdcFrameConfig *, bool);

/// @brief GDC的同步调用接口，对应AR_MPI_GDC_ADV_Process(pstParam)
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param config
/// @param need_reset
/// \n BSP参考code:
/// @code
/// //将Xreal接口入参config中的字段逐一赋值给gdc_config
/// XR_BSP_API NRResult NRGdcProcess(const NRGdcFrameConfig *config, bool
/// need_reset)
/// {
/// 	AR_S32 ret = -1;
/// 	need_reset = false;
/// 	uint8_t frame_start = config->frame_start;
/// 	if (need_reset)
/// 	{
///         frame_start = 1;
/// 	    BOARD_LOG_WARN("resetting linebuffer for id: {}",
/// s_gdc_config[config->display_usage].s32CoreId);
/// 	    /* 1. disable gdc */
/// 	    ret =
/// pfn_AR_MPI_GDC_ADV_Stop(s_gdc_config[config->display_usage].s32CoreId); 	    if
/// (ret < 0)
/// 	    {
/// 	        BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Stop fail");
/// 	    	return NR_RESULT_FAILURE;
/// 	    }
/// 	    /* 2. disablevo lb */
/// 	    ret =
/// pfn_AR_MPI_VO_DisableLineBuffer(s_gdc_config[config->display_usage].s32CoreId);
/// 	    if (ret < 0)
/// 	    {
/// 	        BOARD_LOG_ERROR("AR_MPI_VO_DisableLineBuffer fail");
/// 	    	return NR_RESULT_FAILURE;
/// 	    }
/// 	    /* 3. enable vo lb */
/// 	    ret =
/// pfn_AR_MPI_VO_EnableLineBuffer(s_gdc_config[config->display_usage].s32CoreId);
/// 	    if (ret < 0)
/// 	    {
/// 	        BOARD_LOG_ERROR("AR_MPI_VO_EnableLineBuffer fail");
/// 	    	return NR_RESULT_FAILURE;
/// 	    }
/// 	}
///     s_gdc_config[config->display_usage].stGdcParam.stStartCfg.u8FrameStart =
///     frame_start; // start working(auto clean)
/// 	s_gdc_config[config->display_usage].stInBuffer.u32IsVB =
/// config->in_buffer.is_vb;
/// 	s_gdc_config[config->display_usage].stInBuffer.u32Width =
/// config->in_buffer.width;
/// 	s_gdc_config[config->display_usage].stInBuffer.u32Height =
/// config->in_buffer.height;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[0].u32Stride
/// = config->in_buffer.strides.x;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[1].u32Stride
/// = config->in_buffer.strides.y;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[2].u32Stride
/// = config->in_buffer.strides.z;
/// 	// VI获取的Frame每个Chanel的物理地址和虚拟地址,
/// 直接赋值透传给GDC的相应寄存器即可
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[0].uptrAddrVirt
/// = config->in_buffer.data.x;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[1].uptrAddrVirt
/// = config->in_buffer.data.y;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[2].uptrAddrVirt
/// = config->in_buffer.data.z;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[0].u32AddrPhy
/// = config->in_buffer.data_ext.x;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[1].u32AddrPhy
/// = config->in_buffer.data_ext.y;
/// 	s_gdc_config[config->display_usage].stInBuffer.astChannels[2].u32AddrPhy
/// = config->in_buffer.data_ext.z;
/// 	s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[0]
/// = AR_ALIGN128(config->in_buffer.width); // width值是1920
/// 	s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[1]
/// = AR_ALIGN128(config->in_buffer.width / 2);
/// 	s_gdc_config[config->display_usage].stLdParam.stOutLowdelay.u32Width[2]
/// = AR_ALIGN128(config->in_buffer.width / 2); 	ret =
/// pfn_AR_MPI_GDC_ADV_Process(&s_gdc_config[config->display_usage]); 	if
/// (AR_SUCCESS != ret)
/// 	{
/// 	    BOARD_LOG_ERROR("AR_MPI_GDC_ADV_Process error");
/// 	    return NR_RESULT_FAILURE;
/// 	}
/// 	if (need_reset)
/// 	{
/// 	    ret =
/// pfn_AR_MPI_VO_ResetLineBufferPrs(s_gdc_config[config->display_usage].s32CoreId);
/// 	    if (ret < 0)
/// 	    {
/// 	        BOARD_LOG_ERROR("AR_MPI_VO_ResetLineBufferPrs fail");
/// 	    	return NR_RESULT_FAILURE;
/// 	    }
/// 	    BOARD_LOG_WARN("reset linebuffer for id: {} done",
/// s_gdc_config[config->display_usage].s32CoreId);
/// 	}
/// 	return NR_RESULT_SUCCESS;
/// }
/// @endcode
/// @return 结果

NRResult NRGdcProcess(const NRGdcFrameConfig *, bool);

/// @brief
/// GDC渲染到当前帧的目标line，对应XR_AR_MPI_GDC_ADV_Config_CPUBp_Line(pstLineParam)
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param line_config
/// @return 结果

NRResult NRGdcProcessToLine(const NRGdcLineConfig *);

/// @brief 申请mmz内存，对应ar_hal_sys_mmz_alloc(AR_U64 *pu64_phy_addr, AR_VOID
/// **p_vir_addr,
///                                           const AR_CHAR *pstr_mmb, const
///                                           AR_CHAR *pstr_zone, AR_U32
///                                           u32_len)
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param  physical_addr
/// @param  virtual_addr
/// @param  name
/// @param  zone_name
/// @param  length
/// @return 结果

NRResult NRMmzAlloc(void **, void **, const char *, const char *, uint32_t);

/// @brief 释放mmz内存，对应ar_hal_sys_mmz_free(AR_U64 pu64_phy_addr, AR_VOID
/// *p_vir_addr) \n air-like: \n light: \n gina: new
/// @param  physical_addr
/// @param  virtual_addr
/// @return 结果

NRResult NRMmzFree(void *, void *);
/// @}