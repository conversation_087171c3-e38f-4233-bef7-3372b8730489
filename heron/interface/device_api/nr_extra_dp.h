#pragma once
#include "nr_common.h"

/// @defgroup extra Extra
/// @defgroup extra_dp Dp
/// @ingroup extra
/// @brief dp相关内容

/// @{

#pragma pack(1)

/// @brief Dp 数据
/// \n frame_id: 图像的id，对应VIDEO_FRAME_INFO_S.stVFrame.u32FrameId
/// \n width: 图像的宽，对应VIDEO_FRAME_INFO_S.stVFrame.u32Width
/// \n height: 图像的高，对应VIDEO_FRAME_INFO_S.stVFrame.u32Height
/// \n pixel_format:
/// 图像的像素格式，对应VIDEO_FRAME_INFO_S.stVFrame.enPixelFormat \n data:
/// 图像3个通道数据的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64VirAddr，能在client进程读写数据
/// \n data_ext:
/// 图像3个通道数据的物理地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64PhyAddr \n
/// header:
/// 图像header3个通道数据的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64HeaderVirAddr,用于内存压缩、dump文件等场景
/// \n header_ext:
/// 图像header3个通道数据的物理地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64HeaderPhyAddr,,用于内存压缩、dump文件等场景
/// \n strides:
/// 图像3个通道数据的stride，对应VIDEO_FRAME_INFO_S.stVFrame.u32Stride \n pts:
/// 图像的pts，暂不使用，对应VIDEO_FRAME_INFO_S.stVFrame.u64PTS \n
/// ar_frame_handle:
/// 对应AR_MPI_VI_GetChnFrame(0,0,frame,-1)中的出参，调用release接口时会用到

typedef struct NRDpFrameData {
  union {
    struct {
      uint32_t
          frame_id; /**< 图像的id，对应VIDEO_FRAME_INFO_S.stVFrame.u32FrameId*/
      uint32_t width; /**< 图像的宽，对应VIDEO_FRAME_INFO_S.stVFrame.u32Width*/
      uint32_t
          height; /**< 图像的高，对应VIDEO_FRAME_INFO_S.stVFrame.u32Height*/
      NRFrameBufferFormat
          pixel_format; /**<
                           图像的像素格式，对应VIDEO_FRAME_INFO_S.stVFrame.enPixelFormat*/
      NRVector3u64
          data; /**<
                   图像3个通道数据的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64VirAddr，能在client进程读写*/
      NRVector3u64
          data_ext; /**<
                       图像3个通道数据的物理地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64PhyAddr*/
      NRVector3u64
          header; /**<
                     图像header3个通道数据的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64HeaderVirAddr*/
      NRVector3u64
          header_ext; /**<
                         图像header3个通道数据的物理地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64HeaderPhyAddr*/
      NRVector3u
          strides; /**<
                      图像3个通道数据的stride，对应VIDEO_FRAME_INFO_S.stVFrame.u32Stride*/
      uint64_t
          pts; /**<
                  图像的pts，暂不使用，对应VIDEO_FRAME_INFO_S.stVFrame.u64PTS*/
      uint64_t
          ar_frame_handle; /**<
                              对应AR_MPI_VI_GetChnFrame(0,0,frame,-1)中的出参，调用release接口时会用到*/
    };
    uint8_t padding[256];
  };

} NRDpFrameData;

#pragma pack()

/// @brief 从 DP 模块获取当前的一帧图像，对应AR_MPI_VI_GetChnFrame(0,0,frame,-1)
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param data 图像数据
/// @param timsout_ms 获取图像帧的超时时间，单位毫秒
/// @return 结果
/// \n BSP参考code(以yuv420为例):
/// @code
///	XR_BSP_API NRResult NRDpGetFrame(NRDpFrameData *nr_dp_frame, uint32_t
///timeout_ms)
///{
///    VIDEO_FRAME_INFO_S pstFrameInfo;
///    AR_S32 ret = pfn_AR_MPI_VI_GetChnFrame(0, 0, &pstFrameInfo, timeout_ms);
///    if (AR_SUCCESS != ret)
///    {
///        BOARD_LOG_ERROR("AR_MPI_VI_GetChnFrame error: {:#x}", ret);
///        return NR_RESULT_FAILURE;
///    }
///    nr_dp_frame->ar_frame_handle = (uint64_t)(&pstFrameInfo);
///    nr_dp_frame->data.x = pstFrameInfo.stVFrame.u64VirAddr[0];
///    nr_dp_frame->data.y = pstFrameInfo.stVFrame.u64VirAddr[1];
///    nr_dp_frame->data.z = pstFrameInfo.stVFrame.u64VirAddr[2];
///    nr_dp_frame->data_ext.x = pstFrameInfo.stVFrame.u64PhyAddr[0];
///    nr_dp_frame->data_ext.y = pstFrameInfo.stVFrame.u64PhyAddr[1];
///    nr_dp_frame->data_ext.z = pstFrameInfo.stVFrame.u64PhyAddr[2];
///    nr_dp_frame->frame_id = pstFrameInfo.stVFrame.u32FrameId;
///    nr_dp_frame->width = pstFrameInfo.stVFrame.u32Width;
///    nr_dp_frame->height = pstFrameInfo.stVFrame.u32Height;
///    nr_dp_frame->stride.x = pstFrameInfo.stVFrame.u32Stride[0];
///    nr_dp_frame->stride.y = pstFrameInfo.stVFrame.u32Stride[1];
///    nr_dp_frame->stride.z = pstFrameInfo.stVFrame.u32Stride[2];
///    nr_dp_frame->pts = pstFrameInfo.stVFrame.u64PTS;
///    nr_dp_frame->pixel_format = NR_FRAME_BUFFER_FORMAT_YUV420_PLANAR;
///    return NR_RESULT_SUCCESS;
///}
/// @endcode

NRResult NRDpGetFrame(NRDpFrameData *, uint32_t);

/// @brief 根据 userdata 释放图像数据，对应AR_MPI_VI_ReleaseChnFrame(0,0,frame)
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param frame 要释放的NRDpFrameData实例对应指针
/// @return 结果
/// \n BSP参考code:
/// @code
/// XR_BSP_API NRResult NRDpReleaseFrame(const NRDpFrameData *frame)
/// {
///     AR_S32 ret = pfn_AR_MPI_VI_ReleaseChnFrame(0, 0, (const
///     VIDEO_FRAME_INFO_S *)(frame->ar_frame_handle)); if (AR_SUCCESS != ret)
///     {
///         BOARD_LOG_ERROR("AR_MPI_VI_ReleaseChnFrame error: {:#x}", ret);
///         return NR_RESULT_FAILURE;
///     }
///     return NR_RESULT_SUCCESS;
/// }
/// @endcode

NRResult NRDpReleaseFrame(const NRDpFrameData *);

/// @brief 根据width，height，对DpInput数据进行Downscale/Upscale操作
/// \n 可以接受实际scale之后的image宽/高不严格等于输入值
/// \n 具体width，height，stride等，数值以Get到的VIDEO_FRAME_INFO_S为准
/// \n air-like:
/// \n light:
/// \n gina: new
/// @param width 目标图像宽度
/// @param height 目标图像高度
/// @return 结果

NRResult NRDpResizeFrame(uint32_t width, uint32_t height);

/// @brief 获取 dp 数据压缩是否启用 (目前版本先不用)
/// \n 获取 dp 的 CF50 数据压缩功能 是否启用
/// @return 结果

NRResult NRDpGetDataCompressionEnable(NREnableValue *);

/// @brief 设置 dp 数据压缩是否启用(目前版本先不用)
/// \n 设置 dp 的 CF50 数据压缩功能 是否启用
/// @return 结果

NRResult NRDpSetDataCompressionEnable(NREnableValue);
/// @}