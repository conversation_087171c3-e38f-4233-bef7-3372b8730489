#include <heron/util/types.h>

#include <frame_embedded_info.hpp>

using namespace msg;
namespace heron
{
    inline void MsgFov4fToFov4f(Fov4f &out, const MFov4f &in)
    {
        out.left_tan = in.left_tan;
        out.right_tan = in.right_tan;
        out.top_tan = in.top_tan;
        out.bottom_tan = in.bottom_tan;
    }

    inline void MsgTransformToTransform(Transform &out, const MTransform &in)
    {
        out.rotation.w() = in.rotation.qw;
        out.rotation.x() = in.rotation.qx;
        out.rotation.y() = in.rotation.qy;
        out.rotation.z() = in.rotation.qz;
        out.position.x() = in.position.x;
        out.position.y() = in.position.y;
        out.position.z() = in.position.z;
    }

    inline void MsgVec3fToVec3f(Vector3f &out, const MVector3f &in)
    {
        out.x() = in.x;
        out.y() = in.y;
        out.z() = in.z;
    }

}