#pragma once

#include <framework/net/util/net_types.h>
#include <framework/util/bytebuf.h>

// namespace heron::message contains a set of sell-defined message types
// note that this is different than Msg namespace which is script generated
namespace heron::message
{

#pragma pack(1)

    struct MsgVector3f
    {
        float x;
        float y;
        float z;
    };

    struct MsgVector4f
    {
        float x;
        float y;
        float z;
        float w;
    };

    struct MsgTransform
    {
        MsgVector3f position;
        MsgVector4f rotation;
    };

    inline void MsgTransformToTransform(Transform &transform, const MsgTransform &msg_transform)
    {
        transform.position.x() = msg_transform.position.x;
        transform.position.y() = msg_transform.position.y;
        transform.position.z() = msg_transform.position.z;
        transform.rotation.x() = msg_transform.rotation.x;
        transform.rotation.y() = msg_transform.rotation.y;
        transform.rotation.z() = msg_transform.rotation.z;
        transform.rotation.w() = msg_transform.rotation.w;
    }

    inline void TransformToMsgTransform(MsgTransform &msg_transform, const Transform &transform)
    {
        msg_transform.position.x = transform.position.x();
        msg_transform.position.y = transform.position.y();
        msg_transform.position.z = transform.position.z();
        msg_transform.rotation.x = transform.rotation.x();
        msg_transform.rotation.y = transform.rotation.y();
        msg_transform.rotation.z = transform.rotation.z();
        msg_transform.rotation.w = transform.rotation.w();
    }

    enum
    {
        // message type between render and flinger
        MSG_CLOSE_ENCRYPT_REQ = 1,
        MSG_CLOSE_ENCRYPT_RSP,
        MSG_HEART_BEAT,
        MSG_UPDATE_HMD_INFO,
        MSG_DEVICE_USE_QUAD_CHANGED = 1000,
    };

    struct MsgHeader
    {
        uint16_t msg_type{0};
        ConnIDType src_connid_for_proxy{0};
    };

    struct SimpleMsg
    {
        MsgHeader header;
    };

    struct HmdInfoMsg
    {
        MsgHeader header;
        MsgTransform left_pose_from_head;
        MsgTransform right_pose_from_head;
        Fov4f left_fov;
        Fov4f right_fov;
    };

#pragma pack()
    void GenerateDpVsyncInfo(framework::util::ByteBuf& buf);

} // namespace heron::message
