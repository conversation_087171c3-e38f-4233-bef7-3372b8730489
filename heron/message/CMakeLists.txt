file( GLOB GENERATOR_XML "${CMAKE_CURRENT_SOURCE_DIR}/*.xml" )
FRAMEWORK_INIT_PROTO_VARIABLE(${GENERATOR_XML})

PROJECT_INIT_TARGET_VARIABLE(msg)

add_library( ${TARGET_NAME} OBJECT )

target_sources( ${TARGET_NAME}
	PRIVATE
	"$<BUILD_INTERFACE:${TARGET_HEADER_FILES}>"
	"$<BUILD_INTERFACE:${TARGET_SOURCE_FILES}>"
	"$<BUILD_INTERFACE:${TARGET_PROTO_INLINE_FILES}>"
	"$<BUILD_INTERFACE:${TARGET_PROTO_HEADER_FILES}>"
	"$<BUILD_INTERFACE:${TARGET_PROTO_SOURCE_FILES}>"
	"$<BUILD_INTERFACE:${TARGET_PROTO_HEADER_FILES_EXT}>"
	"$<BUILD_INTERFACE:${TARGET_PROTO_SOURCE_FILES_EXT}>"
	${TARGET_HEADER_FILES}
	${TARGET_SOURCE_FILES}
	)

target_include_directories( ${TARGET_NAME}
	PUBLIC	
	"$<BUILD_INTERFACE:${TARGET_INCLUDE_PATH}>"
	"$<BUILD_INTERFACE:${PROJECT_BINARY_DIR}>"
	"$<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}>"
	"$<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>"
	"$<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/inline>"
	"$<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/include>"
	"$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>"
	"$<BUILD_INTERFACE:${PROTO_GEN_DIR}>"
	${EXTERNAL_INCLUDE_DIR}
	${TARGET_INCLUDE_PATH}
	${PROJECT_BINARY_DIR}
	${PROJECT_SOURCE_DIR}
	)
target_link_libraries(${TARGET_NAME}
	PUBLIC
	protobuf::libprotobuf-lite
	warpcore::warpcore
    framework::framework
	)
