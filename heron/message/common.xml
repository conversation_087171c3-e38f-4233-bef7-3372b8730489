<?xml version="1.0" encoding="UTF-8"?>

<msg>
    <data name="MVersion">
        <variable name="major_version" type="int32_t" default="0" tag="1"/>
        <variable name="minor_version" type="int32_t" default="0" tag="2"/>
        <variable name="revision_version" type="int32_t" default="0" tag="3"/>
    </data>
    <data name="MSize2i">
        <variable name="width" type="uint32_t"/>
        <variable name="height" type="uint32_t"/>
    </data>
    <data name="MVector2f">
        <variable name="x" type="float"/>
        <variable name="y" type="float"/>
    </data>
    <data name="MVector3f">
        <variable name="x" type="float"/>
        <variable name="y" type="float"/>
        <variable name="z" type="float"/>
    </data>
    <data name="MVector4f">
        <variable name="x" type="float"/>
        <variable name="y" type="float"/>
        <variable name="z" type="float"/>
        <variable name="w" type="float"/>
    </data>
    <data name="MQuatf">
        <variable name="qx" type="float"/>
        <variable name="qy" type="float"/>
        <variable name="qz" type="float"/>
        <variable name="qw" type="float"/>
    </data>
    <data name="MTransform">
        <variable name="position" type="MVector3f"/>
        <variable name="rotation" type="MQuatf"/>
    </data>
    <data name="MMat4f">
        <variable name="column0" type="MVector4f"/>
        <variable name="column1" type="MVector4f"/>
        <variable name="column2" type="MVector4f"/>
        <variable name="column3" type="MVector4f"/>
    </data>
    <data name="MFov4f">
        <variable name="left_tan" type="float" default="0" />
        <variable name="right_tan" type="float" default="0" />
        <variable name="top_tan" type="float" default="0" />
        <variable name="bottom_tan" type="float" default="0" />
    </data>
    <data name="BufferMetaData">
        <variable name="traget" type="int32_t"/>
        <variable name="pose" type="MTransform"/>
        <variable name="fov" type="MFov4f"/>
        <variable name="plane_point" type="MVector3f"/>
        <variable name="plane_normal" type="MVector3f"/>
    </data>
</msg>

