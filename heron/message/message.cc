#include <frame_embedded_info.hpp>
#include <frame_embedded_info_simple.hpp>
#include <frame_meta_info.hpp>
#include <dp_vsync_info.hpp>
#include <close_encrypt_request.hpp>
#include <close_encrypt_response.hpp>
#include <render_heart_beat.hpp>
#include <update_hmd_info.hpp>

#include <heron/model/model_manager.h>
#include <heron/message/m_type_converter.h>
#include <heron/util/warp.h>
#include <heron/util/nr_types_twin.h>
#include <heron/util/misc.h>
#include <heron/util/debug.h>

using namespace heron;
using namespace heron::model;

void msg::FrameEmbeddedInfo::HandleMessage(NetBase *base, ConnIDType connid)
{
}

void msg::FrameEmbeddedInfoSimple::HandleMessage(NetBase *base, ConnIDType connid)
{
}

void msg::FrameMetaInfo::HandleMessage(NetBase *base, ConnIDType connid)
{
    FrameMetaInfoTwin metadata;
    FrameMetaInfoToFrameMetaInfoTwin(metadata, *this);
    ModelManager::GetInstance()->ReceiveFrameMetaInfo(metadata);
}

void msg::CloseEncryptRequest::HandleMessage(NetBase *base, ConnIDType connid)
{
}

void msg::CloseEncryptResponse::HandleMessage(NetBase *base, ConnIDType connid)
{
}

void msg::RenderHeartBeat::HandleMessage(NetBase *base, ConnIDType connid)
{
}

void msg::UpdateHmdInfo::HandleMessage(NetBase *base, ConnIDType connid)
{
    ModelManager::GetInstance()->host_modified_display_projection_[DISPLAY_USAGE_LEFT] = warp::GetProjectionMatrixFromFov(left_fov.left_tan,
                                                                                                                          left_fov.right_tan,
                                                                                                                          left_fov.top_tan,
                                                                                                                          left_fov.bottom_tan,
                                                                                                                          0.3f, 100.0f);
    Fov4f fov;
    MFov4fToFov4f(fov, left_fov);
    PrintObject("Left fov: ", fov);
    ModelManager::GetInstance()->host_modified_display_projection_[DISPLAY_USAGE_RIGHT] = warp::GetProjectionMatrixFromFov(right_fov.left_tan,
                                                                                                                           right_fov.right_tan,
                                                                                                                           right_fov.top_tan,
                                                                                                                           right_fov.bottom_tan,
                                                                                                                           0.3f, 100.0f);
    MFov4fToFov4f(fov, right_fov);
    PrintObject("Right fov: ", fov);
    MTransformToTransform(ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_LEFT], left_pose_from_head);
    PrintObject("Left pose from head: ", ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_LEFT]);
    MTransformToTransform(ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_RIGHT], right_pose_from_head);
    PrintObject("Right pose from head: ", ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_RIGHT]);
}

void msg::DpVsyncInfo::HandleMessage(NetBase *base, ConnIDType connid)
{
}

namespace heron::message
{
    void GenerateDpVsyncInfo(ByteBuf &buf)
    {
        static uint64_t vsync_count = 0;
        vsync_count++;
        uint64_t now = GetTimeNano();
        //HERON_LOG_DEBUG("Send dp vsync time:{} count:{}", now, vsync_count)
        DpVsyncInfo dp_vsync_info;
        dp_vsync_info.device_dp_vsync_time = now;
        dp_vsync_info.vsync_count = vsync_count;

        dp_vsync_info.Encode(buf);
    }
}
