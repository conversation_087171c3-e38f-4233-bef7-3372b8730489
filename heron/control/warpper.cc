#include <heron/control/warpper.h>
#include <heron/control/control_display.h>
#include <heron/interface_provider/flinger.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/util/math_tools.h>
#include <heron/util/misc.h>
#include <heron/util/warp.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

using namespace heron;
using namespace model;
using namespace dispatch;
using namespace control;
using namespace warp;
using namespace interface_provider;

Warpper::Warpper() : task_queue_("async_warp", 3) {}

void Warpper::Init()
{
    screen_size_pixel_ = ModelManager::GetInstance()->GetScreenSizePixel();
    TOTAL_BLOCK_CNT = DIV_CEIL(screen_size_pixel_.y(), ROW_COUNT_IN_BLOCK);
    TOTAL_CALLBACKS = screen_size_pixel_.y() / (CALLBACK_BLOCK_CNT * ROW_COUNT_IN_BLOCK);
    BLOCK_ID_OFFSET = DebugManager::GetInstance()->normal_timing.warp_delay_block_cnt;
    SPECIAL_INTERRUPT_ID = (TOTAL_BLOCK_CNT - BLOCK_ID_OFFSET) / CALLBACK_BLOCK_CNT - 1; // less than CALLBACK_BLOCK_CNT blocks left
    BLOCK_LEFT_ON_SPECIAL_INTERRUPT = TOTAL_BLOCK_CNT + 1 - (1 + (SPECIAL_INTERRUPT_ID + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET);
    OVERWRITE_BLOCK_CNT = DebugManager::GetInstance()->normal_timing.overwrite_block_cnt;
    WARP_BLOCK_CNT_AT_EACH_CALLBACK = DebugManager::GetInstance()->normal_timing.warp_block_cnt_at_each_callback;
}

void Warpper::StartAsyncWarp()
{
    task_queue_.Start();
}

void Warpper::StopAsyncWarp()
{
    task_queue_.Stop();
}
