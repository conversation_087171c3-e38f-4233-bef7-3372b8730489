#pragma once

#include <heron/model/model_manager.h>
#include <heron/util/types.h>

#include <framework/util/singleton.h>

namespace heron::control
{
    class FocusJudger : public framework::util::Singleton<FocusJudger>
    {
    public:
        void IsLookingAt(const model::SpaceScreenStatus &status,
                         const Transform &head_transform,
                         const Transform &recenter_transform,
                         bool &inner_area, bool &outer_area);
    };

    class FocusStrategy
    {
    public:
        virtual ~FocusStrategy() = default;
        virtual void IsLookingAt(const model::SpaceScreenStatus &status,
                                 const Transform &head_transform,
                                 const Transform &recenter_transform,
                                 bool &inner_area, bool &outer_area) = 0;
    };

    class StrategyArea : public FocusStrategy, public framework::util::Singleton<StrategyArea>
    {
    public:
        void IsLookingAt(const model::SpaceScreenStatus &status,
                         const Transform &head_transform,
                         const Transform &recenter_transform,
                         bool &inner_area, bool &outer_area) override;
    };


    class Strategy6Dof : public FocusStrategy, public framework::util::Singleton<Strategy6Dof>
    {
    public:
        void IsLookingAt(const model::SpaceScreenStatus &status,
                         const Transform &head_transform,
                         const Transform &recenter_transform,
                         bool &inner_area, bool &outer_area) override;
    };

    class Strategy3Dof : public FocusStrategy, public framework::util::Singleton<Strategy3Dof>
    {
    public:
        void IsLookingAt(const model::SpaceScreenStatus &status,
                         const Transform &head_transform,
                         const Transform &recenter_transform,
                         bool &inner_area, bool &outer_area) override;
    };
} // namespace heron::control
