#include <heron/control/control_device.h>
#include <heron/control/control_display.h>
#include <heron/interface_provider/device.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

using namespace heron;
using namespace heron::control;
using namespace heron::interface_provider;

void DeviceCtrl::Start()
{
    return;
    // for debug only
    if (!DebugManager::GetInstance()->control_device_thread)
        return;
    if (running_)
        return;
    running_ = true;
    thread_ = std::thread(&DeviceCtrl::Monitor, this);
}

void DeviceCtrl::Stop()
{
    HERON_LOG_INFO("DeviceCtrl Stopping...")
    if (!running_)
        return;
    running_ = false;
    if (thread_.joinable())
    {
        thread_.join();
    }
    HERON_LOG_INFO("DeviceCtrl Stopped...")
}

void DeviceCtrl::Monitor()
{
    HERON_LOG_DEBUG("DeviceCtrl Monitor thread start");
    while (running_)
    {
        // CheckIsLookingAt();
        CheckCanvasCornerPosition();

        usleep(500 * 1000);
    }
    HERON_LOG_DEBUG("DeviceCtrl Monitor thread quit");
}

void DeviceCtrl::CheckIsLookingAt()
{
    bool is_looking_at_inner, is_looking_at_outer;
    DisplayCtrl::GetInstance()->IsLookingAt(is_looking_at_inner, is_looking_at_outer);
    // HERON_LOG_TRACE("intersection is_looking_at_inner:{} outer:{}", is_looking_at_inner, is_looking_at_outer);
    bool is_looking_at = transparent_ ? is_looking_at_inner : is_looking_at_outer;
    if (is_looking_at)
    {
        looking_at_count_++;
        not_looking_at_count_ = 0;
    }
    else
    {
        looking_at_count_ = 0;
        not_looking_at_count_++;
    }
    if (transparent_ && looking_at_count_ >= RESUME_COUNT)
    {
        ResumeEcLevel();
        transparent_ = false;
    }
    if (!transparent_ && not_looking_at_count_ >= TRANSPARENT_COUNT)
    {
        SetEcTransparent();
        transparent_ = true;
    }
}

void DeviceCtrl::CheckCanvasCornerPosition()
{
    // DisplayCtrl::GetInstance()->CheckCanvasCorners();
}
void DeviceCtrl::SetEcTransparent()
{
    DeviceInterface::GetInstance()->GetEcLevel(&cached_ec_level_);
    if (cached_ec_level_ == 1)
        DeviceInterface::GetInstance()->UpdateEcLevel(OPERATION_DECREASE);
    if (cached_ec_level_ == 2)
        DeviceInterface::GetInstance()->UpdateEcLevel(OPERATION_INCREASE);
}
void DeviceCtrl::ResumeEcLevel()
{
    if (cached_ec_level_ == 1)
        DeviceInterface::GetInstance()->UpdateEcLevel(OPERATION_INCREASE);
    if (cached_ec_level_ == 2)
        DeviceInterface::GetInstance()->UpdateEcLevel(OPERATION_DECREASE);
}
