#include <heron/control/control_display.h>
#include <heron/control/warpper.h>
#include <heron/interface_provider/flinger.h>
#include <heron/model/model_manager.h>
#include <heron/model/permanent_config.h>
#include <heron/dispatch/dpu_manager.h>
#include <heron/dispatch/dp_manager.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/util/debug.h>
#include <heron/util/warp.h>
#include <heron/util/math_tools.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>

#include <bitset>
#include <pthread.h>

using namespace heron;
using namespace control;
using namespace model;
using namespace dispatch;
using namespace interface_provider;

void DisplayCtrl::Start()
{
    if (running_)
        return;
    running_ = true;
    GDCManager::GetInstance()->GetDummyFrame(dummy_frame_info_.dp_frame_data);
    latest_frame_info_got_ = &dummy_frame_info_;
    space_screen_status_in_use_[DISPLAY_USAGE_LEFT] = std::make_shared<SpaceScreenStatus>();
    space_screen_status_in_use_[DISPLAY_USAGE_RIGHT] = std::make_shared<SpaceScreenStatus>();
    get_frame_and_config_gdc_at_ = 0;
    gdc_task_queue_.Start();
    misc_task_queue_.Start();
    DpuManager::GetInstance()->Start();
}

void DisplayCtrl::Stop()
{
    HERON_LOG_INFO("DisplayCtrl Stopping...")
    if (!running_)
        return;
    running_ = false;
    HERON_LOG_INFO("DisplayCtrl misc_task_queue_ stopping...")
    misc_task_queue_.Stop();
    HERON_LOG_INFO("DisplayCtrl misc_task_queue_  Stopped.")
    HERON_LOG_INFO("DisplayCtrl gdc_task_queue_ stopping...")
    gdc_task_queue_.Stop();
    HERON_LOG_INFO("DisplayCtrl gdc_task_queue_  Stopped.")
    // stop dpu
    HERON_LOG_INFO("DisplayCtrl Dpu Stopping...")
    DpuManager::GetInstance()->Stop();
    HERON_LOG_INFO("DisplayCtrl Dpu Stopped.");
}

// when display callback is distroyed, need to flush display with dummy frame
// manually to avoid display hang
void DisplayCtrl::FlushDisplayWithDummyFrame()
{
    if (!running_)
    {
        HERON_LOG_WARN("FlushDisplayWithDummyFrame when DisplayManager is not running.");
        return;
    }
    gdc_task_queue_.ForceAddTask([this]()
                                 { this->FlushDisplayWithDummyFrameInternal(); });
}

void DisplayCtrl::FlushDisplayWithDummyFrameInternal()
{
    SpaceScreenStatus status;
    status.validation.flushed_src_frame = true;
    Result result = GDCManager::GetInstance()->RenderFrame(GDC_USAGE_LEFT_DISPLAY, dummy_frame_info_.dp_frame_data, status, false);
    HERON_LOG_DEBUG("flush left display with dummy frame result: {}", result);
    result = GDCManager::GetInstance()->RenderFrame(GDC_USAGE_RIGHT_DISPLAY, dummy_frame_info_.dp_frame_data, status, false);
    HERON_LOG_DEBUG("flush right display with dummy frame result: {}", result);
}

static bool s_need_reset[DISPLAY_USAGE_COUNT]{false, false};
static uint64_t s_tmp_data_ext_x = 0;
void DisplayCtrl::GdcConfigure()
{
    s_tmp_data_ext_x = (uint64_t)latest_frame_info_got_->dp_frame_data.data_ext[0];
    uint64_t config_gdc_start = GetTimeNano();
    bool left_need_reset = s_need_reset[DISPLAY_USAGE_LEFT];
    s_need_reset[DISPLAY_USAGE_LEFT] = false; // only reset when GDC actually configures(GdcConfigure() might be discarded)
    GDCManager::GetInstance()->RenderFrame(GDC_USAGE_LEFT_DISPLAY, latest_frame_info_got_->dp_frame_data, latest_frame_info_got_->space_screen_status, left_need_reset);
    uint64_t left_gdc_config_done = GetTimeNano();
    bool right_need_reset = s_need_reset[DISPLAY_USAGE_RIGHT];
    s_need_reset[DISPLAY_USAGE_RIGHT] = false;
    GDCManager::GetInstance()->RenderFrame(GDC_USAGE_RIGHT_DISPLAY, latest_frame_info_got_->dp_frame_data, latest_frame_info_got_->space_screen_status, right_need_reset);
    if (!DebugManager::GetInstance()->debug_log.timing_detail)
        return;
    config_gdc_time_cost_[GDC_USAGE_LEFT_DISPLAY].Update(left_gdc_config_done - config_gdc_start);
    config_gdc_time_cost_[GDC_USAGE_RIGHT_DISPLAY].Update(GetTimeNano() - left_gdc_config_done);
}

void DisplayCtrl::GdcDirectConfigure(const DpFrameData &src_frame, const model::SpaceScreenStatus &status,
                                     bool need_reset, uint8_t frame_start)
{
    s_tmp_data_ext_x = (uint64_t)src_frame.data_ext[0];
    GDCManager::GetInstance()->RenderFrame(GDC_USAGE_LEFT_DISPLAY, src_frame, status, need_reset, frame_start);
    GDCManager::GetInstance()->RenderFrame(GDC_USAGE_RIGHT_DISPLAY, src_frame, status, need_reset, frame_start);
}
/*******************************************************
 *
 * Pay attention to the following methods:
 * - OnVsync: only called by left display
 * - OnBlock: only called by left display
 * - OnFrameDone: only called by right display
 *   These methods are called by TWO different threads.
 *
 * ****************************************************/
void DisplayCtrl::OnVsync(DisplayUsage display_usage, const XR_VO_CALLBACK_INFO &callback_info)
{
    if (callback_info.need_reset)
    { // XXX
        HERON_LOG_DEBUG("OnVsync display_usage{} {} need reset", display_usage, __FUNCTION__);
        s_need_reset[display_usage] = callback_info.need_reset;
    }
    // HERON_LOG_TRACE("display{} {} timestamp:{}", display_usage, __FUNCTION__, callback_info.irq_timestamp_ns);
    curr_vsync_time_ns_[display_usage] = callback_info.irq_timestamp_ns;
    Warpper::GetInstance()->PrepareWarp(curr_vsync_time_ns_[display_usage], space_screen_status_in_use_[display_usage], display_usage, 0, callback_info);
    if (vsync_callback_count_[display_usage] % DebugManager::GetInstance()->print_frame_interval == 0 && display_usage == DISPLAY_USAGE_LEFT)
        PrintProfilingInfo(); // always print on the first Vsync
    vsync_callback_count_[display_usage]++;
}

void DisplayCtrl::OnBlock(DisplayUsage display_usage, uint32_t interrupt_id, const XR_VO_CALLBACK_INFO &callback_info)
{
    if (callback_info.need_reset)
    { // XXX
        s_need_reset[display_usage] = callback_info.need_reset;
        HERON_LOG_DEBUG("OnBlock:{} display_usage:{} need reset", interrupt_id, display_usage);
    }
    if (display_usage == DISPLAY_USAGE_LEFT && interrupt_id == get_frame_and_config_gdc_at_)
    { // must before PrepareWarp() so that we can get the latest src frame at SPECIAL_INTERRUPT_ID
        GetLatestSrcFrame();
        MaybeUpdateRecenterMatrix(callback_info.irq_timestamp_ns);
        CheckSrcFrame();
    }
    Warpper::GetInstance()->PrepareWarp(curr_vsync_time_ns_[display_usage], space_screen_status_in_use_[display_usage], display_usage, interrupt_id, callback_info);
    if (display_usage != DISPLAY_USAGE_LEFT)
        return;
    if (interrupt_id == 0)
        CheckGDCAddrMismatch(display_usage);
    if (interrupt_id != get_frame_and_config_gdc_at_)
        return;
    if (DebugManager::GetInstance()->dp_rx_single_buffer)
    { // no need to config GDC in rp_rx_single_buffer mode
        if (!s_need_reset[DISPLAY_USAGE_LEFT] && !s_need_reset[DISPLAY_USAGE_RIGHT])
            return;
    }
    gdc_task_queue_.AddTask([this]()
                            { this->GdcConfigure(); });
}

void DisplayCtrl::OnFrameDone(DisplayUsage display_usage, const XR_VO_CALLBACK_INFO &callback_info)
{
    if (callback_info.need_reset)
    { // XXX
        s_need_reset[display_usage] = callback_info.need_reset;
        HERON_LOG_DEBUG("display_usage{} {} need reset", display_usage, __FUNCTION__);
    }
    // HERON_LOG_TRACE("display{} {} timestamp:{}", display_usage, __FUNCTION__, callback_info.irq_timestamp_ns);
    //  curr_vsync_time_ns_[display_usage] = callback_info.irq_timestamp_ns + ModelManager::GetInstance()->GetDisplayVAfter();
}

void DisplayCtrl::GetLatestSrcFrame()
{
    RingBuffer<FrameInfo> *frame_infos = ModelManager::GetInstance()->GetFrameInfos(FRAME_USAGE_ANY);
    uint32_t index = frame_infos->GetReadableBufferIndex();
    model::FrameInfo *readable_buffer = nullptr; // we don't want latest_frame_info_got_ suddenly be set to null
    if (frame_infos->IsIndexValid(index))
        readable_buffer = frame_infos->GetBuffer(index);
    get_read_count_++;
    if (!readable_buffer)
    {
        latest_frame_info_got_ = &dummy_frame_info_;
        get_readable_error_count_++;
        return;
    }
    latest_frame_info_got_ = readable_buffer;
    latest_frame_info_got_->metadata.timing.frame_apply_ns = GetTimeNano();
    if (DebugManager::GetInstance()->debug_log.timing_detail)
        frame_apply_latency_.Update(latest_frame_info_got_->metadata.timing.frame_apply_ns - latest_frame_info_got_->metadata.timing.dp_rx_done_ns);
}

// return value means result surety
bool DisplayCtrl::IsLookingAt(bool &inner_area, bool &outer_area)
{
    if (!latest_head_transform_valid_)
        return false;
    if (space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->pupil_adjust)
        return false;
    if (space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->scene_mode == SCENE_MODE_WITH_NEBULA)
        return false;
    SpaceScreenStatus space_screen_status = *space_screen_status_in_use_[DISPLAY_USAGE_LEFT];
    FocusJudger::GetInstance()->IsLookingAt(space_screen_status, latest_head_transform_, recenter_transform_, inner_area, outer_area);
    return true;
}

bool DisplayCtrl::InCylinder(bool &inner, bool &outer)
{
    if (!latest_head_transform_valid_)
        return false;
    IsInCylinder(recenter_transform_, latest_head_transform_,
                 space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->GetDepth(),
                 inner, outer);
    return true;
}

// corner order of the canvas itself. [might be different on display]
bool DisplayCtrl::GetCanvasCornerHomogeneous(Vector4f &tl, Vector4f &tr, Vector4f &bl, Vector4f &br)
{
    if (!latest_head_transform_valid_)
        return false;
    Transform quad_transform = recentered_quad_transform_[DISPLAY_USAGE_LEFT];
    Vector2f size_meters;
    space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->GetActualSizeMeters(size_meters);
    Vector3f quad_base_corners[4]{
        Vector3f(-size_meters.x() / 2, size_meters.y() / 2, 0.0f),  // top left
        Vector3f(size_meters.x() / 2, size_meters.y() / 2, 0.0f),   // top right
        Vector3f(-size_meters.x() / 2, -size_meters.y() / 2, 0.0f), // bottom left
        Vector3f(size_meters.x() / 2, -size_meters.y() / 2, 0.0f),  // bottom right
    };
    Vector4f homogeneous[4];
    for (int i = 0; i < 4; i++)
        WorldToHomogeneous(quad_transform.rotation * quad_base_corners[i] + quad_transform.position, latest_head_transform_,
                           ModelManager::GetInstance()->display_center_projection_, homogeneous[i]);
    // HERON_LOG_TRACE("homogeneous values: {} {} {} {}", homogeneous[0].w(), homogeneous[1].w(), homogeneous[3].w(), homogeneous[2].w());
    std::tie(tl, tr, bl, br) = std::make_tuple(homogeneous[0], homogeneous[1], homogeneous[2], homogeneous[3]);
    return true;
}

void DisplayCtrl::PrintProfilingInfo()
{
    HERON_LOG_DEBUG("frame_not_shown:{} readable_buffer_error:{} addr_mismatch:{} pose_error:{}",
                    src_frame_not_shown_count_, get_readable_error_count_, frame_addr_mismatch_count_, get_headpose_error_count_);
}

void DisplayCtrl::PrintStatusInfo(const Vector2f &size_meters, const Vector3f &quad_position)
{
    HERON_LOG_DEBUG("space:{} scene:{} percep:{} dp_in:{} vo_fps:{} duty:{:.2f} src_{}:{}x{} quad:{:.2f}x{:.2f}({:.4f},{:.4f},{:.2f}) fov:({:.2f},{:.2f},{:.2f})",
                    space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->space_mode,
                    space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->scene_mode,
                    space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->perception_type,
                    space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->dp_input_mode,
                    (int)ModelManager::GetInstance()->GetDisplayExpectedFps(), ModelManager::GetInstance()->GetDisplayDutyValue() / 100.0,
                    latest_frame_info_got_->dp_frame_data.frame_id, latest_frame_info_got_->dp_frame_data.width, latest_frame_info_got_->dp_frame_data.height,
                    size_meters.x(), size_meters.y(), quad_position.x(), quad_position.y(), quad_position.z(),
                    space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->GetTargetDirectSizeFactor(),
                    space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->GetTargetFovFactorOnQuadBase(),
                    space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->GetTargetActualFovFactor());
}

void DisplayCtrl::CheckSrcFrame()
{
    uint32_t reason = latest_frame_info_got_->space_screen_status.validation.NotToPresent();
    if (reason)
        src_frame_not_shown_count_++;
    if (last_frame_not_show_reason_ == reason)
        return;
    // reason changed
    last_frame_not_show_reason_ = reason;
    if (reason == 0)
    {
        HERON_LOG_DEBUG("stop using padding color.");
    }
    else
    {
        std::bitset<3> binary(reason);
        HERON_LOG_DEBUG("use padding color reason: {}", binary.to_string());
    }
}

void DisplayCtrl::CheckGDCAddrMismatch(DisplayUsage display_usage)
{
    int tmp = AR_DPTEST_GET_REG_BITS(DebugManager::GetInstance()->va_gdc_reg_base[1] + 0x100 + 1 * 4); // va_get_reg_base[1] corresponds to DISPLAY_USAGE_LEFT, for debug only
    if (DebugManager::GetInstance()->debug_log.frame_data_ext)
    {
        HERON_LOG_DEBUG("get_read_cnt:{} configured_phy_addr:{} addr_using:{} mismatch:{}", get_read_count_,
                        (void *)s_tmp_data_ext_x, (void *)((uint64_t)tmp), s_tmp_data_ext_x != (uint64_t)tmp);
    }
    if (s_tmp_data_ext_x != (uint64_t)tmp)
        frame_addr_mismatch_count_++;
    else
        input_latency_.Update(curr_vsync_time_ns_[display_usage] - latest_frame_info_got_->metadata.timing.dp_rx_done_ns);
}

void DisplayCtrl::MaybeUpdateRecenterMatrix(uint64_t timestamp)
{
    if (!need_update_recenter_matrix_)
        return;
    if (latest_frame_info_got_->space_screen_status.validation.InvalidFrame())
        return;
    if (latest_frame_info_got_->space_screen_status.pupil_adjust)
        return;
    // latest_head_transform_ cannot be used to do recenter when exiting pupil adjust
    Transform head_transform;
    if (FlingerInterface::GetInstance()->GetDevicePose(
            space_screen_status_in_use_[DISPLAY_USAGE_LEFT]->perception_type,
            timestamp, TRACKING_POSE_TYPE_LOW_LATENCY, &head_transform))
    {
        warp::CalcRecenterTransform(head_transform, recenter_transform_);
        if (DebugManager::GetInstance()->use_perception_recenter)
            misc_task_queue_.AddTask([]()
                                     { FlingerInterface::GetInstance()->PerceptionRecenter(); });

        need_update_recenter_matrix_ = false;
    }
}