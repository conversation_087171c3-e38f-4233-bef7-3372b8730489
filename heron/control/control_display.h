#pragma once

#include <heron/model/model_manager.h>
#include <heron/control/focus_judger.h>
#include <heron/util/types.h>
#include <heron/util/counter.h>
#include <heron/util/task_queue.h>
#include <warpcore/warp_interfaces.h>

#include <framework/util/singleton.h>

namespace heron::control
{
    class DisplayCtrl : public framework::util::Singleton<DisplayCtrl>
    {
    public:
        DisplayCtrl() : gdc_task_queue_("config_gdc", 1), misc_task_queue_("misc_tasks", 3) {}

    public:
        void Start();
        void Stop();
        void FlushDisplayWithDummyFrame();

    public:
        void OnVsync(DisplayUsage display_usage, const XR_VO_CALLBACK_INFO &callback_info);
        void OnBlock(DisplayUsage display_usage, uint32_t block_id, const XR_VO_CALLBACK_INFO &callback_info);
        void OnFrameDone(DisplayUsage display_usage, const XR_VO_CALLBACK_INFO &callback_info);

    private:
        void GetLatestSrcFrame();
        void CheckSrcFrame();
        void CheckGDCAddrMismatch(DisplayUsage display_usage);
        void PrepareWarp(DisplayUsage display_usage, uint32_t interrupt_id,
                         const XR_VO_CALLBACK_INFO &callback_info);
        void FlushDisplayWithDummyFrameInternal();
        void MaybeUpdateRecenterMatrix(uint64_t timestamp);

    public:
        void ApplyNextFrameStatus(DisplayUsage display_usage, uint32_t get_frame_and_config_gdc_at);
        void UpdateHeadTransform(Transform &to_transform, uint32_t source_interrupt_id, DisplayUsage display_usage,
                                 uint32_t target_block_id, uint64_t timestamp_nanos);
        void InvalidateLatestHeadTransform()
        {
            latest_head_transform_.position = Vector3f(0, 0, 0);
            latest_head_transform_.rotation = Quatf(0, 0, 1, 0);
            latest_head_transform_valid_ = false;
        }
        void NeedRecenter() { need_update_recenter_matrix_ = true; }
        void ResetRecenterTransform()
        {
            recenter_transform_ = Transform();
        }
        bool IsLookingAt(bool &inner_area, bool &outer_area);
        bool GetCanvasCornerHomogeneous(Vector4f &tl, Vector4f &tr, Vector4f &bl, Vector4f &br);
        bool InCylinder(bool &inner, bool &outer);

        model::FrameInfo *GetLatestFrameInfo() { return latest_frame_info_got_; }

    private:
        model::FrameInfo *latest_frame_info_got_;
        model::FrameInfo dummy_frame_info_;
        SpaceScreenStatusPtr space_screen_status_in_use_[DISPLAY_USAGE_COUNT];
        Transform latest_head_transform_;
        bool latest_head_transform_valid_ = false;
        bool should_assign_latest_head_transform_ = true;
        Transform recentered_quad_transform_[DISPLAY_USAGE_COUNT];
        uint32_t get_frame_and_config_gdc_at_;
        bool need_update_recenter_matrix_ = false;
        Transform recenter_transform_;
        bool showing_good_view_ = true;

    private:
        TaskQueue gdc_task_queue_;
        TaskQueue misc_task_queue_;
        void GdcConfigure();
        std::atomic<bool> running_{false};

    public:
        void GdcDirectConfigure(const DpFrameData &src_frame, const model::SpaceScreenStatus &status,
                                bool need_reset = false, uint8_t frame_start = 0);

    private:
        uint64_t vsync_callback_count_[DISPLAY_USAGE_COUNT]{0};
        uint64_t curr_vsync_time_ns_[DISPLAY_USAGE_COUNT]{0};
        uint32_t get_read_count_{0};
        uint32_t done_read_count_{0};
        uint32_t get_headpose_error_count_{0};
        uint32_t get_headpose_count_{0};
        uint32_t frame_addr_mismatch_count_{0};
        uint32_t get_readable_error_count_{0};
        uint32_t src_frame_not_shown_count_{0};
        uint32_t last_frame_not_show_reason_{0};

        // for profiling
        Average frame_apply_latency_{"dp_rx_done_till_apply"}; // delay from dp_rx_done to "config gdc for next frame"
        Average input_latency_{"input_latency"};               // delay from dp_rx_done to vsync
        Average config_gdc_time_cost_[DISPLAY_USAGE_COUNT]{
            Average("left_gdc_config"),
            Average("right_gdc_config")};
        Average pose_latency_[DISPLAY_USAGE_COUNT]{
            Average("left_pose_latency"),
            Average("right_pose_latency")};

        void PrintProfilingInfo();
        void PrintStatusInfo(const Vector2f &size_meters, const Vector3f &quad_position);
    };
} // namespace heron::control

#include "control_display.inl"
