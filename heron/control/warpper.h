#pragma once

#include <heron/model/model.h>
#include <heron/util/types.h>
#include <heron/util/counter.h>
#include <heron/util/task_queue.h>

#include <framework/util/singleton.h>

namespace heron
{
    class Warpper : public framework::util::Singleton<Warpper>
    {
    public:
        Warpper();
        void Init();
        void StartAsyncWarp();
        void StopAsyncWarp();
        void PrepareWarp(uint64_t curr_vsync_time_ns, SpaceScreenStatusPtr status, DisplayUsage display_usage,
                         uint32_t interrupt_id, const XR_VO_CALLBACK_INFO &callback_info);

        const uint32_t WARP_FPS_SAMPLE_SIZE = 10000;
        CallCounter warp_counter_{"warp", WARP_FPS_SAMPLE_SIZE, 0.0f, std::numeric_limits<float>::max()};

    private:
        void UpdateTiming(SpaceScreenStatusPtr status);
        void PrepareWarpInternal(uint64_t curr_vsync_time_ns, SpaceScreenStatusPtr status, DisplayUsage display_usage,
                                 uint32_t interrupt_id, const XR_VO_CALLBACK_INFO &callback_info);
        void PrepareWarpForInterrupt(uint32_t job_id, uint32_t begin_block_id, uint32_t end_block_id,
                                     uint64_t curr_vsync_time_ns, SpaceScreenStatusPtr status, DisplayUsage display_usage);
        void PrepareWarpForBlock(uint32_t interrupt_id, SpaceScreenStatusPtr status, DisplayUsage display_usage,
                                 uint32_t target_block_id, uint64_t timestamp_nanos);
        void PopulateATWFrameDetail(SpaceScreenStatusPtr status, DisplayUsage display_usage, uint32_t target_block_id, uint64_t now,
                                    uint64_t timestamp_nanos, const Transform &to_transform);

    public:
        int32_t GetTotalCallbacks() { return TOTAL_CALLBACKS; }

    private:
        uint64_t V_BLANK_NS = 405208;
        uint64_t BLOCK_REFRESH_TIME_NS = 321263;
        uint64_t FRAME_INTERVAL_NS = 1000000000 / 90;
        uint64_t DISPLAY_DUTY = FRAME_INTERVAL_NS * 0.35;

        int32_t TOTAL_BLOCK_CNT = 35;
        int32_t TOTAL_CALLBACKS = 8;
        int32_t BLOCK_ID_OFFSET = 4;
        int32_t SPECIAL_INTERRUPT_ID = (TOTAL_BLOCK_CNT - BLOCK_ID_OFFSET) / CALLBACK_BLOCK_CNT - 1; // less then CALLBACK_BLOCK_CNT blocks left
        int32_t BLOCK_LEFT_ON_SPECIAL_INTERRUPT = TOTAL_BLOCK_CNT + 1 - (1 + (SPECIAL_INTERRUPT_ID + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET);
        uint32_t GET_FRAME_AND_CONFIG_GDC_AT = 4;
        uint32_t OVERWRITE_BLOCK_CNT = 0;
        uint32_t WARP_BLOCK_CNT_AT_EACH_CALLBACK = 8;

    private:
        uint32_t frame_count_ = 0;

    private:
        Vector2i screen_size_pixel_;

    private:
        void ApplyNextFrameStatus(DisplayUsage display_usage);

    private:
        TaskQueue task_queue_;

    private:
        Average warp_time_cost_[DISPLAY_USAGE_COUNT]{Average("left_warp"), Average("right_warp")};

    private:
        void PrintTimingVerbose(const FrameMetadataInternal &metadata);
    };
} // namespace heron

#include "warpper.inl"
