#pragma once

#include <heron/util/types.h>

#include <framework/util/singleton.h>

#include <vector>
#include <atomic>
#include <thread>

namespace heron::control
{
    class DeviceCtrl : public framework::util::Singleton<DeviceCtrl>
    {
    public:
        void Start();
        void Stop();
    private:
        void SetEcTransparent();
        void ResumeEcLevel();
        void CheckIsLookingAt();
        void CheckCanvasCornerPosition();

    private:
        int32_t cached_ec_level_ = 0;
        int32_t TRANSPARENT_COUNT = 1;
        int32_t RESUME_COUNT = 1;
        int32_t looking_at_count_ = 0;
        int32_t not_looking_at_count_ = 0;
        bool transparent_ = false;
        std::thread thread_;
        std::atomic<bool> running_{false};
        void Monitor();
    };

} // namespace heron::control

