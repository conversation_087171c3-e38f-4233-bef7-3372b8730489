set(EXPORT_BASENAME HERON)
set(PLATFORM_CONFIG_BASENAME HERON)
set(SYSTEM_CONFIG_BASENAME HERON)
set(VERSION_BASENAME HERON)

add_compile_definitions(AR9481)
add_compile_definitions(NRPLUGIN)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-rtti")

set(TARGET_NAME nr_flinger)
	ADD_PLUGIN_LIBRARIES( ${TARGET_NAME} shared_library static_library provider interface_provider control dispatch model util msg)

add_subdirectory(../external/project/cmake/env env)
add_subdirectory(message)
add_subdirectory(interface)
add_subdirectory(provider)
add_subdirectory(interface_provider)
add_subdirectory(library)
add_subdirectory(control)
add_subdirectory(dispatch)
add_subdirectory(model)
add_subdirectory(util)