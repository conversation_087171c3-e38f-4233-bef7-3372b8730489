#pragma once
#include <heron/util/types.h>

#include <framework/util/singleton.h>
#include <framework/util/json.h>

#define METERS_PER_INCH 0.0254;

namespace heron
{
    class PermanentConfig : public framework::util::Singleton<PermanentConfig>
    {
    private:
        float canvas_base_depth_meters_ = 4.0f;
        float min_canvas_depth_meters_ = 2.0f;
        float max_canvas_depth_meters_ = 10.0f;
        float canvas_depth_step_meters_ = 1.0f;
        float extended_canvas_depth_step_meters_ = 0.1f;

        float presentation_diagonal_fov_degree_ = 50.0f;
        uint32_t default_canvas_fov_index_ = 3;
        std::vector<float> canvas_diagonal_fov_factor_array_ = {0.8f, 0.9f, 1.0f, 1.3f};
        std::vector<float> pupil_adjustment_pixel_array_ = {0, 19.59, 39.185, 58.775, 78.365, 97.96, 117.55};
        float extended_canvas_diagonal_fov_factor_step_ = 0.01f;
        float default_fov_factor_for_ultra_wide_ = 0.92f;
        float default_depth_for_ultra_wide_ = 4.0f;
        float default_fov_factor_for_3D_ = 0.95f;
        float default_depth_for_3D_ = 4.0f;

        float thumbnail_depth_meters_ = 4.0f;
        float thumbnail_diagonal_size_inches_ = 50.0f;
        float thumbnail_size_factor_ = 0.287;
        Vector2f thumbnail_left_position_ = Vector2f(-1.0f, 0.5f);
        Vector2f thumbnail_right_position_ = Vector2f(1.0f, 0.5f);

        SpaceMode space_mode_saved_ = SPACE_MODE_HOVER;
        ThumbnailPositionType thumbnail_position_type_saved_ = THUMBNAIL_POSE_TYPE_LEFT_TOP;
        uint32_t pupil_level_index_saved_ = 6;
        float canvas_size_factor_saved_ = canvas_diagonal_fov_factor_array_[default_canvas_fov_index_];
        float canvas_depth_saved_ = canvas_base_depth_meters_;

        float auto_ec_adjustment_thresh_[2]{0.35f, 0.55f};

    private:
        Rectf base_quad_;

    public:
        const Rectf &GetBaseQuad()
        {
            return base_quad_;
        }

    private:
        float depth_transition_duration_ms_ = 500.0f;
        float size_transition_duration_ms_ = 500.0f;
        float pupil_level_transition_duration_ms_ = 200.0f;

    public:
        float GetDepthTransitionDurationMs() { return depth_transition_duration_ms_; }
        float GetSizeTransitionDurationMs() { return size_transition_duration_ms_; }
        float GetPupilLevelTransitionDurationMs() { return pupil_level_transition_duration_ms_; }

    public:
        void ResetUserConfigs();
        bool ParseConfigs();
        float GetMinCanvasDepthMeters() { return min_canvas_depth_meters_; }
        float GetMaxCanvasDepthMeters() { return max_canvas_depth_meters_; }
        float GetCanvasDepthStepMeters() { return canvas_depth_step_meters_; }
        float GetExtendedCanvasDepthStepMeters() { return extended_canvas_depth_step_meters_; }

        float GetPresentationDiagonalFovDegree() { return presentation_diagonal_fov_degree_; }
        float GetMinCanvasDiagonalFovFactor() { return canvas_diagonal_fov_factor_array_[0]; }
        float GetMaxCanvasDiagonalFovFactor() { return canvas_diagonal_fov_factor_array_[canvas_diagonal_fov_factor_array_.size() - 1]; }
        bool GetNearestFovFactorFromArrayLargerThan(float curr_factor, float *out_factor);
        bool GetNearestFovFactorFromArrayLessThan(float curr_factor, float *out_factor);
        float GetExtendedCanvasDiagonalFovFactorStep() { return extended_canvas_diagonal_fov_factor_step_; }
        float GetDefaultFovFactorForUltraWide() { return default_fov_factor_for_ultra_wide_; }
        float GetDefaultDepthForUltraWide() { return default_depth_for_ultra_wide_; }
        float GetDefaultFovFactorFor3D() { return default_fov_factor_for_3D_; }
        float GetDefaultDepthFor3D() { return default_depth_for_3D_; }
        void SetDefaultFovFactorForThumbnail(float factor) { thumbnail_size_factor_ = factor; }
        float GetDefaultFovFactorForThumbnail() { return thumbnail_size_factor_; }

        float GetThumbnailDiagonalSizeMeters() { return thumbnail_diagonal_size_inches_ * METERS_PER_INCH; }
        void GetThumbnailPosition(Vector3f &position)
        {
            position.x() = thumbnail_position_type_saved_ == THUMBNAIL_POSE_TYPE_LEFT_TOP ? thumbnail_left_position_.x() : thumbnail_right_position_.x();
            position.y() = thumbnail_position_type_saved_ == THUMBNAIL_POSE_TYPE_LEFT_TOP ? thumbnail_left_position_.y() : thumbnail_right_position_.y();
            position.z() = -thumbnail_depth_meters_;
        }

        float GetCanvasDepthSaved() { return canvas_depth_saved_; }
        void SetCanvasDepth(float depth)
        {
            canvas_depth_saved_ = depth;
        }

        float GetCanvasSizeFactorSaved() { return canvas_size_factor_saved_; }
        void SetCanvasSizeFactor(float size_factor)
        {
            canvas_size_factor_saved_ = size_factor;
        }

        SpaceMode GetSpaceModeSaved() { return space_mode_saved_; }
        void SetSpaceMode(SpaceMode mode)
        {
            if (mode == SPACE_MODE_HOVER || mode == SPACE_MODE_FOLLOW || mode == SPACE_MODE_THUMBNAIL)
                space_mode_saved_ = mode;
        }

        ThumbnailPositionType GetThumbnailPositionTypeSaved() { return thumbnail_position_type_saved_; }
        void SetThumbnailPositionType(ThumbnailPositionType type)
        {
            thumbnail_position_type_saved_ = type;
        }

        uint32_t GetPupilLevelIndexSaved() { return pupil_level_index_saved_; }
        void SetPuilLevelIndex(uint32_t level_idx)
        {
            pupil_level_index_saved_ = level_idx;
        }

        float GetPupilAdjustmentPixelByIdx(uint32_t idx)
        {
            return idx >= pupil_adjustment_pixel_array_.size() ? 0.0f : pupil_adjustment_pixel_array_[idx];
        }

        void GetIsLookingAtThresh(float &focus, float &transparent)
        {
            focus = auto_ec_adjustment_thresh_[1];
            transparent = auto_ec_adjustment_thresh_[0];
        };

    private:
        std::vector<uint8_t> blank_frame_color_ = {64, 128, 128};

    public:
        const std::vector<uint8_t> &GetBlankFrameColor() { return blank_frame_color_; }

    private:
        void ParseFlingerDefaultConfig(const std::string &config_string, bool overwrite = false);
        bool ParseUserConfig(const std::string &config_file_full_path);
        bool GenerateUserConfigFileAndDump(const std::string &config_file_full_path);
        void ParseThumbnailConfig(const Json::Value &root);

    public:
        bool UpdateUserConfigFileAndDump();
    };
}
