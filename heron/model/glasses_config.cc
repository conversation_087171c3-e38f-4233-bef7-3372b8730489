#include <heron/model/glasses_config.h>
#include <heron/model/model_manager.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>

#include <framework/util/json.h>

namespace heron
{
    static float CrossNorm(Vector2f p1, Vector2f p2)
    {
        return std::abs(p1.x() * p2.y() - p1.y() * p2.x());
    }

    // 顶点顺序 左上 右上 左下 右下
    static void GenGDCWeightForGrid(std::vector<Vector2f> points, Vector2f srcPoint, std::vector<float> &weights)
    {
        int indexA_1 = 1;
        int indexA_2 = 2;
        int indexB = 0;
        int indexC = 3;

        double minX = points[0].x();
        double maxX = points[1].x();
        double minY = points[0].y();
        double maxY = points[2].y();
        double xScale = (srcPoint.x() - minX) / (maxX - minX);
        double yScale = (srcPoint.y() - minY) / (maxY - minY);
        Vector2f pB(points[indexB].x(), points[indexB].y());
        Vector2f pC(points[indexC].x(), points[indexC].y());
        Vector2f pP(srcPoint.x(), srcPoint.y());
        if (yScale > xScale)
        {
            // 三角形的面积是两个向量叉乘的绝对值的一半（右手系为正）
            Vector2f pA(points[indexA_2].x(), points[indexA_2].y());
            double S = CrossNorm(pB - pA, pC - pA);
            double kA = CrossNorm(pB - pP, pC - pP) / S;
            double kB = CrossNorm(pA - pP, pC - pP) / S;
            double kC = CrossNorm(pA - pP, pB - pP) / S;
            // weights[indexB] = kB;
            // weights[indexA_1] = 0;
            // weights[indexA_2] = kA;
            // weights[indexC] = kC;
            weights[indexB] = kB;
            weights[indexA_1] = kA;
            weights[indexA_2] = 0;
            weights[indexC] = kC;
        }
        else
        {
            Vector2f pA(points[indexA_1].x(), points[indexA_1].y());
            double S = CrossNorm(pB - pA, pC - pA);
            double kA = CrossNorm(pB - pP, pC - pP) / S;
            double kB = CrossNorm(pA - pP, pC - pP) / S;
            double kC = CrossNorm(pA - pP, pB - pP) / S;
            // weights[indexB] = kB;
            // weights[indexA_1] = kA;
            // weights[indexA_2] = 0;
            // weights[indexC] = kC;
            weights[indexB] = kB;
            weights[indexA_1] = 0;
            weights[indexA_2] = kA;
            weights[indexC] = kC;
        }
    }

    static bool ParseGlassesDisplayMetadata(const Json::Value &display)
    {
        if (!display.isMember("resolution"))
        {
            HERON_LOG_ERROR("No resolution in display");
            return false;
        }
        Json::Value resolution = display["resolution"];
        model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_LEFT].width = (uint32_t)(resolution[0].asFloat());
        model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_LEFT].height = (uint32_t)(resolution[1].asFloat());
        model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_RIGHT].width = (uint32_t)(resolution[0].asFloat());
        model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_RIGHT].height = (uint32_t)(resolution[1].asFloat());
        HERON_LOG_DEBUG("resolution in GlassesConfig.json: {}x{}", (uint32_t)(resolution[0].asFloat()), (uint32_t)(resolution[1].asFloat()));
        if (!display.isMember("k_left_display"))
        {
            HERON_LOG_ERROR("No k_left_display in display");
            return false;
        }
        Json::Value k_scr_left = display["k_left_display"];
        for (int i = 0; i < 9; i++)
            model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_LEFT].k_screen[i] = k_scr_left[i].asFloat();

        if (!display.isMember("k_right_display"))
        {
            HERON_LOG_ERROR("No k_right_display in display");
            return false;
        }
        Json::Value k_scr_right = display["k_right_display"];
        for (int i = 0; i < 9; i++)
        {
            model::ModelManager::GetInstance()->display_metadatas_[DISPLAY_USAGE_RIGHT].k_screen[i] = k_scr_right[i].asFloat();
        }
        return true;
    }

    void GenGDCWeight(int grid_height, int grid_width, float *weights)
    {
        std::vector<Vector2f> gridPoint4;
        gridPoint4.push_back(Vector2f(0, 0));
        gridPoint4.push_back(Vector2f(grid_width, 0));
        gridPoint4.push_back(Vector2f(0, grid_height));
        gridPoint4.push_back(Vector2f(grid_width, grid_height));
        std::vector<float> weight_for_grid(4);
        for (int i = 0; i < grid_height; i++)
        { // x = i y = j
            for (int j = 0; j < grid_width; j++)
            {
                Vector2f src(i, j);
                GenGDCWeightForGrid(gridPoint4, src, weight_for_grid);
                weights[(i * grid_height + j) * 4] = weight_for_grid[0];
                weights[(i * grid_height + j) * 4 + 1] = weight_for_grid[1];
                weights[(i * grid_height + j) * 4 + 2] = weight_for_grid[2];
                weights[(i * grid_height + j) * 4 + 3] = weight_for_grid[3];
            }
        }
    }

    bool ParseGlassesConfig(const char *device_config_str, uint32_t device_config_size)
    {
        Json::Value json_root;
        Json::CharReaderBuilder json_builder;
        json_builder["collectComments"] = false;
        JSONCPP_STRING json_errs;
        std::istringstream json_stream(std::string(device_config_str, device_config_size));
        if (!parseFromStream(json_builder, json_stream, &json_root, &json_errs))
        {
            HERON_LOG_ERROR("Parse glasses config error, json_errs = {}", json_errs.c_str());
            return false;
        }

        if (!json_root.isMember("display"))
        {
            HERON_LOG_ERROR("No display metadata in config");
            return false;
        }
        const Json::Value &display = json_root["display"];
        if (!ParseGlassesDisplayMetadata(display))
            return false;

        const Json::Value &distortion_data = json_root["display_distortion"];
        if (!json_root.isMember("display_distortion"))
        {
            HERON_LOG_ERROR("No display_distortion in config");
            return false;
        }
        return true;
    }

} // namespace heron
