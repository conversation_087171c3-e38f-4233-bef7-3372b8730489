#pragma once

#include <heron/model/model.h>
#include <heron/util/ringbuffer.h>

#include <framework/util/singleton.h>

#include <warpcore/warp_interfaces.h>

#include <map>

#define FRAME_META_DATA_COUNT 100
namespace heron::model
{
    using FramebufferQueuePtr = std::shared_ptr<FramebufferQueue>;
    using OverlayFrameDataPtr = std::shared_ptr<OverlayFrameData>;

    class ModelManager : public framework::util::Singleton<ModelManager>
    {
    public:
        enum
        {
            FRAME_INFO_COUNT_FOR_DP = 3,
        };

    public:
        ModelManager();
        RingBuffer<FrameInfo> *GetFrameInfos(FrameUsage usage)
        {
            return &frame_infos_[usage];
        }

    public:
        void UpdateHMDParams();

    private:
        std::vector<FramebufferQueuePtr> osd_frame_buffer_queues_;
        std::vector<std::vector<void *>> osd_buffer_queue_raw_data_;
        std::vector<OverlayFrameDataPtr> blank_overlay_frame_infos_;

    public:
        OverlayFrameDataPtr GetBlankOverlayFrameInfoPtr(DisplayUsage display)
        {
            return (blank_overlay_frame_infos_.size() - 1) < display ? nullptr : blank_overlay_frame_infos_[display];
        }
        void UpdateOsdFramebufferFormat(FramebufferFormat format);
        bool AllocOSDFramebufferQueue(uint32_t count);
        void DeallocOSDBuffer() {};
        FramebufferQueuePtr GetLatestFrameBufferQueue() { return osd_frame_buffer_queues_.back(); }
        OverlayFrameDataPtr GetOSDReadableBuffer(DisplayUsage display_usage);
        void DoneReadOSDBuffer(DisplayUsage display_usage)
        {
            osd_double_buffers_[display_usage].DoneReadBuffer();
        }
        void SubmitOSDFrame(DisplayUsage display_usage, const void *virtual_address)
        { // use virtual_address as key of osd_frame_info
            osd_double_buffers_[display_usage].Submit(&(osd_frame_info_map_[(uint64_t)virtual_address]));
        }

        bool GetFrameMetaInfo(uint64_t frame_number, uint64_t &receive_time_ns, FrameMetaInfoTwin &metadata);
        void ReceiveFrameMetaInfo(const FrameMetaInfoTwin &metadata);

    private:
        RingBuffer<FrameInfo> frame_infos_[FrameUsage::FRAME_USAGE_COUNT];
        // RingBufferHoldNextR<FrameInfo> frame_infos_[FrameUsage::FRAME_USAGE_COUNT];
        DoubleBuffer<OverlayFrameDataPtr> osd_double_buffers_[DISPLAY_USAGE_COUNT];
        std::map<uint64_t, OverlayFrameDataPtr> osd_frame_info_map_;

        // The first is frame number
        std::map<uint64_t, std::pair<uint64_t, FrameMetaInfoTwin>> dp_frame_metadata_map_;
        uint64_t min_frame_number_ = 0;
        std::recursive_mutex frame_metadata_mutex_;

        // msg::FrameMetaInfo* dp_frame_meta_list_first_ = nullptr;
        // msg::FrameMetaInfo* dp_frame_meta_list_last_ = nullptr;
        // msg::FrameMetaInfo[FRAME_META_DATA_COUNT] dp_frame_meta_datas_;

    public:
        GDCQuadWarpPtr GetGDCWarp(DisplayUsage display) { return gdc_warp_[display]; }
        GDCCylinderWarpPtr GetCylinderWarp(DisplayUsage display) { return gdc_cylinder_warp_[display]; }
        HostWarpPtr GetHostWarp(DisplayUsage display) { return host_warp_[display]; }
        HostPtwWarpPtr GetHostPtwWarp(DisplayUsage display) { return host_ptw_warp_[display]; }
        void InitWarp();

    private:
        GDCQuadWarpPtr gdc_warp_[DISPLAY_USAGE_COUNT];
        GDCCylinderWarpPtr gdc_cylinder_warp_[DISPLAY_USAGE_COUNT];
        HostWarpPtr host_warp_[DISPLAY_USAGE_COUNT];
        HostPtwWarpPtr host_ptw_warp_[DISPLAY_USAGE_COUNT];

    public:
        Vector2i GetScreenSizePixel();
        Vector2i GetDpSrcSizePixel();
        bool InitDpVideoPipelineParamsAndCheck();

    private:
        std::shared_ptr<DpVideoPipelineParam> dp_video_pipeline_param_ = nullptr;

    public:
        DisplayMetaData display_metadatas_[DISPLAY_USAGE_COUNT];
        bool InitDistortionMeshData();
        Transform display_pose_from_head_[DISPLAY_USAGE_COUNT];
        Transform host_pose_from_head_[DISPLAY_USAGE_COUNT];
        Mat4f host_modified_display_projection_[DISPLAY_USAGE_COUNT];
        void ResetHostModifiedDisplayProjection()
        {
            host_modified_display_projection_[DISPLAY_USAGE_LEFT] = origional_display_projection_[DISPLAY_USAGE_LEFT];
            host_modified_display_projection_[DISPLAY_USAGE_RIGHT] = origional_display_projection_[DISPLAY_USAGE_RIGHT];
        };
        Mat4f origional_display_projection_[DISPLAY_USAGE_COUNT];
        Mat4f display_center_projection_;

    private:
        std::shared_ptr<SpaceScreenStatus> space_screen_status_;
        Rectf default_quad_base_calced_{-1.5f, 1.5f, -1.5f, 1.5f};
        Vector2f calced_full_screen_size_meters_at_depth_for_pupil_adjust_;
        float soft_min_fov_factor_ = 0.8f;
        float soft_max_fov_factor_ = 1.3f;
        Vector2f soft_min_fov_degree_{0.0f, 0.0f};
        Vector2f soft_max_fov_degree_{0.0f, 0.0f};
        float normal_mode_canvas_direct_size_factor_ = 1.0f;
        float presentation_size_factor_ = 1.0f; // screen size of configured fov over screen size of calced fov at the same distance

    private:
        void SyncSpaceScreenStatus();
        bool SpaceScreenInNormalMode();

    public:
        void UpdateNormalModeCanvasDirectSizeFactor(float factor);
        // Full screen fov for presentation is hard coded in the config file, which is the same for all glasses.
        // We also have an actual calculated full screen fov ,which is different for different glasses.
        // The presentation size factor is the ratio of the two.
        float GetPresentationSizeFactor() { return presentation_size_factor_; }
        Vector2f GetSoftMinFovDegree() { return soft_min_fov_degree_; };
        Vector2f GetSoftMaxFovDegree() { return soft_max_fov_degree_; };

        std::shared_ptr<const SpaceScreenStatus> GetSpaceScreenStatus() const
        {
            return space_screen_status_;
        }
        void SetPupilAdjustFlag(bool flag);

        void SetDpInputMode(DpInputMode mode);
        DpInputMode GetDpInputMode()
        {
            return space_screen_status_->dp_input_mode;
        }
        void UpdateHostProjection(float left_tan, float right_tan, float top_tan, float bottom_tan, DisplayUsage display_usage);

        PerceptionType GetLocalPerceptionType() { return space_screen_status_->perception_type; }
        void SetLocalPerceptionType(PerceptionType type) { space_screen_status_->perception_type = type; }
        SpaceMode GetSpaceMode()
        {
            return space_screen_status_->space_mode;
        }
        void SetSpaceModeAndSave(SpaceMode mode);
        void GetThumbnailPositionType(ThumbnailPositionType *out_type)
        {
            *out_type = space_screen_status_->thumbnail_position_type;
        }
        void SetThumbnailPositionTypeAndSave(ThumbnailPositionType type);

        void GetBaseQuadSizeMeters(Vector2f &out_size_meters)
        {
            out_size_meters = space_screen_status_->GetTargetBaseSizeMeters();
        }
        void UpdateCanvasSizeAndSave(OperationType operation_type, StepType step_type);
        void ResetDirectCanvasSizeFactor();
        float GetTargetDepth() { return space_screen_status_->GetTargetDepth(); }
        void UpdateQuadStatus() { space_screen_status_->Update(); };
        void UpdateCanvasDepthAndSave(OperationType operation_type, StepType step_type);

    private:
        void GetRoisByPupilLevelIdx(uint32_t idx, Rectf *out_left_roi, Rectf *out_right_roi);
        void UpdateQuadBaseByPupilLevelIdx(uint32_t idx);

    public:
        bool SetPupilLevelAndSave(int32_t idx);

    public:
        void PopulateSavedValueFromConfig();

    private:
        uint64_t display_frame_interval_ns_ = 11111111;
        uint64_t display_block_interval_ns_ = 321263;
        uint64_t display_v_blank_ns_ = 405208;
        uint64_t display_v_after_ns_ = 37584;
        float expected_display_fps_ = 90.0;
        uint32_t duty_value_ = 30;

    public:
        void UpdateDisplayTimingInfo(float fps);
        uint64_t GetDisplayFrameRefreshInterval() { return display_frame_interval_ns_; }
        uint64_t GetDisplayBlockRefreshInterval() { return display_block_interval_ns_; }
        uint64_t GetDisplayVBlank() { return display_v_blank_ns_; }
        uint64_t GetDisplayVAfter() { return display_v_after_ns_; }
        uint64_t GetDisplayExpectedFps() { return expected_display_fps_; };
        uint32_t GetDisplayDutyValue() { return duty_value_; }
        void SetDisplayDutyValue(uint32_t duty_value) { duty_value_ = duty_value; }

    public:
        static const Vector2i DEFAULT_OSD_QUAD_SIZE_PIXEL;

        static const std::vector<int32_t> PUPIL_LEVEL_ARRAY;
        static const uint32_t DEFAULT_PUPIL_LEVEL_INDEX;

        static const std::string USER_CONFIG_FILE_NAME;

        static const float MISC_V_BLANK_PERCENTAGE;
        static const float MISC_V_AFTER_PERCENTAGE;
        static const float MISC_V_BLANK_PERCENTAGE_1200;
        static const float MISC_V_AFTER_PERCENTAGE_1200;
    };

} // namespace heron::model
