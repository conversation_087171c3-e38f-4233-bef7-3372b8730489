#pragma once

#include <heron/util/types.h>
#include <heron/util/math_tools.h>
#include <heron/message/msg_types_twin.h>

#include <memory>

namespace heron::model
{
    const float DEPTH_METERS_FOR_PUPIL_ADJUST = 4.0f;

    class DisplayMetaData
    {
    public:
        uint32_t width;
        uint32_t height;
        float k_screen[9];
        DistortionInfo distortion_info;
    };

    class SpaceScreenStatus
    {
    public:
        struct Validation
        {
            bool first_frames_to_ignore = false;
            bool flushed_src_frame = true;
            bool bad_view = true;
            uint32_t InvalidFrame() const
            {
                uint32_t reason = 0 | flushed_src_frame;
                return reason;
            }
            uint32_t NotToPresent() const
            {
                uint32_t reason = 0 | first_frames_to_ignore;
                reason <<= 1;
                reason |= bad_view;
                reason <<= 1;
                reason |= flushed_src_frame;
                return reason;
            }
        };
        SceneMode scene_mode = SCENE_MODE_SPACE_SCREEN;
        DpInputMode dp_input_mode = DP_INPUT_MODE_MONO;
        SpaceMode space_mode = SPACE_MODE_HOVER;
        ThumbnailPositionType thumbnail_position_type = THUMBNAIL_POSE_TYPE_LEFT_TOP;
        PerceptionType perception_type = PERCEPTION_TYPE_3DOF;
        bool pupil_adjust = false;
        Mat4f host_projection[DISPLAY_USAGE_COUNT];
        Validation validation;

    private:
        Transform quad_transform;
        Interpolator<float> interpolated_depth;
        Interpolator<float> interpolated_direct_size_factor;
        Interpolator<Rectf> interpolated_quad_base;

        float target_base_factor = 1.0f; // only used for debug log print

    private:
        bool use_quad_base_center = true;

    public:
        void SetTargetQuadBase(const Rectf &base, float factor, uint32_t transition_ms = 0);
        void SetTargetDirectSizeFactor(float factor, uint32_t transition_ms = 0);
        void SetTargetDepth(float depth, uint32_t transition_ms = 0);
        void ApplyQuadBaseCenter();
        void ApplyThumbnailPosition(const Vector3f &position);
        // we sometimes need this even when SpaceScreen is not in "NormalMode"
        float GetNormalModeTargetFovFactorOnQuadBase() const;
        float GetTargetFovFactorOnQuadBase() const;
        float GetTargetActualFovFactor() const;
        void GetActualFovDegree(float &h_angle, float &v_angle) const;
        void GetActualSizeMeters(Vector2f &out_size_meters) const;
        void GetTransform(Transform &out_transform) const;
        // also considering ULTRA_WIDE
        float GetTargetActualDiagonalSizeMeters() const;
        float GetDepth() const { return -quad_transform.position.z(); }
        float GetTargetDepth() const { return interpolated_depth.GetTarget(); }
        float GetTargetDirectSizeFactor() const { return interpolated_direct_size_factor.GetTarget(); }
        Vector2f GetTargetBaseSizeMeters() const
        {
            Rectf quad_base = interpolated_quad_base.GetTarget();
            return Vector2f(abs(quad_base.left - quad_base.right), abs(quad_base.top - quad_base.bottom));
        }

        void Update();

    private:
        void GetTargetActualSizeMeters(Vector2f &out_size_meters) const;
    };

    class FrameInfo
    {
    public:
        FrameUsage usage{FrameUsage::FRAME_USAGE_ANY};
        void *nr_dp_frame{nullptr};
        DpFrameData dp_frame_data;
        SpaceScreenStatus space_screen_status;
        bool gotten{false}; // means nr_dp_frame is populated by DpGetFrame(), not local generated
        FrameMetadataInternal metadata;
        Vector2f target_size_factor;
    };
    class OSDFrameInfo
    {
    public:
        uint32_t width;
        uint32_t height;
        FramebufferFormat format;
        uint32_t data_length;
        void *virtual_address{nullptr};
        uint64_t physical_address = 0;
    };
} // namespace heron::model

using SpaceScreenStatusPtr = std::shared_ptr<heron::model::SpaceScreenStatus>;