#include <heron/model/model_manager.h>
#include <heron/model/permanent_config.h>
#include <heron/interface_provider/generic.h>
#include <heron/interface_provider/flinger.h>
#include <heron/interface_provider/hmd.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/util/debug.h>
#include <heron/util/math_tools.h>
#include <heron/util/warp.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>
#include <heron/message/m_type_converter.h>

#include <framework/util/util.h>

using namespace heron;
using namespace heron::model;
using namespace heron::interface_provider;

const Vector2i ModelManager::DEFAULT_OSD_QUAD_SIZE_PIXEL(960, 540);
/* collected when VO_fps=90 1920x1080
static uint64_t block_average_interval_ns[10]{
    37584,   // last frame_done to vsync
    1680000, // vsync to block0(128)
    1274792, // block0(128) to block1(256)
    1279646,
    1274771,
    1267875,
    1279021,
    1272542,
    1273041,
    471896}; // block7(1024) to frame_done
*/
const float ModelManager::MISC_V_BLANK_PERCENTAGE = 0.03647; //(1680000 - 1274792) / 11111111
const float ModelManager::MISC_V_AFTER_PERCENTAGE = 0.00338; // 37584 / 11111111

/* collected when VO_fps=90 1920x1200
callback_interval last to vsync:426496
callback_interval last to callback0:1511117.**********
callback_interval last to callback1:1147379.**********
callback_interval last to callback2:1146350.**********
callback_interval last to callback3:1146230.273064516
callback_interval last to callback4:1148068.**********
callback_interval last to callback5:1145151.**********
callback_interval last to callback6:1146189.3606774195
callback_interval last to callback7:1148598.460516129
callback_interval last to callback8:1145528.9822580644
1200 line total time = (1147380.0 + 1146350.3 +1146230.3 +1148068.4 +1145151.6 +1146189.4 +1148598.5 +1145529.0) / 8 / 128 * 1200 = 10750190
128 line time = (1147380.0 + 1146350.3 +1146230.3 +1148068.4 +1145151.6 +1146189.4 +1148598.5 +1145529.0) / 8  = 1146687.1875
*/
const float ModelManager::MISC_V_BLANK_PERCENTAGE_1200 = 0.03280; //(1511118 - 1146687) / 11111111
const float ModelManager::MISC_V_AFTER_PERCENTAGE_1200 = 0.0;     // (11111111 - (1511118 - 1146687) - 10750190) / 11111111 is negative...

const std::vector<int32_t> ModelManager::PUPIL_LEVEL_ARRAY = {-6, -5, -4, -3, -2, -1, 0, 1, 2, 3, 4, 5, 6};
const uint32_t ModelManager::DEFAULT_PUPIL_LEVEL_INDEX = 6;

const std::string ModelManager::USER_CONFIG_FILE_NAME = "flinger_user_config.json";

ModelManager::ModelManager()
    : frame_infos_{
          RingBuffer<FrameInfo>(FRAME_INFO_COUNT_FOR_DP),
          RingBuffer<FrameInfo>(FRAME_INFO_COUNT_FOR_DP)}
{
    space_screen_status_ = std::make_shared<SpaceScreenStatus>();
}

bool ModelManager::InitDpVideoPipelineParamsAndCheck()
{
    ResolutionInfo res_info;
    GenericInterface::GetInstance()->GetDisplayResolutionInfo(&res_info);
    HERON_LOG_DEBUG("GetDisplayResolutionInfo: {}x{}@{}", res_info.width, res_info.height, res_info.refresh_rate);
    dp_video_pipeline_param_ = std::make_shared<DpVideoPipelineParam>(SRC_FRAME_WIDTH_DEFAULT, SRC_FRAME_HEIGHT_DEFAULT, res_info.width, res_info.height);
    UpdateDisplayTimingInfo(res_info.refresh_rate);
    if (DebugManager::GetInstance()->use_arbitrary_dp_video_pipeline_param)
    {
        dp_video_pipeline_param_ = std::make_shared<DpVideoPipelineParam>(DebugManager::GetInstance()->dp_video_pipeline_param);
        HERON_LOG_DEBUG("forcely set dp_video_pipeline_param src:{}x{} screen:{}x{}",
                        dp_video_pipeline_param_->SRC_WIDTH, dp_video_pipeline_param_->SRC_HEIGHT,
                        dp_video_pipeline_param_->SCREEN_WIDTH, dp_video_pipeline_param_->SCREEN_HEIGHT);
    }
    if (display_metadatas_[DISPLAY_USAGE_LEFT].width != dp_video_pipeline_param_->SCREEN_WIDTH || display_metadatas_[DISPLAY_USAGE_LEFT].height != dp_video_pipeline_param_->SCREEN_HEIGHT)
    {
        HERON_LOG_ERROR("resolution:{}x{} in GlassesConfig.json doesn't match BSP API screen resolution:{}x{}.",
                        display_metadatas_[DISPLAY_USAGE_LEFT].width, display_metadatas_[DISPLAY_USAGE_LEFT].height,
                        dp_video_pipeline_param_->SCREEN_WIDTH, dp_video_pipeline_param_->SCREEN_HEIGHT);
        return false;
    }
    if (dp_video_pipeline_param_->SCREEN_HEIGHT != 1080) // XXX: silicon level hard code
        DebugManager::GetInstance()->lines_64_enable = false;
    return true;
}

Vector2i ModelManager::GetScreenSizePixel()
{
    if (!dp_video_pipeline_param_)
    {
        HERON_LOG_ERROR("{} DpVideoPipelineParam not initialized", __FUNCTION__);
        usleep(500 * 1000);
        std::abort();
    }
    return Vector2i(dp_video_pipeline_param_->SCREEN_WIDTH, dp_video_pipeline_param_->SCREEN_HEIGHT);
}

Vector2i ModelManager::GetDpSrcSizePixel()
{
    if (!dp_video_pipeline_param_)
    {
        HERON_LOG_ERROR("{} DpVideoPipelineParam not initialized", __FUNCTION__);
        usleep(500 * 1000);
        std::abort();
    }
    return Vector2i(dp_video_pipeline_param_->SRC_WIDTH, dp_video_pipeline_param_->SRC_HEIGHT);
}

void ModelManager::PopulateSavedValueFromConfig()
{
    Fov4f left_fov;
    HMDInterface::GetInstance()->GetComponentFov(COMPONENT_DISPLAY_LEFT, &left_fov);
    float x = abs(left_fov.left_tan) + abs(left_fov.right_tan);
    float y = abs(left_fov.top_tan) + abs(left_fov.bottom_tan);
    Vector2f tmp_size(x, y);
    Vector2i dp_src_size_pixel = GetDpSrcSizePixel();
    Vector2i screen_size_pixel = GetScreenSizePixel();
    Rectf left_screen_roi{0, (float)screen_size_pixel.x(), 0, (float)screen_size_pixel.y()};
    Rectf right_screen_roi{0, (float)screen_size_pixel.x(), 0, (float)screen_size_pixel.y()};
    FlingerInterface::GetInstance()->CalcuQuadSize(&left_screen_roi, &right_screen_roi, &dp_src_size_pixel,
                                                   DEPTH_METERS_FOR_PUPIL_ADJUST, &default_quad_base_calced_);
    PrintObject("CalcuQuadSize left roi", left_screen_roi);
    PrintObject("CalcuQuadSize right roi", right_screen_roi);
    PrintObject("CalcuQuadSize dp resolution", dp_src_size_pixel);
    PrintObject("CalcuQuadSize out quad rect", default_quad_base_calced_);
    calced_full_screen_size_meters_at_depth_for_pupil_adjust_.x() = abs(default_quad_base_calced_.right - default_quad_base_calced_.left);
    calced_full_screen_size_meters_at_depth_for_pupil_adjust_.y() = abs(default_quad_base_calced_.bottom - default_quad_base_calced_.top);
    soft_min_fov_factor_ = PermanentConfig::GetInstance()->GetMinCanvasDiagonalFovFactor();
    soft_max_fov_factor_ = PermanentConfig::GetInstance()->GetMaxCanvasDiagonalFovFactor();
    soft_min_fov_degree_ = Vector2f(
        atan(calced_full_screen_size_meters_at_depth_for_pupil_adjust_.x() / 2 * soft_min_fov_factor_ / DEPTH_METERS_FOR_PUPIL_ADJUST) / M_PI * 180.0 * 2,
        atan(calced_full_screen_size_meters_at_depth_for_pupil_adjust_.y() / 2 * soft_min_fov_factor_ / DEPTH_METERS_FOR_PUPIL_ADJUST) / M_PI * 180.0 * 2);
    soft_max_fov_degree_ = Vector2f(
        atan(calced_full_screen_size_meters_at_depth_for_pupil_adjust_.x() / 2 * soft_max_fov_factor_ / DEPTH_METERS_FOR_PUPIL_ADJUST) / M_PI * 180.0 * 2,
        atan(calced_full_screen_size_meters_at_depth_for_pupil_adjust_.y() / 2 * soft_max_fov_factor_ / DEPTH_METERS_FOR_PUPIL_ADJUST) / M_PI * 180.0 * 2);
    presentation_size_factor_ = tan(PermanentConfig::GetInstance()->GetPresentationDiagonalFovDegree() / 2 * M_PI / 180.0f) /
                                (calced_full_screen_size_meters_at_depth_for_pupil_adjust_.norm() / DEPTH_METERS_FOR_PUPIL_ADJUST / 2);
    float thumbnail_diagnoal_size_meters = PermanentConfig::GetInstance()->GetThumbnailDiagonalSizeMeters();
    PermanentConfig::GetInstance()->SetDefaultFovFactorForThumbnail((thumbnail_diagnoal_size_meters / DEPTH_METERS_FOR_PUPIL_ADJUST / 2) /
                                                                    tan(PermanentConfig::GetInstance()->GetPresentationDiagonalFovDegree() / 2 * M_PI / 180.0f));
    HERON_LOG_DEBUG("diagonal_fov_degree:{:.2f} calced:{:.2f} presentation_factor:{:.2f}",
                    atan(tmp_size.norm() / 2) / M_PI * 180.0 * 2,
                    atan(calced_full_screen_size_meters_at_depth_for_pupil_adjust_.norm() / DEPTH_METERS_FOR_PUPIL_ADJUST / 2) / M_PI * 180.0 * 2,
                    presentation_size_factor_);
    space_screen_status_->SetTargetDepth(PermanentConfig::GetInstance()->GetCanvasDepthSaved());
    space_screen_status_->SetTargetDirectSizeFactor(PermanentConfig::GetInstance()->GetCanvasSizeFactorSaved());
    normal_mode_canvas_direct_size_factor_ = space_screen_status_->GetTargetDirectSizeFactor();
    space_screen_status_->space_mode = PermanentConfig::GetInstance()->GetSpaceModeSaved();
    space_screen_status_->thumbnail_position_type = PermanentConfig::GetInstance()->GetThumbnailPositionTypeSaved();
    UpdateQuadBaseByPupilLevelIdx(PermanentConfig::GetInstance()->GetPupilLevelIndexSaved());
    HERON_LOG_DEBUG("Flinger permenent configs populated.");
}

void ModelManager::UpdateHMDParams()
{
    for (uint32_t display = 0; display < DISPLAY_USAGE_COUNT; ++display)
    {
        HMDInterface::GetInstance()->GetComponentPoseFromHead((Component)display, &display_pose_from_head_[display]);
        host_pose_from_head_[display] = display_pose_from_head_[display];
        string str = display == DISPLAY_USAGE_LEFT ? "left display " : "right display ";
        PrintObject(str + "pose", display_pose_from_head_[DISPLAY_USAGE_LEFT]);
        Fov4f display_fov;
        HMDInterface::GetInstance()->GetComponentFov((Component)display, &display_fov);
        PrintObject(str + "fov", display_fov);
        origional_display_projection_[display] = warp::GetProjectionMatrixFromFov(display_fov.left_tan,
                                                                                  display_fov.right_tan,
                                                                                  display_fov.top_tan,
                                                                                  display_fov.bottom_tan,
                                                                                  0.3f, 100.0f);
        host_modified_display_projection_[display] = origional_display_projection_[display];
    }
    Fov4f center_eye_fov;
    HMDInterface::GetInstance()->GetComponentFov(COMPONENT_HEAD, &center_eye_fov);
    PrintObject("center eye fov", center_eye_fov);
    display_center_projection_ = warp::GetProjectionMatrixFromFov(center_eye_fov.left_tan,
                                                                  center_eye_fov.right_tan,
                                                                  center_eye_fov.top_tan,
                                                                  center_eye_fov.bottom_tan,
                                                                  0.3f, 100.0f);
}

void ModelManager::UpdateDisplayTimingInfo(float fps)
{
    if (DebugManager::GetInstance()->arbitrary_vo_fps)
        fps = DebugManager::GetInstance()->default_vo_fps;
    expected_display_fps_ = fps;
    display_frame_interval_ns_ = 1000000000 / fps;
    Vector2i screen_size_pixel = GetScreenSizePixel();
    if (screen_size_pixel.y() == 1080)
    {
        display_v_blank_ns_ = MISC_V_BLANK_PERCENTAGE * display_frame_interval_ns_;
        display_v_after_ns_ = MISC_V_AFTER_PERCENTAGE * display_frame_interval_ns_;
    }
    if (screen_size_pixel.y() == 1200)
    {
        display_v_blank_ns_ = MISC_V_BLANK_PERCENTAGE_1200 * display_frame_interval_ns_;
        display_v_after_ns_ = MISC_V_AFTER_PERCENTAGE_1200 * display_frame_interval_ns_;
    }
    display_block_interval_ns_ = (display_frame_interval_ns_ - display_v_blank_ns_ - display_v_after_ns_) / screen_size_pixel.y() * ROW_COUNT_IN_BLOCK;
    HERON_LOG_DEBUG("update display timing fps:{:.2f} frame:{} v_blank:{} v_after:{} block:{}",
                    expected_display_fps_, display_frame_interval_ns_, display_v_blank_ns_, display_v_after_ns_, display_block_interval_ns_);
}

void ModelManager::UpdateQuadBaseByPupilLevelIdx(uint32_t idx)
{
    Rectf left_screen_roi, right_screen_roi;
    GetRoisByPupilLevelIdx(idx, &left_screen_roi, &right_screen_roi);
    HERON_LOG_DEBUG("pupil level:{} roi left:{},{},{},{}, right:{}, {}, {}, {}", PUPIL_LEVEL_ARRAY[idx],
                    left_screen_roi.left, left_screen_roi.right, left_screen_roi.top, left_screen_roi.bottom,
                    right_screen_roi.left, right_screen_roi.right, right_screen_roi.top, right_screen_roi.bottom);
    Vector2i screen_size_pixel = ModelManager::GetInstance()->GetScreenSizePixel();
    float base_factor = abs(right_screen_roi.left - right_screen_roi.right) / (float)screen_size_pixel.x();
    Rectf new_quad_base = default_quad_base_calced_ * base_factor;
    HERON_LOG_DEBUG("pupil level:{} base_factor:{:.2f} new_quad_base:{:.4f},{:.4f},{:.4f},{:.4f}",
                    PUPIL_LEVEL_ARRAY[idx], base_factor, new_quad_base.left, new_quad_base.right, new_quad_base.top, new_quad_base.bottom);
    space_screen_status_->SetTargetQuadBase(new_quad_base, base_factor, PermanentConfig::GetInstance()->GetPupilLevelTransitionDurationMs());
    space_screen_status_->ApplyQuadBaseCenter();
}

bool ModelManager::InitDistortionMeshData()
{
    for (uint32_t display = 0; display < DISPLAY_USAGE_COUNT; ++display)
    {
        Vector2i distortion_mesh_size;
        if (!HMDInterface::GetInstance()->GetComponentDisplayDistortionSize((Component)display, &distortion_mesh_size))
        {
            HERON_LOG_ERROR("get display{} distortion size failed", display);
            return false;
        }
        int32_t num_cols = DIV_CEIL(display_metadatas_[display].width, COLUMN_COUNT_IN_BLOCK) + 1;
        int32_t num_rows = DIV_CEIL(display_metadatas_[display].height, ROW_COUNT_IN_BLOCK) + 1;
        if (distortion_mesh_size.x() != num_cols || distortion_mesh_size.y() != num_rows)
        {
            HERON_LOG_ERROR("display{} HMD distortion size:{}x{} not match size calced:{}x{}",
                            display, distortion_mesh_size.x(), distortion_mesh_size.y(), num_cols, num_rows);
            return false;
        }
        display_metadatas_[display].distortion_info.num_rows = num_rows;
        display_metadatas_[display].distortion_info.num_columns = num_cols;
        HERON_LOG_DEBUG("display{} distortion_size:{}x{}", display, num_cols, num_rows);
        vector<float> mesh_data;
        uint32_t mesh_data_float_count = num_cols * num_rows * 4;
        mesh_data.assign(mesh_data_float_count, 0.0f);
        if (!HMDInterface::GetInstance()->GetComponentDisplayDistortionData((Component)display, mesh_data_float_count, mesh_data.data()))
        {
            HERON_LOG_ERROR("get display{} distortion data failed", display);
            return false;
        }
        float dis_pt_x, dis_pt_y, dis_grid_x, dis_grid_y;
        model::ModelManager::GetInstance()->display_metadatas_[display].distortion_info.mesh_for_gdc.clear();
        for (int32_t i = 0; i < num_rows * num_cols; ++i)
        {
            int32_t col = i % num_cols;
            int32_t row = i / num_cols;
            dis_grid_x = col == 0 ? 0.0 : col * 32.0 - 1.0;
            dis_grid_y = row == 0 ? 0.0 : row * 32.0 - 1.0;
            if (DebugManager::GetInstance()->gdc_configs[display].use_identity_mesh)
            {
                dis_pt_x = dis_grid_x;
                dis_pt_y = dis_grid_y;
            }
            else
            {
                dis_pt_x = mesh_data[4 * i + 2];
                dis_pt_y = mesh_data[4 * i + 3];
            }
            display_metadatas_[display].distortion_info.identity_mesh_for_gdc.emplace_back(dis_grid_x);
            display_metadatas_[display].distortion_info.identity_mesh_for_gdc.emplace_back(dis_grid_y);
            display_metadatas_[display].distortion_info.mesh_for_gdc.emplace_back(dis_pt_x);
            display_metadatas_[display].distortion_info.mesh_for_gdc.emplace_back(dis_pt_y);
        }
    }
    return true;
}

void ModelManager::InitWarp()
{
    Transform default_transform;
    Vector2f canvas_base_size_meters;
    Vector2i dp_src_size_pixel = ModelManager::GetInstance()->GetDpSrcSizePixel();
    GetBaseQuadSizeMeters(canvas_base_size_meters);
    for (uint32_t display = 0; display < DISPLAY_USAGE_COUNT; ++display)
    {
        float fc[2]{display_metadatas_[display].k_screen[0],
                    display_metadatas_[display].k_screen[4]};
        float cc[2]{display_metadatas_[display].k_screen[2],
                    display_metadatas_[display].k_screen[5]};
        HERON_LOG_DEBUG("{} fc:{:.4f},{:.4f}, cc:{:.4f},{:.4f}", display == 0 ? "left" : "right", fc[0], fc[1], cc[0], cc[1]);
        gdc_warp_[display] = GDCQuadWarp::Create();
        gdc_warp_[display]->WarpInit(fc, cc, dp_src_size_pixel, canvas_base_size_meters, default_transform);
        gdc_cylinder_warp_[display] = GDCCylinderWarp::Create();
        gdc_cylinder_warp_[display]->WarpInit(fc, cc, dp_src_size_pixel, canvas_base_size_meters, default_transform);
        host_warp_[display] = HostWarp::Create();
        host_ptw_warp_[display] = HostPtwWarp::Create();
    }
}

void ModelManager::UpdateOsdFramebufferFormat(FramebufferFormat format)
{

    for (auto it = osd_frame_info_map_.begin(); it != osd_frame_info_map_.end(); ++it)
    {
        it->second->format = format;
    }
    for (auto &it : blank_overlay_frame_infos_)
    {
        it->format = format;
    }
}
bool ModelManager::AllocOSDFramebufferQueue(uint32_t count)
{
    FramebufferQueuePtr framebuffer_queue = std::make_shared<FramebufferQueue>();
    framebuffer_queue->buffer_count = count;
    std::vector<void *> tmp;
    for (uint32_t i = 0; i < count; i++)
    {
        OverlayFrameDataPtr overlay_frame_info = std::make_shared<OverlayFrameData>();
        overlay_frame_info->format = FRAMEBUFFER_FORMAT_BGRA_8888;
        if (!dispatch::DispatcherWrapper::GetInstance()->AllocOverlayFrame(overlay_frame_info.get()))
        {
            HERON_LOG_ERROR("AllocOsd vb frame{}/{} failure", i + 1, count);
            return false;
        }
        osd_frame_info_map_[(uint64_t)overlay_frame_info->data_data] = overlay_frame_info;
        tmp.emplace_back((void *)overlay_frame_info->data_data);
    }
    osd_buffer_queue_raw_data_.emplace_back(tmp);
    framebuffer_queue->buffer_queue = osd_buffer_queue_raw_data_.back().data();
    osd_frame_buffer_queues_.emplace_back(framebuffer_queue);
    if (count == 2)
        return true;
    // prepare black_overlay_frame_infos_ when count == 1
    return true;
}

OverlayFrameDataPtr ModelManager::GetOSDReadableBuffer(DisplayUsage display_usage)
{
    OverlayFrameDataPtr *tmp_ptr = osd_double_buffers_[display_usage].GetReadableBuffer();
    if (!tmp_ptr)
    {
        HERON_LOG_WARN("GetReadableBuffer error in {}", __FUNCTION__);
        return nullptr;
    }
    return *tmp_ptr;
};

void ModelManager::UpdateCanvasSizeAndSave(OperationType operation_type, StepType step_type)
{
    if (space_screen_status_->space_mode == SPACE_MODE_THUMBNAIL)
    {
        HERON_LOG_WARN("in SPACE_MODE_THUMBNAIL, not doing anything");
        return;
    }
    float fov_factor = space_screen_status_->GetNormalModeTargetFovFactorOnQuadBase();
    HERON_LOG_DEBUG("fov_factor: {} min:{} max: {}", fov_factor, soft_min_fov_factor_, soft_max_fov_factor_);
    float new_fov_factor = fov_factor;
    if (fov_factor <= soft_min_fov_factor_ && operation_type == OPERATION_DECREASE)
        return;
    if (fov_factor >= soft_max_fov_factor_ && operation_type == OPERATION_INCREASE)
        return;
    if (step_type == STEP_TYPE_NORMAL || soft_min_fov_factor_ - fov_factor > EPS || fov_factor - soft_max_fov_factor_ > EPS)
    {
        if (operation_type == OPERATION_INCREASE)
        {
            if (!PermanentConfig::GetInstance()->GetNearestFovFactorFromArrayLargerThan(fov_factor, &new_fov_factor))
                return;
        }
        if (operation_type == OPERATION_DECREASE)
        {
            if (!PermanentConfig::GetInstance()->GetNearestFovFactorFromArrayLessThan(fov_factor, &new_fov_factor))
                return;
        }
    }
    else
    {
        float fov_factor_step = PermanentConfig::GetInstance()->GetExtendedCanvasDiagonalFovFactorStep();
        if (operation_type == OPERATION_INCREASE)
            new_fov_factor += fov_factor_step;
        else
            new_fov_factor -= fov_factor_step;
        if (new_fov_factor > soft_max_fov_factor_ || new_fov_factor < soft_min_fov_factor_)
            return;
    }
    space_screen_status_->SetTargetDirectSizeFactor(
        new_fov_factor * space_screen_status_->GetTargetDepth() / DEPTH_METERS_FOR_PUPIL_ADJUST,
        PermanentConfig::GetInstance()->GetSizeTransitionDurationMs());
    UpdateNormalModeCanvasDirectSizeFactor(space_screen_status_->GetTargetDirectSizeFactor());
    PermanentConfig::GetInstance()->SetCanvasSizeFactor(space_screen_status_->GetTargetDirectSizeFactor());
    PermanentConfig::GetInstance()->UpdateUserConfigFileAndDump();
}

void ModelManager::UpdateCanvasDepthAndSave(OperationType operation_type, StepType step_type)
{
    if (space_screen_status_->space_mode == SPACE_MODE_THUMBNAIL)
    {
        HERON_LOG_WARN("in SPACE_MODE_THUMBNAIL, not doing anything");
        return;
    }
    float depth_step = PermanentConfig::GetInstance()->GetCanvasDepthStepMeters();
    float new_depth = space_screen_status_->GetTargetDepth();
    if (step_type == STEP_TYPE_EXTENDED)
        depth_step = PermanentConfig::GetInstance()->GetExtendedCanvasDepthStepMeters();
    float max_depth = PermanentConfig::GetInstance()->GetMaxCanvasDepthMeters();
    float min_depth = PermanentConfig::GetInstance()->GetMinCanvasDepthMeters();
    if (operation_type == OPERATION_INCREASE)
        new_depth = space_screen_status_->GetTargetDepth() + depth_step > max_depth ? max_depth : space_screen_status_->GetTargetDepth() + depth_step;
    else
        new_depth = space_screen_status_->GetTargetDepth() - depth_step < min_depth ? min_depth : space_screen_status_->GetTargetDepth() - depth_step;
    // HERON_LOG_DEBUG("new_depth: {}", new_depth);
    space_screen_status_->SetTargetDepth(new_depth, PermanentConfig::GetInstance()->GetDepthTransitionDurationMs());
    PermanentConfig::GetInstance()->SetCanvasDepth(space_screen_status_->GetTargetDepth());
    // check if actual_fov valid after adjusting depth
    // XXX: save limited value to file when actual_fov exceeds limit
    float fov_factor = space_screen_status_->GetNormalModeTargetFovFactorOnQuadBase();
    if (fov_factor <= soft_min_fov_factor_)
        PermanentConfig::GetInstance()->SetCanvasSizeFactor(soft_min_fov_factor_ * space_screen_status_->GetTargetDepth() / DEPTH_METERS_FOR_PUPIL_ADJUST);
    if (fov_factor >= soft_max_fov_factor_)
        PermanentConfig::GetInstance()->SetCanvasSizeFactor(soft_max_fov_factor_ * space_screen_status_->GetTargetDepth() / DEPTH_METERS_FOR_PUPIL_ADJUST);
    PermanentConfig::GetInstance()->UpdateUserConfigFileAndDump();
}

void ModelManager::ResetDirectCanvasSizeFactor()
{
    normal_mode_canvas_direct_size_factor_ = 1.0f;
    space_screen_status_->SetTargetDirectSizeFactor(1.0f);
}

bool ModelManager::SetPupilLevelAndSave(int32_t idx)
{
    if (idx < 0 || idx >= (int32_t)PUPIL_LEVEL_ARRAY.size())
    {
        HERON_LOG_ERROR("invalid pupil level {} expect:[0, {}]", idx, PUPIL_LEVEL_ARRAY.size() - 1);
        return false;
    }
    UpdateQuadBaseByPupilLevelIdx(idx);
    PermanentConfig::GetInstance()->SetPuilLevelIndex((uint32_t)idx);
    PermanentConfig::GetInstance()->UpdateUserConfigFileAndDump();
    return true;
}

void ModelManager::GetRoisByPupilLevelIdx(uint32_t idx, Rectf *out_left_roi, Rectf *out_right_roi)
{
    Vector2i screen_size_pixel = ModelManager::GetInstance()->GetScreenSizePixel();
    out_left_roi->top = 0;
    out_left_roi->bottom = screen_size_pixel.y();
    out_right_roi->top = 0;
    out_right_roi->bottom = screen_size_pixel.y();
    int32_t real_pupil_level = PUPIL_LEVEL_ARRAY[idx];
    if (real_pupil_level >= 0)
    {
        out_left_roi->left = PermanentConfig::GetInstance()->GetPupilAdjustmentPixelByIdx(abs(real_pupil_level));
        out_left_roi->right = screen_size_pixel.x();
        out_right_roi->left = 0;
        out_right_roi->right = screen_size_pixel.x() - PermanentConfig::GetInstance()->GetPupilAdjustmentPixelByIdx(abs(real_pupil_level));
    }
    else
    {
        out_left_roi->left = 0;
        out_left_roi->right = screen_size_pixel.x() - PermanentConfig::GetInstance()->GetPupilAdjustmentPixelByIdx(abs(real_pupil_level));
        out_right_roi->left = PermanentConfig::GetInstance()->GetPupilAdjustmentPixelByIdx(abs(real_pupil_level));
        out_right_roi->right = screen_size_pixel.x();
    }
}
void ModelManager::SetThumbnailPositionTypeAndSave(ThumbnailPositionType type)
{
    space_screen_status_->thumbnail_position_type = type;
    PermanentConfig::GetInstance()->SetThumbnailPositionType(type);
    SyncSpaceScreenStatus();
    PermanentConfig::GetInstance()->UpdateUserConfigFileAndDump();
}

bool ModelManager::SpaceScreenInNormalMode()
{
    if (space_screen_status_->dp_input_mode == DP_INPUT_MODE_STEREO)
        return false;
    if (space_screen_status_->space_mode == SPACE_MODE_ULTRA_WIDE)
        return false;
    if (space_screen_status_->space_mode == SPACE_MODE_THUMBNAIL)
        return false;
    return true;
}

// Be careful with normal_mode_canvas_direct_size_factor_:
//  1. may be different than canvas_size_factor_saved_
//  2. may be different than space_screen_status_->direct_size_factor
void ModelManager::UpdateNormalModeCanvasDirectSizeFactor(float factor)
{
    if (SpaceScreenInNormalMode())
    {
        normal_mode_canvas_direct_size_factor_ = factor;
    }
    else
    {
        HERON_LOG_WARN("should not update canvas size. Not doing anything. dp_input:{} space_mode:{}",
                       space_screen_status_->dp_input_mode, space_screen_status_->space_mode);
    }
}

// private function, to sync space screen size/depth when space mode/dp input mode changed
void ModelManager::SyncSpaceScreenStatus()
{
    if (space_screen_status_->scene_mode == SCENE_MODE_WITH_NEBULA)
        return;
    space_screen_status_->ApplyQuadBaseCenter();
    if (space_screen_status_->space_mode == SPACE_MODE_THUMBNAIL)
    {
        Vector3f position;
        PermanentConfig::GetInstance()->GetThumbnailPosition(position);
        space_screen_status_->ApplyThumbnailPosition(position);
        space_screen_status_->SetTargetDirectSizeFactor(PermanentConfig::GetInstance()->GetDefaultFovFactorForThumbnail());
        return;
    }
    if (space_screen_status_->space_mode == SPACE_MODE_ULTRA_WIDE)
    {
        space_screen_status_->SetTargetDirectSizeFactor(PermanentConfig::GetInstance()->GetDefaultFovFactorForUltraWide());
        return;
    }
    if (space_screen_status_->dp_input_mode == DP_INPUT_MODE_STEREO)
    {
        space_screen_status_->SetTargetDepth(PermanentConfig::GetInstance()->GetDefaultDepthFor3D());
        space_screen_status_->SetTargetDirectSizeFactor(PermanentConfig::GetInstance()->GetDefaultFovFactorFor3D() * space_screen_status_->GetTargetDepth() / DEPTH_METERS_FOR_PUPIL_ADJUST);
        return;
    }
    space_screen_status_->SetTargetDepth(PermanentConfig::GetInstance()->GetCanvasDepthSaved());
    space_screen_status_->SetTargetDirectSizeFactor(normal_mode_canvas_direct_size_factor_);
}

void ModelManager::SetSpaceModeAndSave(SpaceMode mode)
{
    space_screen_status_->space_mode = mode;
    SyncSpaceScreenStatus();
    PermanentConfig::GetInstance()->SetSpaceMode(mode);
    PermanentConfig::GetInstance()->UpdateUserConfigFileAndDump();
}

void ModelManager::SetDpInputMode(DpInputMode mode)
{
    space_screen_status_->dp_input_mode = mode;
    SyncSpaceScreenStatus();
}

void ModelManager::SetPupilAdjustFlag(bool flag)
{
    if (!flag)                   // no need to sync status on enterring pupil_adjust
        SyncSpaceScreenStatus(); // sync status before flag setting
    space_screen_status_->pupil_adjust = flag;
}

void ModelManager::UpdateHostProjection(float left_tan, float right_tan, float top_tan, float bottom_tan, DisplayUsage display_usage)
{
    space_screen_status_->host_projection[display_usage] = warp::GetProjectionMatrixFromFov(left_tan, right_tan, top_tan, bottom_tan, 0.1f, 100.0f);
}

bool ModelManager::GetFrameMetaInfo(uint64_t frame_number, uint64_t &receive_time_ns, FrameMetaInfoTwin &metadata)
{
    // HERON_LOG_TRACE("GetFrameMetaInfo for {}", frame_number)
    std::unique_lock<std::recursive_mutex> lock(frame_metadata_mutex_);
    auto it = dp_frame_metadata_map_.find(frame_number);
    if (it != dp_frame_metadata_map_.end())
    {
        receive_time_ns = it->second.first;
        metadata = it->second.second;
        // HERON_LOG_TRACE("Success to GetFrameMetaData for {}", frame_number)
        return true;
    }

    // for (auto it = dp_frame_metadata_map_.begin(); it != dp_frame_metadata_map_.end(); ++it)
    // {
    //     HERON_LOG_DEBUG("GetFrameMetaData dp_frame_metadata_map_ frame buffer {}", it->second.frame_number)
    // }

    return false;
}

static Average s_msg_transfer("msg_transfer");
static Average s_msg_interval("msg_interval");
static uint32_t s_metadata_msg_count = 0;
void ModelManager::ReceiveFrameMetaInfo(const FrameMetaInfoTwin &metadata)
{
    DebugManager* p_dm = DebugManager::GetInstance();
    // HERON_LOG_TRACE("ReceiveFrameMetaInfo {}", meta_info.frame_number)
    ++s_metadata_msg_count;
    static uint64_t last_set_time = 0;
    uint64_t now = GetTimeNano();
    int64_t delta = now - last_set_time;
    // 若间隔小于1ms，则说明存在粘包情况
    if (delta < 1000000)
    {
        HERON_LOG_DEBUG("Get frame meta data too close.")
    }
    last_set_time = now;
    int64_t usb_transfer_time = now - metadata.data_device_time;
    if (p_dm->debug_log.timing_detail || p_dm->profile_atw)
    {
        s_msg_interval.Update(delta);
        s_msg_transfer.Update(usb_transfer_time);
    }
    float usb_transfer_time_ms = (float)usb_transfer_time / 1000000.0f;
    if (metadata.metadata_vec.size() != 2)
    {
        HERON_LOG_WARN("unsupported metadata_vec size: {} on ReceiveFrameMetaInfo", metadata.metadata_vec.size());
        return;
    }
    std::unique_lock<std::recursive_mutex> lock(frame_metadata_mutex_);
    dp_frame_metadata_map_[metadata.frame_number] = {now, metadata};
    int32_t frame_range = (int32_t)(metadata.frame_number - min_frame_number_);
    while (frame_range >= 100)
    {
        // HERON_LOG_TRACE("Delete older FrameMetaData {}", min_frame_number_)
        dp_frame_metadata_map_.erase(min_frame_number_);
        min_frame_number_ = min_frame_number_ + 1;
        frame_range = (int32_t)(metadata.frame_number - min_frame_number_);
    }

    if (!p_dm->debug_log.msg_metadata || s_metadata_msg_count % p_dm->print_frame_interval != 0)
        return;
    PrintObject("ReceiveFrameMetaInfo", metadata);
    // for (auto it = dp_frame_metadata_map_.begin(); it != dp_frame_metadata_map_.end(); ++it)
    // {
    //     HERON_LOG_DEBUG("SetFrameMetaData dp_frame_metadata_map_ frame buffer {}", it->second.frame_number)
    // }
}
