#pragma once

#include <heron/util/types.h>

#include <framework/util/os_time.h>

#include <vector>
#include <string>

namespace heron
{
    /* need to zero out the ticklist array before starting */
    /* average will ramp up until the buffer is full */
    /* returns average calls per second over the samples last calls */
    /* no print when print_interval_sec is 0 */
    class CallCounter
    {
    public:
        CallCounter(std::string name, uint32_t samples, float expected, float warnning_thresh)
            : name_(name), samples_(samples), expected_calls_per_second_(expected), warnning_thresh_(warnning_thresh),
              tick_list_(samples, 0) {}
        void Update();
        void SetExpectedCallCountsPerSecond(float expected) { expected_calls_per_second_ = expected; }
        float GetExpectedCallCountsPerSecond() { return expected_calls_per_second_; }

    private:
        std::string name_;

        int samples_;
        int tick_index_ = 0;
        uint64_t tick_sum_ = 0;
        float expected_calls_per_second_;
        float warnning_thresh_;
        std::vector<uint64_t> tick_list_;
        uint64_t last_tick_ = 0;

        uint64_t last_print_timestamp_ = 0;
        const uint32_t PRINT_INTERVAL_SEC = 10;
    };

    class Distribution
    {
    public:
        Distribution(std::string name, int64_t start, int64_t step, uint32_t size, bool enable,
                     uint64_t print_interval_ns = 2 * OS_NS_PER_SEC);
        void Update(int64_t candidate, bool log_trace = false);
        void Clear();

    private:
        void Print(uint64_t now);

    private:
        std::string name_;
        int64_t start_;
        int64_t step_;
        uint32_t size_;
        bool enable_;

        uint64_t print_interval_ns_;

    private:
        uint64_t samples_ever_since_ = 0;
        uint64_t samples_since_last_print_ = 0;
        uint64_t valid_samples_ever_since_ = 0;
        uint64_t valid_samples_since_last_print_ = 0;
        int64_t min_ever_since_ = 1000000000;
        int64_t max_ever_since_ = 0;
        int64_t min_since_last_print_ = 1000000000;
        int64_t max_since_last_print_ = 0;

    private:
        uint64_t last_print_ns_ = 0;

        std::vector<int64_t> statistics_ever_since_;
        std::vector<int64_t> statistics_since_last_print_;
        std::vector<int64_t> bars_;
    };

    class Average
    {
    public:
        Average(std::string name, bool enable = true, uint64_t print_interval_ns = (uint64_t)10 * OS_NS_PER_SEC);
        void Update(int64_t candidate);

    private:
        void Print(uint64_t now);

    private:
        std::string name_;
        bool enable_;
        int64_t min_ever_since_ = std::numeric_limits<int64_t>::max();
        int64_t max_ever_since_ = std::numeric_limits<int64_t>::min();
        int64_t min_since_last_print_ = std::numeric_limits<int64_t>::max();
        int64_t max_since_last_print_ = std::numeric_limits<int64_t>::min();

        int64_t sum_ever_since_ = 0;
        int64_t sum_since_last_print_ = 0;

        uint64_t count_ever_since_ = 0;
        uint64_t count_since_last_print_ = 0;

        uint64_t print_interval_ns_;
        uint64_t last_print_ns_ = 0;
    };
} // namespace heron
