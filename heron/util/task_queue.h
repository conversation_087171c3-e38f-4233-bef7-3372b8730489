#pragma once

#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <atomic>
#include <string>

namespace heron
{
    class TaskQueue
    {
    public:
        TaskQueue(std::string name, size_t max_queue_size)
            : name_(std::move(name)), max_queue_size_(max_queue_size), stop_flag_(false), counter_(0) {}

        ~TaskQueue();

        void Start();
        void Stop();

        // Add a task to the queue
        void AddTask(const std::function<void()> &task);
        void ForceAddTask(const std::function<void()> &task);

    private:
        void AddTaskInternal(const std::function<void()> &task, bool force);
        // The worker thread function that executes tasks
        void Worker();

    private:
        std::string name_;
        size_t max_queue_size_; // Maximum allowed size for the queue
        std::queue<std::function<void()>> task_queue_; // The task queue
        std::thread worker_thread_;                    // The worker thread
        std::mutex queue_mutex_;                       // Mutex to protect the queue
        std::condition_variable condition_;            // Condition variable to signal the worker thread
        std::atomic<bool> stop_flag_;                  // Flag to signal the worker thread to stop

        uint64_t counter_ = 0;
    };
} // namespace heron