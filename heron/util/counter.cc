#include <heron/util/counter.h>
#include <heron/util/misc.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

#include <sstream>
#include <iomanip> // Include for manipulators like std::setw, std::setprecision

namespace heron
{
    void CallCounter::Update()
    {
        if (last_tick_ == 0)
        {
            last_tick_ = GetTimeNano();
            return;
        }
        uint64_t curr_tick = GetTimeNano();
        uint64_t new_tick = curr_tick - last_tick_;
        last_tick_ = curr_tick;
        tick_sum_ += new_tick;                /* add new value */
        tick_sum_ -= tick_list_[tick_index_]; /* subtract value falling off */
        tick_list_[tick_index_] = new_tick;   /* save new value so it can be subtracted later */
        if (++tick_index_ == samples_)
            tick_index_ = 0; /* inc buffer index */
        if (PRINT_INTERVAL_SEC <= 0)
            return;
        if ((curr_tick - last_print_timestamp_) / OS_NS_PER_SEC <= PRINT_INTERVAL_SEC)
            return;
        double avg_calls_per_second = (double)OS_NS_PER_SEC * samples_ / tick_sum_;
        last_print_timestamp_ = curr_tick;
        if (abs(avg_calls_per_second - expected_calls_per_second_) < warnning_thresh_)
        {
            HERON_LOG_DEBUG("{} fps:{:.2f}", name_, avg_calls_per_second);
        }
        else
        {
            HERON_LOG_WARN("{} fps error:{:.2f}, expected:{:.2f}", name_, avg_calls_per_second, expected_calls_per_second_);
        }
    }

    void Distribution::Clear()
    {
        samples_ever_since_ = 0;
        samples_since_last_print_ = 0;
        valid_samples_ever_since_ = 0;
        valid_samples_since_last_print_ = 0;
        min_ever_since_ = 1000000000;
        max_ever_since_ = 0;
        min_since_last_print_ = 1000000000;
        max_since_last_print_ = 0;
        last_print_ns_ = 0;

        std::fill(statistics_since_last_print_.begin(), statistics_since_last_print_.end(), 0);
        std::fill(statistics_ever_since_.begin(), statistics_ever_since_.end(), 0);
    }
    Distribution::Distribution(std::string name, int64_t start, int64_t step, uint32_t size, bool enable,
                               uint64_t print_interval_ns)
        : name_(name), start_(start), step_(step), size_(size), enable_(enable),
          print_interval_ns_(print_interval_ns)
    {
        statistics_ever_since_.clear();
        statistics_since_last_print_.clear();
        bars_.clear();

        statistics_ever_since_.resize(size, 0);
        statistics_since_last_print_.resize(size, 0);
        bars_.reserve(size + 1);

        for (uint32_t i = 0; i <= size_; i++)
        {
            bars_.emplace_back(start_ + step_ * i);
        }
    }
    void Distribution::Update(int64_t candidate, bool log_trace)
    {
        if (!enable_)
            return;
        min_since_last_print_ = std::min(candidate, min_since_last_print_);
        max_since_last_print_ = std::max(candidate, max_since_last_print_);
        min_ever_since_ = std::min(candidate, min_ever_since_);
        max_ever_since_ = std::max(candidate, max_ever_since_);
        samples_ever_since_++;
        samples_since_last_print_++;
        if (candidate < start_)
        {
            if (log_trace)
            {
                LOG_TRACE("{} distribution candidate: {} less than {}", name_, candidate, start_);
            }
            else
            {
                LOG_WARN("{} distribution candidate: {} less than {}", name_, candidate, start_);
            }
            return;
        }
        if (candidate > bars_[size_])
        {
            if (log_trace)
            {
                LOG_TRACE("{} distribution candidate: {} larger than {}", name_, candidate, bars_[size_]);
            }
            else
            {
                LOG_WARN("{} distribution candidate: {} larger than {}", name_, candidate, bars_[size_]);
            }
            return;
        }

        uint32_t index = (candidate - start_) / step_;
        if (index == size_) // only when candidate == bars_[size]
            --index;
        statistics_ever_since_[index]++;
        statistics_since_last_print_[index]++;
        valid_samples_ever_since_++;
        valid_samples_since_last_print_++;

        uint64_t now = GetTimeNano();
        if (now - last_print_ns_ > print_interval_ns_)
        {
            Print(now);
        }
        return;
    }
    void Distribution::Print(uint64_t now)
    {
        if (!enable_)
            return;

        std::stringstream ss;
        for (uint32_t i = 0; i < size_; i++)
        {
            ss << statistics_since_last_print_[i];
            if (i != (size_ - 1))
                ss << ",";
        }
        LOG_INFO("[{}] ({},{},{}/{} {:.2f},[{},{},{}]) [{}] in {}sec", name_,
                 min_since_last_print_, max_since_last_print_,
                 valid_samples_since_last_print_, samples_since_last_print_,
                 valid_samples_since_last_print_ / (double)samples_since_last_print_,
                 bars_[0], bars_[size_], step_, ss.str(), print_interval_ns_ / OS_NS_PER_SEC);
        samples_since_last_print_ = 0;
        valid_samples_since_last_print_ = 0;
        std::fill(statistics_since_last_print_.begin(), statistics_since_last_print_.end(), 0);
        min_since_last_print_ = 1000000000;
        max_since_last_print_ = 0;
        last_print_ns_ = now;
    }
    Average::Average(std::string name, bool enable, uint64_t print_interval_ns)
        : name_(name), enable_(enable), print_interval_ns_(print_interval_ns)
    {
    }

    void Average::Update(int64_t candidate)
    {
        if (!enable_)
            return;
        min_since_last_print_ = candidate < min_since_last_print_ ? candidate : min_since_last_print_;
        max_since_last_print_ = candidate > max_since_last_print_ ? candidate : max_since_last_print_;
        min_ever_since_ = candidate < min_ever_since_ ? candidate : min_ever_since_;
        max_ever_since_ = candidate > max_ever_since_ ? candidate : max_ever_since_;

        count_ever_since_++;
        sum_ever_since_ += candidate;
        count_since_last_print_++;
        sum_since_last_print_ += candidate;

        uint64_t now = GetTimeNano();
        if (now - last_print_ns_ > print_interval_ns_)
            Print(now);
        return;
    }

    void Average::Print(uint64_t now)
    {
        double average_since_last_print = sum_since_last_print_ / (double)count_since_last_print_;
        double average_ever_since = sum_ever_since_ / (double)count_ever_since_;
        sum_since_last_print_ = 0;

        HERON_LOG_DEBUG("[{}] {:.3f} {:.3f} ({:.3f},{:.3f}) {} times in {}sec", name_,
                        average_since_last_print / 1000000.0,
                        average_ever_since / 1000000.0,
                        min_since_last_print_ / 1000000.0, max_since_last_print_ / 1000000.0,
                        count_since_last_print_, print_interval_ns_ / OS_NS_PER_SEC);
        count_since_last_print_ = 0;
        min_since_last_print_ = std::numeric_limits<int64_t>::max();
        max_since_last_print_ = std::numeric_limits<int64_t>::min();
        last_print_ns_ = now;
    }
}