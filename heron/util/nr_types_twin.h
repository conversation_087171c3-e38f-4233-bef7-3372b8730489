#pragma once

#include <stdint.h>

namespace heron
{
    struct GUID
    {
        uint64_t high;
        uint64_t low;
    };

    enum Component
    {
        COMPONENT_INVALID = -1,
        COMPONENT_DISPLAY_LEFT = 0,
        COMPONENT_DISPLAY_RIGHT,
        COMPONENT_RGB_CAMERA,
        COMPONENT_GRAYSCALE_CAMERA_LEFT,
        COMPONENT_GRAYSCALE_CAMERA_RIGHT,
        COMPONENT_MAGNETIC,
        COMPONENT_HEAD,
        COMPONENT_IMU,
        COMPONENT_NECK,
        COMPONENT_NUM,
        COMPONENT_DISPLAY_NUM = 2,
    };

    enum Result
    {
        RESULT_SUCCESS = 0,
        RESULT_FAILURE = 1,
        RESULT_INVALID_ARGUMENT = 2,
        RESULT_NOT_ENOUGH_MEMORY = 3,
        RESULT_UNSUPPORTED = 4,
        RESULT_BUSY = 5,
        RESULT_TIMEOUT = 6,
    };
    enum Resolution
    {
        RESOLUTION_UNKNOWN = 0,
        RESOLUTION_1920_1080_60 = 1,
        RESOLUTION_1920_1080_72 = 2,
        RESOLUTION_1920_1080_90 = 3,
        RESOLUTION_1920_1080_120 = 4,
        RESOLUTION_3840_1080_60 = 5,
        RESOLUTION_3840_1080_72 = 6,
        RESOLUTION_3840_1080_90 = 7,
        RESOLUTION_3840_1080_120 = 8,
        RESOLUTION_1920_1200_60 = 21,
        RESOLUTION_1920_1200_72 = 22,
        RESOLUTION_1920_1200_90 = 23,
        RESOLUTION_1920_1200_120 = 24,
        RESOLUTION_3840_1200_60 = 25,
        RESOLUTION_3840_1200_72 = 26,
        RESOLUTION_3840_1200_90 = 27,
        RESOLUTION_3840_1200_120 = 28,
    };
    enum ImageFormat
    {
        IMAGE_FORMAT_UNKNOWN = 0,
        IMAGE_FORMAT_BGR_888_packed = 1,
        IMAGE_FORMAT_RGB_888_packed,
        IMAGE_FORMAT_BGRA_8888_packed,
        IMAGE_FORMAT_RGBA_8888_packed,
        IMAGE_FORMAT_BGR_888_planar,
        IMAGE_FORMAT_RGB_888_planar,
        IMAGE_FORMAT_BGRA_8888_planar,
        IMAGE_FORMAT_RGBA_8888_planar,
        IMAGE_FORMAT_ARGB_8888_planar,
        IMAGE_FORMAT_GRAY_8,
        IMAGE_FORMAT_YUV_420_888,
        IMAGE_FORMAT_YV12,
        IMAGE_FORMAT_NV21,
        IMAGE_FORMAT_NV12,
        IMAGE_FORMAT_BGRA_4444_packed,
    };

    enum TrackingPoseType
    {
        TRACKING_POSE_TYPE_NORMAL = 0,
        TRACKING_POSE_TYPE_LOW_LATENCY = 1,
    };

    enum SceneMode
    {
        SCENE_MODE_SPACE_SCREEN = 0,
        SCENE_MODE_WITH_NEBULA = 1,
    };

    enum DpInputMode
    {
        DP_INPUT_MODE_MONO = 0,
        DP_INPUT_MODE_STEREO = 1,
        DP_INPUT_MODE_COUNT,
    };

    enum SpaceMode
    {
        SPACE_MODE_HOVER = 0,
        SPACE_MODE_FOLLOW = 1,
        SPACE_MODE_THUMBNAIL = 2,
        SPACE_MODE_ULTRA_WIDE = 3,
    };

    enum PerceptionType
    {
        PERCEPTION_TYPE_6DOF = 0,
        PERCEPTION_TYPE_3DOF,
        PERCEPTION_TYPE_0DOF,
        PERCEPTION_TYPE_EIS,
        PERCEPTION_TYPE_OSD_EIS,
        PERCEPTION_TYPE_REMOTE = 100,
    };

    enum OperationType
    {
        OPERATION_INCREASE = 0,
        OPERATION_DECREASE = 1,
    };

    enum StepType
    {
        STEP_TYPE_NORMAL = 0,
        STEP_TYPE_EXTENDED = 1,
    };

    enum ThumbnailPositionType
    {
        THUMBNAIL_POSE_TYPE_LEFT_TOP = 0,
        THUMBNAIL_POSE_TYPE_RIGHT_TOP = 1,
    };

    enum CanvasAreaType
    {
        CANVAS_AREA_UNKNOWN = 0,
        CANVAS_AREA_INSIDE_IMMERSIVE_BOUND = 1,
        CANVAS_AREA_BETWEEN_IMMERSIVE_AND_INTERACTION_BOUND = 2,
        CANVAS_AREA_OUTSIDE_INTERACTION_BOUND = 3,
    };

    struct ResolutionInfo
    {
        int32_t width;
        int32_t height;
        int32_t refresh_rate;
        ResolutionInfo() {};
        ResolutionInfo(Resolution res_ext)
        {
            switch (res_ext)
            {
            case RESOLUTION_1920_1080_60:
                width = 1920;
                height = 1080;
                refresh_rate = 60;
                break;
            case RESOLUTION_1920_1080_72:
                width = 1920;
                height = 1080;
                refresh_rate = 72;
                break;
            case RESOLUTION_1920_1080_90:
                width = 1920;
                height = 1080;
                refresh_rate = 90;
                break;
            case RESOLUTION_1920_1080_120:
                width = 1920;
                height = 1080;
                refresh_rate = 120;
                break;
            case RESOLUTION_3840_1080_60:
                width = 3840;
                height = 1080;
                refresh_rate = 60;
                break;
            case RESOLUTION_3840_1080_72:
                width = 3840;
                height = 1080;
                refresh_rate = 72;
                break;
            case RESOLUTION_3840_1080_90:
                width = 3840;
                height = 1080;
                refresh_rate = 90;
                break;
            case RESOLUTION_3840_1080_120:
                width = 3840;
                height = 1080;
                refresh_rate = 120;
                break;
            case RESOLUTION_1920_1200_60:
                width = 1920;
                height = 1200;
                refresh_rate = 60;
                break;
            case RESOLUTION_1920_1200_72:
                width = 1920;
                height = 1200;
                refresh_rate = 72;
                break;
            case RESOLUTION_1920_1200_90:
                width = 1920;
                height = 1200;
                refresh_rate = 90;
                break;
            case RESOLUTION_1920_1200_120:
                width = 1920;
                height = 1200;
                refresh_rate = 120;
                break;
            case RESOLUTION_3840_1200_60:
                width = 3840;
                height = 1200;
                refresh_rate = 60;
                break;
            case RESOLUTION_3840_1200_72:
                width = 3840;
                height = 1200;
                refresh_rate = 72;
                break;
            case RESOLUTION_3840_1200_90:
                width = 3840;
                height = 1200;
                refresh_rate = 90;
                break;
            case RESOLUTION_3840_1200_120:
                width = 3840;
                height = 1200;
                refresh_rate = 120;
                break;
            default:
                width = 1920;
                height = 1080;
                refresh_rate = 90;
                break;
            };
        }
    };

    enum FramebufferFormat
    {
        FRAMEBUFFER_FORMAT_RGBA_PLANAR = 0,
        FRAMEBUFFER_FORMAT_ARGB_PLANAR = 1,
        FRAMEBUFFER_FORMAT_YUV420_PLANAR = 2,
        FRAMEBUFFER_FORMAT_BGRA_8888 = 3,
        FRAMEBUFFER_FORMAT_BGRA_4444 = 4,
    };

    enum MiscSchedPolicy
    {
        MISC_SCHED_POLICY_NORMAL = 0,
        MISC_SCHED_POLICY_FIFO = 1,
        MISC_SCHED_POLICY_RR = 2,
    };

    struct Rectf
    {
        Rectf() {};
        Rectf(float left, float right, float top, float bottom) : left(left), right(right), top(top), bottom(bottom) {};
        float left, right, top, bottom;

        // 对象与浮点数相乘（成员函数形式）
        Rectf operator*(float scalar) const
        {
            return Rectf(left * scalar, right * scalar, top * scalar, bottom * scalar);
        }
    };

    // 对象间加法（非成员函数）
    inline Rectf operator+(const Rectf &a, const Rectf &b)
    {
        return Rectf(
            a.left + b.left,
            a.right + b.right,
            a.top + b.top,
            a.bottom + b.bottom);
    }

    // 对象间减法（非成员函数）
    inline Rectf operator-(const Rectf &a, const Rectf &b)
    {
        return Rectf(
            a.left - b.left,
            a.right - b.right,
            a.top - b.top,
            a.bottom - b.bottom);
    }

    // 浮点数与对象相乘（非成员函数，复用成员函数实现）
    inline Rectf operator*(float scalar, const Rectf &rect)
    {
        return rect * scalar; // 直接调用已实现的 operator*
    }

    struct Fov4f
    {
        float left_tan;
        float right_tan;
        float top_tan;
        float bottom_tan;
    };

    enum DisplayUsage
    {
        DISPLAY_USAGE_LEFT = 0,
        DISPLAY_USAGE_RIGHT = 1,
        DISPLAY_USAGE_COUNT = 2,
    };

    struct FramebufferQueue
    {
        uint32_t buffer_count;
        void **buffer_queue;
    };

    struct DpFrameData
    {
        uint32_t frame_id;
        uint32_t width;
        uint32_t height;
        FramebufferFormat pixel_format;
        char *data[3];
        char *data_ext[3];
        char *header[3];
        char *header_ext[3];
        uint32_t strides[3];
        uint64_t pts;
        uint64_t ar_frame_handle;
    };
    struct OverlayFrameData
    {
        uint32_t width;           /**< 图像的宽，对应VIDEO_FRAME_INFO_S.stVFrame.u32Width*/
        uint32_t height;          /**< 图像的高，对应VIDEO_FRAME_INFO_S.stVFrame.u32Height*/
        FramebufferFormat format; /**< 图像像素通道排布，对应VIDEO_FRAME_INFO_S.stVFrame.enPixelFormat*/
        const char *data_data;    /**< 图像chn[0]的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64VirAddr[0]的地址*/
        uint32_t data_size;       /**< 图像chn[0]的虚拟地址，对应VIDEO_FRAME_INFO_S.stVFrame.u64VirAddr[0]的地址*/
        uint64_t ar_frame_handle; /**< 构造NROverlayFrameData实例时，需同时构造一个VIDEO_FRAME_INFO_S实例，并保留二者对应关系*/
    };

    struct GdcImageBuffer
    {
        uint32_t is_vb;
        FramebufferFormat pixel_format;
        uint32_t width;
        uint32_t height;
        uint32_t strides[4];
        char *data[4];
        uint32_t data_ext[4];
        char *header[4];
        uint32_t header_ext[4];
    };

    struct GdcMetadata
    {
        const char *warp_data_data;
        uint32_t warp_data_size;
        const char *mesh_data_data;
        uint32_t mesh_data_size;
        const char *weight_data_data;
        uint32_t weight_data_size;
    };

    struct GdcInitConfig
    {
        DisplayUsage display_usage;
        uint8_t start_mode;
        uint32_t lines64_enable;
        uint8_t padding_color[4];
        uint32_t warp_mode;
        uint32_t warp_flush_cnt;
        uint32_t mesh_mode;
        uint32_t mesh_stride;
        uint32_t weight_mode;
        GdcMetadata metadata;
    };

    struct GdcFrameConfig
    {
        DisplayUsage display_usage;
        GdcImageBuffer in_buffer;
        uint8_t frame_start;
        uint32_t warp_mode;
        uint32_t apb_matrix[9];
        GdcMetadata metadata;
        uint8_t padding_color[4];
    };

    struct DisplayConfig
    {
        uint32_t callback_block_cnt;
        uint32_t init_line_cnt;
        uint32_t lines64_enable;
        uint32_t display_mode;
    };

} // namespace heron
