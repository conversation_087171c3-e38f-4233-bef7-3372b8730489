#include <heron/util/lock.h>
#include <heron/util/log.h>

namespace heron
{
    SpinLock::SpinLock() // mac doesn't support spin lock
    {
#ifdef HERON_SYSTEM_MACOS
        pthread_mutex_init(&lock_, NULL);
#else
        pthread_spin_init(&lock_, PTHREAD_PROCESS_PRIVATE);
#endif
    }

    void SpinLock::Lock()
    {
#ifdef HERON_SYSTEM_MACOS
        pthread_mutex_lock(&lock_);
#else
        pthread_spin_lock(&lock_);
#endif
    }
    void SpinLock::Unlock()
    {
#ifdef HERON_SYSTEM_MACOS
        pthread_mutex_unlock(&lock_);
#else
        pthread_spin_unlock(&lock_);
#endif
    }
}