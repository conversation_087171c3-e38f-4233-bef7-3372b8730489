#pragma once
#include <framework/util/log.h>

#include <framework/util/singleton.h>

namespace heron
{

    class Logger : public framework::util::Singleton<Logger>
    {
    public:
        Logger()
        {
            if (debug_log_enable_)
            {
                GetLogger()->set_level(framework::util::log::LogLevel::debug);
                GetLogger()->flush_on(framework::util::log::LogLevel::debug);
            }
            if (log_all_level_)
            {
                GetLogger()->set_level(framework::util::log::LogLevel::trace);
                GetLogger()->flush_on(framework::util::log::LogLevel::trace);
            }
        }
        bool IsLogAllLevel() const { return log_all_level_; }
        bool DebugLogEnable() const { return debug_log_enable_; }
        void SetLogLevel(uint32_t level)
        {
            log_all_level_ = false;
            debug_log_enable_ = false;
            GetLogger()->set_level(framework::util::log::LogLevel::info);
            GetLogger()->flush_on(framework::util::log::LogLevel::info);
            if (level <= 2)
            {
                debug_log_enable_ = true;
                GetLogger()->set_level(framework::util::log::LogLevel::debug);
                GetLogger()->flush_on(framework::util::log::LogLevel::debug);
            }
            if (level <= 1)
            {
                log_all_level_ = true;
                GetLogger()->set_level(framework::util::log::LogLevel::trace);
                GetLogger()->flush_on(framework::util::log::LogLevel::trace);
            }
            log_level_ = level;
        }
        int32_t GetLogLevel() const { return log_level_; }
        framework::util::log::LoggerPtr GetLogger() { return framework::util::log::Logger::defaultLogger(section_); }
        void SetSection(const std::string &section) { section_ = section; }

    private:
        bool debug_log_enable_{true};
        bool log_all_level_{false};
        int32_t log_level_{2};
        std::string section_{"Flinger"};
    };

} // namespace heron
#define HERON_LOG_TRACE(format, ...)                             \
    if (heron::Logger::GetInstance()->IsLogAllLevel())           \
        heron::Logger::GetInstance()->GetLogger()->trace(format, \
                                                         ##__VA_ARGS__);
#define HERON_LOG_DEBUG(format, ...)                             \
    if (heron::Logger::GetInstance()->DebugLogEnable())          \
        heron::Logger::GetInstance()->GetLogger()->debug(format, \
                                                         ##__VA_ARGS__);
#define HERON_LOG_TRACE2(format1, format2, ...)                                          \
    if (heron::Logger::GetInstance()->IsLogAllLevel())                                   \
    {                                                                                    \
        std::string format = format1;                                                    \
        format += format2;                                                               \
        heron::Logger::GetInstance()->GetLogger()->trace(format.c_str(), ##__VA_ARGS__); \
    }

#define HERON_LOG_HEX(format, ...) \
    heron::Logger::GetInstance()->GetLogger()->hex(format, ##__VA_ARGS__);
#define HERON_LOG_INFO(format, ...) \
    heron::Logger::GetInstance()->GetLogger()->info(format, ##__VA_ARGS__);
#define HERON_LOG_WARN(format, ...) \
    heron::Logger::GetInstance()->GetLogger()->warn(format, ##__VA_ARGS__);
#define HERON_LOG_ERROR(format, ...) \
    heron::Logger::GetInstance()->GetLogger()->error(format, ##__VA_ARGS__);
#define HERON_LOG_FATAL(format, ...) \
    heron::Logger::GetInstance()->GetLogger()->fatal(format, ##__VA_ARGS__);
