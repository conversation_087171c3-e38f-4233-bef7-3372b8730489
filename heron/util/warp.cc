#include <heron/util/warp.h>

using namespace heron;

namespace heron
{
    namespace warp
    {
        static bool CheckObject(std::string key_msg, const Vector3f &candidate, const Vector3f &expected, float epsilion)
        {
            for (int i = 0; i < 3; i++)
            {
                if (abs(candidate(i) - expected(i)) > epsilion)
                {
                    HERON_LOG_ERROR("{} check error. candidate[{}]={} is not expected[{}]={}. diff larger than thresh: {}",
                                    key_msg, i, candidate(i), i, expected(i), epsilion);
                    return false;
                }
            }
            return true;
        }
        static bool CheckObject(std::string key_msg, const Quatf &candidate, const Quatf &expected, float epsilion)
        {
            if (abs(candidate.x() - expected.x()) > epsilion)
            {
                HERON_LOG_ERROR("{} check error. candidate.x={} is not expected.x={}. diff larger than thresh: {}",
                                key_msg, candidate.x(), expected.x(), epsilion);
                return false;
            }
            if (abs(candidate.y() - expected.y()) > epsilion)
            {
                HERON_LOG_ERROR("{} check error. candidate.y={} is not expected.y={}. diff larger than thresh: {}",
                                key_msg, candidate.y(), expected.y(), epsilion);
                return false;
            }
            if (abs(candidate.z() - expected.z()) > epsilion)
            {
                HERON_LOG_ERROR("{} check error. candidate.z={} is not expected.z={}. diff larger than thresh: {}",
                                key_msg, candidate.z(), expected.z(), epsilion);
                return false;
            }
            if (abs(candidate.w() - expected.w()) > epsilion)
            {
                HERON_LOG_ERROR("{} check error. candidate.w={} is not expected.w={}. diff larger than thresh: {}",
                                key_msg, candidate.w(), expected.w(), epsilion);
                return false;
            }
            return true;
        }

        bool CheckObject(std::string key_msg, const Mat3f &candidate, const Mat3f &expected, float epsilion)
        {
            for (int i = 0; i < 3; i++)
            {
                for (int j = 0; j < 3; j++)
                {
                    if (abs(candidate(i, j) - expected(i, j)) > epsilion)
                    {
                        HERON_LOG_ERROR("{} check error. candidate({}, {})={} is not expected({}, {})={}. diff larger than thresh: {}",
                                        key_msg, i, j, candidate(i, j), i, j, expected(i, j), epsilion);
                        return false;
                    }
                }
            }
            HERON_LOG_INFO("{} PASS! thresh: {}", key_msg, epsilion);
            return true;
        }

        bool CheckObject(std::string key_msg, const Transform &candidate, const Transform &expected, float epsilion)
        {
            if (!CheckObject(key_msg + "[position]", candidate.position, expected.position, epsilion))
                return false;
            if (!CheckObject(key_msg + "[rotation]", candidate.rotation, expected.rotation, epsilion))
                return false;
            HERON_LOG_INFO("{} PASS! thresh: {}", key_msg, epsilion);
            return true;
        }

        // Constants
        static const double _EPS = std::numeric_limits<double>::epsilon() * 4.0;

        // Axis sequences for Euler angles
        static std::array<int, 4> _NEXT_AXIS = {1, 2, 0, 1};

        // Map axes strings to/from tuples of inner axis, parity, repetition, frame
        static std::map<std::string, std::tuple<int, int, int, int>> _AXES2TUPLE = {
            {"sxyz", {0, 0, 0, 0}}, {"sxyx", {0, 0, 1, 0}}, {"sxzy", {0, 1, 0, 0}}, {"sxzx", {0, 1, 1, 0}}, {"syzx", {1, 0, 0, 0}}, {"syzy", {1, 0, 1, 0}}, {"syxz", {1, 1, 0, 0}}, {"syxy", {1, 1, 1, 0}}, {"szxy", {2, 0, 0, 0}}, {"szxz", {2, 0, 1, 0}}, {"szyx", {2, 1, 0, 0}}, {"szyz", {2, 1, 1, 0}}, {"rzyx", {0, 0, 0, 1}}, {"rxyx", {0, 0, 1, 1}}, {"ryzx", {0, 1, 0, 1}}, {"rxzx", {0, 1, 1, 1}}, {"rxzy", {1, 0, 0, 1}}, {"ryzy", {1, 0, 1, 1}}, {"rzxy", {1, 1, 0, 1}}, {"ryxy", {1, 1, 1, 1}}, {"ryxz", {2, 0, 0, 1}}, {"rzxz", {2, 0, 1, 1}}, {"rxyz", {2, 1, 0, 1}}, {"rzyz", {2, 1, 1, 1}}};

        // Reverse map to get axes from tuple
        static std::map<std::tuple<int, int, int, int>, std::string> _TUPLE2AXES;
        static void initialize_tuple2axes()
        {
            for (const auto &pair : _AXES2TUPLE)
            {
                _TUPLE2AXES[pair.second] = pair.first;
            }
        }

        std::tuple<double, double, double> euler_from_matrix(const Eigen::Matrix3d &matrix, const std::string &axes)
        {
            // Extract axis sequence
            int firstaxis, parity, repetition, frame;
            try
            {
                std::tie(firstaxis, parity, repetition, frame) = _AXES2TUPLE.at(axes);
            }
            catch (const std::out_of_range &)
            {
                HERON_LOG_ERROR("{} Invalid axis sequence", __FUNCTION__);
                return std::make_tuple(0.0, 0.0, 0.0);
            }

            int i = firstaxis;
            int j = _NEXT_AXIS[i + parity];
            int k = _NEXT_AXIS[i - parity + 1];

            Eigen::Matrix3d M = matrix;
            double ax, ay, az;

            if (repetition)
            {
                double sy = std::sqrt(M(i, j) * M(i, j) + M(i, k) * M(i, k));
                if (sy > _EPS)
                {
                    ax = std::atan2(M(i, j), M(i, k));
                    ay = std::atan2(sy, M(i, i));
                    az = std::atan2(M(j, i), -M(k, i));
                }
                else
                {
                    ax = std::atan2(-M(j, k), M(j, j));
                    ay = std::atan2(sy, M(i, i));
                    az = 0.0;
                }
            }
            else
            {
                double cy = std::sqrt(M(i, i) * M(i, i) + M(j, i) * M(j, i));
                if (cy > _EPS)
                {
                    ax = std::atan2(M(k, j), M(k, k));
                    ay = std::atan2(-M(k, i), cy);
                    az = std::atan2(M(j, i), M(i, i));
                }
                else
                {
                    ax = std::atan2(-M(j, k), M(j, j));
                    ay = std::atan2(-M(k, i), cy);
                    az = 0.0;
                }
            }

            // Adjust signs based on parity and frame
            if (parity)
            {
                ax = -ax;
                ay = -ay;
                az = -az;
            }
            if (frame)
            {
                std::swap(ax, az);
            }

            return std::make_tuple(ax, ay, az);
        }

        Eigen::Quaterniond quaternion_from_euler(double ai, double aj, double ak, const std::string &axes)
        {
            // Extract axis sequence
            int firstaxis, parity, repetition, frame;
            try
            {
                std::tie(firstaxis, parity, repetition, frame) = _AXES2TUPLE.at(axes);
            }
            catch (const std::out_of_range &)
            {
                HERON_LOG_ERROR("{} Invalid axis sequence", __FUNCTION__);
                return Eigen::Quaterniond::Identity();
            }

            int i = firstaxis;
            int j = _NEXT_AXIS[i + parity];
            int k = _NEXT_AXIS[i - parity + 1];

            if (frame)
            {
                std::swap(ai, ak);
            }
            if (parity)
            {
                aj = -aj;
            }

            ai /= 2.0;
            aj /= 2.0;
            ak /= 2.0;

            double ci = std::cos(ai);
            double si = std::sin(ai);
            double cj = std::cos(aj);
            double sj = std::sin(aj);
            double ck = std::cos(ak);
            double sk = std::sin(ak);
            double cc = ci * ck;
            double cs = ci * sk;
            double sc = si * ck;
            double ss = si * sk;

            Eigen::Quaterniond quaternion;
            if (repetition)
            {
                quaternion.coeffs()(i) = cj * (cs + sc);
                quaternion.coeffs()(j) = sj * (cc + ss);
                quaternion.coeffs()(k) = sj * (cs - sc);
                quaternion.w() = cj * (cc - ss);
            }
            else
            {
                quaternion.coeffs()(i) = cj * sc - sj * cs;
                quaternion.coeffs()(j) = cj * ss + sj * cc;
                quaternion.coeffs()(k) = cj * cs - sj * sc;
                quaternion.w() = cj * cc + sj * ss;
            }

            if (parity)
            {
                quaternion.coeffs()(j) *= -1;
            }

            return quaternion;
        }

        void CalcRecenterTransform(const Transform &head_transform, Transform &recenter_transform)
        {
            Mat3f head_rotation = head_transform.rotation.toRotationMatrix();
            std::tuple<double, double, double> euler_ryxz = euler_from_matrix(head_rotation.cast<double>(), "ryxz");
            HERON_LOG_DEBUG("recenter ryxz_euler: {},{},{}", std::get<0>(euler_ryxz), std::get<1>(euler_ryxz), std::get<2>(euler_ryxz));
            float pitch = std::get<1>(euler_ryxz) * (180.0f / M_PI);
            float roll = std::get<2>(euler_ryxz) * (180.0f / M_PI);
            if (abs(pitch) < 20 && abs(roll) < 30)
                roll = 0;
            recenter_transform.rotation = quaternion_from_euler(std::get<0>(euler_ryxz), pitch * M_PI / 180.0f, roll * M_PI / 180.0f, "ryxz").cast<float>();
            recenter_transform.position = head_transform.position;
        }

    } // namespace warp
} // namespace heron