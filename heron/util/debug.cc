#include <heron/util/debug.h>
#include <heron/util/config.h>
#include <heron/util/log.h>
#include <heron/model/model_manager.h>

#include <framework/util/json.h>

#include <thread>
#include <mutex>
#include <condition_variable>
#include <vector>
#include <queue>
#include <fstream>
#include <fcntl.h>    // For O_SYNC, O_RDWR, and other file control options
#include <sys/mman.h> // For mmap and related memory management functions

namespace heron
{
    static bool ParseDebugLog(const Json::Value &root, DebugManager::DebugLog &config)
    {
        if (root.isMember("suitable_src_frame_size"))
            config.suitable_src_frame_size = root["suitable_src_frame_size"].asBool();
        if (root.isMember("focus_judger"))
            config.focus_judger = root["focus_judger"].asBool();
        if (root.isMember("warp_job_block_id"))
            config.warp_job_block_id = root["warp_job_block_id"].asBool();
        if (root.isMember("embedded_metadata"))
            config.embedded_metadata = root["embedded_metadata"].asBool();
        if (root.isMember("populated_metadata"))
            config.populated_metadata = root["populated_metadata"].asBool();
        if (root.isMember("msg_metadata"))
            config.msg_metadata = root["msg_metadata"].asBool();
        if (root.isMember("timing_detail"))
            config.timing_detail = root["timing_detail"].asBool();
        if (root.isMember("timing_verbose"))
            config.timing_verbose = root["timing_verbose"].asBool();
        if (root.isMember("frame_data_ext"))
            config.frame_data_ext = root["frame_data_ext"].asBool();
        if (root.isMember("quad_status_interpolation"))
            config.quad_status_interpolation = root["quad_status_interpolation"].asBool();
        if (root.isMember("target_block_id"))
            config.target_block_id = root["target_block_id"].asBool();
        if (root.isMember("head_pose"))
            config.head_pose = root["head_pose"].asBool();
        if (root.isMember("osd_submit"))
            config.osd_submit = root["osd_submit"].asBool();
        return true;
    }

    static bool ParseGDCDebugConfig(const Json::Value &root, DebugManager::GDCDebugConfig &config)
    {
        if (root.isMember("use_sram"))
        {
            config.use_sram = root["use_sram"].asBool();
            HERON_LOG_DEBUG("gdc use_sram parsed: {}", config.use_sram);
        }
        if (root.isMember("ext_padding"))
        {
            const Json::Value &padding = root["ext_padding"];
            config.padding_value_c0 = padding["c0"].asInt();
            config.padding_value_c1 = padding["c1"].asInt();
            config.padding_value_c2 = padding["c2"].asInt();
        }
        if (root.isMember("start_mode"))
        {
            config.start_mode = root["start_mode"].asInt();
            HERON_LOG_DEBUG("gdc start_mode parsed: {}", config.start_mode);
        }
        if (root.isMember("warp_mode"))
        {
            config.warp_mode = root["warp_mode"].asInt();
            HERON_LOG_DEBUG("gdc warp_mode parsed: {}", config.warp_mode);
        }
        if (root.isMember("warp_flush_cnt"))
        {
            config.warp_flush_cnt = root["warp_flush_cnt"].asInt();
            HERON_LOG_DEBUG("gdc warp_flush_cnt parsed: {}", config.warp_flush_cnt);
        }
        if (root.isMember("mesh_mode"))
        {
            config.mesh_mode = root["mesh_mode"].asInt();
            HERON_LOG_DEBUG("gdc mesh_mode parsed: {}", config.mesh_mode);
        }
        if (root.isMember("use_identity_mesh"))
        {
            config.use_identity_mesh = root["use_identity_mesh"].asBool();
            HERON_LOG_DEBUG("gdc use_identity_mesh parsed: {}", config.use_identity_mesh);
        }
        if (root.isMember("weight_mode"))
        {
            config.weight_mode = root["weight_mode"].asInt();
            HERON_LOG_DEBUG("gdc weight_mode parsed: {}", config.weight_mode);
        }
        return true;
    }

    static bool ParseTimingConfig(const Json::Value &root, WarpTiming &timing)
    {
        if (root.isMember("warp_delay_block_cnt"))
        {
            timing.warp_delay_block_cnt = root["warp_delay_block_cnt"].asUInt();
        }
        if (root.isMember("overwrite_block_cnt"))
        {
            timing.overwrite_block_cnt = root["overwrite_block_cnt"].asUInt() > 4 ? 4 : root["overwrite_block_cnt"].asUInt();
        }
        if (root.isMember("warp_block_cnt_at_each_callback"))
        {
            timing.warp_block_cnt_at_each_callback = root["warp_block_cnt_at_each_callback"].asUInt();
            if (timing.warp_block_cnt_at_each_callback - timing.overwrite_block_cnt < 4)
                timing.warp_block_cnt_at_each_callback = timing.overwrite_block_cnt + 4;
            if (timing.warp_block_cnt_at_each_callback > 12)
                timing.warp_block_cnt_at_each_callback = 12;
        }
        if (root.isMember("get_frame_before_special"))
        {
            timing.get_frame_before_special = root["get_frame_before_special"].asUInt();
        }
        return true;
    }
    static bool ParseDumpDpInputConfig(const Json::Value &root, DebugManager::DumpDpInputConfig &dump_config)
    {
        if (root.isMember("dump_dp_src_frame"))
        {
            dump_config.dump_dp_src_frame = root["dump_dp_src_frame"].asBool();
        }
        if (root.isMember("dump_whole_frame_interval"))
        {
            dump_config.dump_whole_frame_interval = root["dump_whole_frame_interval"].asUInt();
        }
        if (root.isMember("dump_first_lines"))
        {
            dump_config.dump_first_lines = root["dump_first_lines"].asBool();
        }
        if (root.isMember("dump_first_lines_frame_interval"))
        {
            dump_config.dump_first_lines_frame_interval = root["dump_first_lines_frame_interval"].asUInt();
        }
        if (root.isMember("line_count"))
        {
            dump_config.line_count = root["line_count"].asUInt();
        }
        if (root.isMember("width"))
        {
            dump_config.width = root["width"].asUInt();
        }
        return true;
    }
    static bool ParseDpVideoPipelineParam(const Json::Value &root, DpVideoPipelineParam &param)
    {
        if (root.isMember("screen_width"))
            param.SCREEN_WIDTH = root["screen_width"].asUInt();
        if (root.isMember("screen_height"))
            param.SCREEN_HEIGHT = root["screen_height"].asUInt();
        if (root.isMember("src_width"))
            param.SRC_WIDTH = root["src_width"].asUInt();
        if (root.isMember("src_height"))
            param.SRC_HEIGHT = root["src_height"].asUInt();
        HERON_LOG_DEBUG("DpVideoPipelineParam parsed. SCREEN:{}x{} SRC:{}x{}",
                        param.SCREEN_WIDTH, param.SCREEN_HEIGHT, param.SRC_WIDTH, param.SRC_HEIGHT);
        return true;
    }

    static void ParseATWProfiler(const Json::Value &root)
    {
        int64_t line_start = 0;
        int64_t line_step = 0;
        uint32_t bar_count = 0;
        uint64_t duration_ns = 0;
        DebugManager *p_dm = DebugManager::GetInstance();
        do
        {
            if (root.isMember("line_start"))
                line_start = root["line_start"].asInt64();
            else
                break;
            if (root.isMember("line_step"))
                line_step = root["line_step"].asInt64();
            else
                break;
            if (root.isMember("bar_count"))
                bar_count = root["bar_count"].asUInt();
            else
                break;
            if (root.isMember("duration_seconds"))
                duration_ns = root["duration_seconds"].asUInt64() * OS_NS_PER_SEC;
            else
                break;
            p_dm->profile_atw = true;
        } while (false);
        if (!p_dm->profile_atw)
        {
            HERON_LOG_ERROR("ATWProfiler config error: {} {} {} {}",
                            line_start, line_step, bar_count, duration_ns);
        }
        else
        {
            p_dm->atw_line_diff =
                Distribution("atw_line_diff", line_start, line_step, bar_count, true, duration_ns);
        }
    }
    bool ParseGlobalConfig(const char *config, uint32_t size)
    {
        if (size == 0)
            return true;
        Json::Value root;
        Json::CharReaderBuilder json_builder;
        json_builder["collectComments"] = false;
        JSONCPP_STRING json_errs;
        std::istringstream json_stream(std::string(config, size));
        if (!parseFromStream(json_builder, json_stream, &root, &json_errs))
        {
            HERON_LOG_ERROR("Parse global config sdk_global error, json_errs = {}", json_errs.c_str());
            return false;
        }
        if (!root.isMember("flinger"))
            return true;

        const Json::Value &flinger = root["flinger"];
        if (flinger.isMember("log_level"))
        {
            uint32_t log_level = atoi(flinger["log_level"].asString().c_str());
            heron::Logger::GetInstance()->SetLogLevel(log_level);
            DebugManager::GetInstance()->log_all_level = log_level == 1;
        }
        if (flinger.isMember("debug_log"))
        {
            const Json::Value &debug_log = flinger["debug_log"];
            ParseDebugLog(debug_log, DebugManager::GetInstance()->debug_log);
        }

        if (flinger.isMember("no_sys_log_when_sending_tcp_log"))
            DebugManager::GetInstance()->no_sys_log_when_sending_tcp_log = flinger["no_sys_log_when_sending_tcp_log"].asBool();

        if (flinger.isMember("dp_video_pipeline_param"))
        {
            DebugManager::GetInstance()->use_arbitrary_dp_video_pipeline_param = true;
            const Json::Value &params = flinger["dp_video_pipeline_param"];
            ParseDpVideoPipelineParam(params, DebugManager::GetInstance()->dp_video_pipeline_param);
        }

        if (flinger.isMember("hide_metadata_lines"))
            DebugManager::GetInstance()->hide_metadata_lines = flinger["hide_metadata_lines"].asBool();

        if (flinger.isMember("arbitrary_vo_fps"))
            DebugManager::GetInstance()->arbitrary_vo_fps = flinger["arbitrary_vo_fps"].asBool();

        if (flinger.isMember("default_vo_fps"))
            DebugManager::GetInstance()->default_vo_fps = flinger["arbitrary_vo_fps"].asFloat();

        if (flinger.isMember("lines_64_enable"))
            DebugManager::GetInstance()->lines_64_enable = flinger["lines_64_enable"].asBool();

        if (flinger.isMember("init_line_cnt"))
            DebugManager::GetInstance()->init_line_cnt = flinger["init_line_cnt"].asUInt();

        if (flinger.isMember("sleep_us_after_dp_rx_done"))
            DebugManager::GetInstance()->sleep_us_after_dp_rx_done = flinger["sleep_us_after_dp_rx_done"].asUInt();

        if (flinger.isMember("no_update_matrix_buffer")) // avoid calling DebugManager::GetInstance() in PrepareWarpMeshPoint()
            DebugManager::GetInstance()->no_update_matrix_buffer_ = flinger["no_update_matrix_buffer"].asBool();

        if (flinger.isMember("use_mmz_alloc_cached"))
            DebugManager::GetInstance()->use_mmz_alloc_cached_ = flinger["use_mmz_alloc_cached"].asBool();

        if (flinger.isMember("use_async_warp"))
            DebugManager::GetInstance()->use_async_warp = flinger["use_async_warp"].asBool();

        if (flinger.isMember("use_host_ptw_warp"))
            DebugManager::GetInstance()->use_host_ptw_warp = flinger["use_host_ptw_warp"].asBool();

        if (flinger.isMember("only_use_left_vo_callback"))
            DebugManager::GetInstance()->only_use_left_vo_callback = flinger["only_use_left_vo_callback"].asBool();

        if (flinger.isMember("target_src_size_pixel"))
        {
            DebugManager::GetInstance()->arbitrary_src_size_pixel = true;
            const Json::Value &target_src_size_pixel = flinger["target_src_size_pixel"];
            DebugManager::GetInstance()->target_src_size_pixel.x() = target_src_size_pixel[0].asInt();
            DebugManager::GetInstance()->target_src_size_pixel.y() = target_src_size_pixel[1].asInt();
        }

        if (flinger.isMember("disable_warp_at_0DOF"))
            DebugManager::GetInstance()->disable_warp_at_0DOF = flinger["disable_warp_at_0DOF"].asBool();

        if (flinger.isMember("check_underflow"))
            DebugManager::GetInstance()->check_underflow = flinger["check_underflow"].asBool();

        if (flinger.isMember("ignore_linebuffer_reset"))
            DebugManager::GetInstance()->ignore_linebuffer_reset = flinger["ignore_linebuffer_reset"].asBool();

        if (flinger.isMember("debug_warp_matrix_file_path"))
        {
            DebugManager::GetInstance()->debug_warp_matrix_file_path = flinger["debug_warp_matrix_file_path"].asString();
            // Open the file in binary mode
            std::ifstream file(DebugManager::GetInstance()->debug_warp_matrix_file_path.c_str(), std::ios::binary);
            if (!file)
            {
                HERON_LOG_WARN("Could not open the file: {}", DebugManager::GetInstance()->debug_warp_matrix_file_path);
            }
            else
            {
                // Determine the size of the file
                file.seekg(0, std::ios::end);
                std::streamsize size = file.tellg();
                file.seekg(0, std::ios::beg);
                // Read the file content into the buffer
                if (size != 35 * 9 * sizeof(float))
                {
                    HERON_LOG_WARN("debugwarp_matrices data size error, get {}", size);
                }
                else
                {
                    if (!file.read(DebugManager::GetInstance()->debug_warp_matrices.data(), size))
                    {
                        HERON_LOG_WARN("Error reading the file! {}", DebugManager::GetInstance()->debug_warp_matrix_file_path);
                    }
                    else
                    {
                        HERON_LOG_INFO("load warp_matrices data success of size: {}", size);
                    }
                }
                file.close();
            }
        }

        if (flinger.isMember("pose_timestamp_offset_ns"))
            DebugManager::GetInstance()->pose_timestamp_offset_ns = flinger["pose_timestamp_offset_ns"].asInt64();

        if (flinger.isMember("warp_timing"))
        {
            const Json::Value &timing = flinger["warp_timing"];
            ParseTimingConfig(timing, DebugManager::GetInstance()->normal_timing);
        }
        if (flinger.isMember("ultra_wide_timing"))
        {
            const Json::Value &timing = flinger["ultra_wide_timing"];
            ParseTimingConfig(timing, DebugManager::GetInstance()->ultra_wide_timing);
        }
        if (flinger.isMember("with_nebula_timing"))
        {
            const Json::Value &timing = flinger["with_nebula_timing"];
            ParseTimingConfig(timing, DebugManager::GetInstance()->with_nebula_timing);
        }
        if (flinger.isMember("print_frame_interval"))
            DebugManager::GetInstance()->print_frame_interval = flinger["print_frame_interval"].asUInt();

        if (flinger.isMember("print_interrupt_interval"))
            DebugManager::GetInstance()->print_interrupt_interval = flinger["print_interrupt_interval"].asUInt();

        if (flinger.isMember("profile_callback_interval"))
            DebugManager::GetInstance()->profile_callback_interval = flinger["profile_callback_interval"].asUInt();

        if (flinger.isMember("profile_atw"))
        {
            const Json::Value &atw_profiler_conf = flinger["profile_atw"];
            ParseATWProfiler(atw_profiler_conf);
        }

        if (flinger.isMember("immediate_return_on_vo_callback"))
            DebugManager::GetInstance()->immediate_return_on_vo_callback = flinger["immediate_return_on_vo_callback"].asBool();

        if (flinger.isMember("dp_rx_single_buffer"))
            DebugManager::GetInstance()->dp_rx_single_buffer = flinger["dp_rx_single_buffer"].asBool();

        if (flinger.isMember("gdc_left"))
        {
            const Json::Value &gdc_left = flinger["gdc_left"];
            if (!ParseGDCDebugConfig(gdc_left, DebugManager::GetInstance()->gdc_configs[DISPLAY_USAGE_LEFT]))
                return false;
        }
        if (flinger.isMember("gdc_right"))
        {
            const Json::Value &gdc_right = flinger["gdc_right"];
            if (!ParseGDCDebugConfig(gdc_right, DebugManager::GetInstance()->gdc_configs[DISPLAY_USAGE_RIGHT]))
                return false;
        }

        if (flinger.isMember("dump_dp_input_config"))
        {
            const Json::Value &dump_config = flinger["dump_dp_input_config"];
            if (!ParseDumpDpInputConfig(dump_config, DebugManager::GetInstance()->dump_config))
                return false;
        }

        if (flinger.isMember("control_device_thread"))
        {
            DebugManager::GetInstance()->control_device_thread = flinger["control_device_thread"].asBool();
            HERON_LOG_DEBUG("control_device_thread: {}", DebugManager::GetInstance()->control_device_thread);
        }

        if (flinger.isMember("use_perception_recenter"))
            DebugManager::GetInstance()->use_perception_recenter = flinger["use_perception_recenter"].asBool();

        if (flinger.isMember("gen_fake_vsync"))
            DebugManager::GetInstance()->gen_fake_vsync = flinger["gen_fake_vsync"].asBool();

        // Parse ATW detail collection configuration
        if (flinger.isMember("enable_atw_detail_collection"))
            DebugManager::GetInstance()->enable_atw_detail_collection = flinger["enable_atw_detail_collection"].asBool();

        if (flinger.isMember("max_atw_details_size"))
            DebugManager::GetInstance()->max_atw_details_size = flinger["max_atw_details_size"].asUInt();

        if (flinger.isMember("atw_details_dump_dir"))
            DebugManager::GetInstance()->atw_details_dump_dir = flinger["atw_details_dump_dir"].asString();

        return true;
    }

    DebugManager::DebugManager()
        : dump_in_progress_(false)
    {
        debug_warp_matrices.resize(39 * 9 * sizeof(float));
        std::vector<float> identity_mat3f = {1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0};
        for (uint32_t i = 0; i < 39; i++)
        {
            std::memcpy(debug_warp_matrices.data() + i * 9 * sizeof(float), identity_mat3f.data(), 9 * sizeof(float));
        }
    }

    DebugManager::~DebugManager()
    {
        // Make sure any ongoing dump is completed before destruction
        std::lock_guard<std::mutex> lock(dump_mutex_);
        if (dump_thread_.joinable())
        {
            dump_thread_.join();
        }
    }

    static int s_fd_dptest_reg_base;
    static unsigned long s_va_base_reg_base;
    // map 0x08800000-08900000  len:0x100000
    void DebugManager::ar_dptest_system_register_map()
    {
#ifdef HERON_SYSTEM_XRLINUX
        s_fd_dptest_reg_base = open("/dev/mem", O_RDWR | O_SYNC);
        s_va_base_reg_base = (unsigned long)mmap(NULL, 0x100000, PROT_READ | PROT_WRITE, MAP_SHARED, s_fd_dptest_reg_base, 0x08800000);

        va_de_reg_base[0] = s_va_base_reg_base + 0x20000;
        va_de_reg_base[1] = s_va_base_reg_base + 0x40000;
        va_gdc_reg_base[0] = s_va_base_reg_base + 0x30000;
        va_gdc_reg_base[1] = s_va_base_reg_base + 0x50000;
#endif
    }

    void DebugManager::ar_dptest_system_register_unmap()
    {
#ifdef HERON_SYSTEM_XRLINUX
        munmap((unsigned char *)s_va_base_reg_base, 0x100000);
        close(s_fd_dptest_reg_base);
#endif
    }

    void DebugManager::CheckUnderflow(DisplayUsage display_usage)
    {
        if (!check_underflow)
            return;
        if (AR_DPTEST_GET_REG_BITS(DebugManager::GetInstance()->va_de_reg_base[display_usage] + 0x1518) & 0x20)
        {
            HERON_LOG_WARN("GDC{} underflow detected on vsync callback", display_usage);
        }
    }

    void DebugManager::DumpATWDetails()
    {
        if (atw_details.empty())
        {
            HERON_LOG_WARN("No ATW details to dump");
            return;
        }

        // Check if a dump is already in progress
        {
            std::lock_guard<std::mutex> lock(dump_mutex_);
            if (dump_in_progress_)
            {
                HERON_LOG_WARN("ATW details dump already in progress, skipping");
                return;
            }

            // Mark as in progress
            dump_in_progress_ = true;
        }

        // Generate a filename based on the current time
        char timestamp[64];
        time_t now = time(nullptr);
        struct tm *tm_info = localtime(&now);
        strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", tm_info);

        std::string filename = std::string("atw_details_") + timestamp + ".bin";
        std::string full_path = atw_details_dump_dir + "/" + filename;

        HERON_LOG_DEBUG("Starting async dump of {} ATW details to {}", atw_details.size(), full_path);

        // Make a copy of the data to be dumped
        std::vector<ATWFrameDetail> details_to_dump = atw_details;
        std::string dump_path = full_path;

        // Clear the vector immediately so we can continue collecting data
        atw_details.clear();

        // If there's an existing thread, wait for it to complete
        if (dump_thread_.joinable())
            dump_thread_.join();

        // Start a new thread to write the data
        dump_thread_ = std::thread(&DebugManager::DumpATWDetailsAsync, this, std::move(details_to_dump), dump_path);
    }

    void DebugManager::DumpATWDetailsAsync(std::vector<ATWFrameDetail> details_to_dump, std::string dump_path)
    {
        // This method runs in a separate thread

        // Ensure the directory exists
        size_t last_slash = dump_path.find_last_of('/');
        if (last_slash != std::string::npos)
        {
            std::string dir_path = dump_path.substr(0, last_slash);
            // Create directory if it doesn't exist (mkdir -p equivalent)
            std::string mkdir_cmd = "mkdir -p " + dir_path;
            int result = system(mkdir_cmd.c_str());
            if (result != 0)
            {
                HERON_LOG_WARN("Failed to create directory: {}, error code: {}", dir_path, result);
            }
        }

        std::ofstream file(dump_path, std::ios::binary);
        if (!file)
        {
            HERON_LOG_ERROR("Failed to open file for ATW details dump: {}", dump_path);

            // Mark as no longer in progress
            std::lock_guard<std::mutex> lock(dump_mutex_);
            dump_in_progress_ = false;
            return;
        }

        // Write the number of details
        uint32_t num_details = static_cast<uint32_t>(details_to_dump.size());
        HERON_LOG_DEBUG("first detail frame_num:{} start_render_ns:{} old_pose_ns:{} ", details_to_dump[0].frame_num, details_to_dump[0].start_render_ns, details_to_dump[0].old_pose_ns);
        PrintObject("first detail old_transform:", details_to_dump[0].old_transform);
        stringstream ss;
        for (int i = 0; i < 35; i++)
        {
            ss << details_to_dump[0].line_diff[i];
            if (i < 34)
                ss << ",";
        }
        HERON_LOG_DEBUG("first detail line_diff: {}", ss.str());
        file.write(reinterpret_cast<const char *>(&num_details), sizeof(num_details));

        // Write all details
        file.write(reinterpret_cast<const char *>(details_to_dump.data()), details_to_dump.size() * sizeof(ATWFrameDetail));

        file.close();

        HERON_LOG_DEBUG("ATW details dump completed, {} bytes written", details_to_dump.size() * sizeof(ATWFrameDetail));

        // Mark as no longer in progress
        std::lock_guard<std::mutex> lock(dump_mutex_);
        dump_in_progress_ = false;
    }
} // namespace heron
