#include <heron/util/types.h>
#include <heron/interface/include/public/nr_plugin_types.h>

namespace heron
{
    inline void ConvertToFov4f(Fov4f &out, const NRFov4f &in)
    {
        out.left_tan = in.left_tan;
        out.right_tan = in.right_tan;
        out.top_tan = in.top_tan;
        out.bottom_tan = in.bottom_tan;
    }

    inline void ConvertToVector3f(Vector3f &out, const NRVector3f &in)
    {
        out.x() = in.x;
        out.y() = in.y;
        out.z() = in.z;
    }

    inline void ConvertToNRVector3f(NRVector3f &out, const Vector3f &in)
    {
        out.x = in.x();
        out.y = in.y();
        out.z = in.z();
    }
    inline void ConvertToNRSize2i(NRSize2i &out, const Vector2i &in)
    {
        out.width = in.x();
        out.height = in.y();
    }

    inline void ConvertToVector2i(Vector2i &out, const NRSize2i &in)
    {
        out.x() = in.width;
        out.y() = in.height;
    }

    inline void ConvertToQuatf(Quatf &out, const NRQuatf &in)
    {
        out.x() = in.qx;
        out.y() = in.qy;
        out.z() = in.qz;
        out.w() = in.qw;
    }

    inline void ConvertToNRQuatf(NRQuatf &out, const Quatf &in)
    {
        out.qx = in.x();
        out.qy = in.y();
        out.qz = in.z();
        out.qw = in.w();
    }

    inline void ConvertToNRRectf(NRRectf &out, const Rectf &in)
    {
        out.bottom = in.bottom;
        out.top = in.top;
        out.left = in.left;
        out.right = in.right;
    }
    inline void ConvertToRectf(Rectf &out, const NRRectf &in)
    {
        out.bottom = in.bottom;
        out.top = in.top;
        out.left = in.left;
        out.right = in.right;
    }

    inline void NRTransformToTransform(Transform &out, const NRTransform &in)
    {
        ConvertToVector3f(out.position, in.position);
        ConvertToQuatf(out.rotation, in.rotation);
    }

    inline void TransformToNRTransform(NRTransform &out, const Transform &in)
    {
        ConvertToNRVector3f(out.position, in.position);
        ConvertToNRQuatf(out.rotation, in.rotation);
    }

    inline void ConvertToNRFrameBufferQueue(NRFrameBufferQueue &out, FramebufferQueue &in)
    {
        out.buffer_count = in.buffer_count;
        out.buffer_queue = in.buffer_queue;
    }

    inline void ConvertToResolutionInfo(ResolutionInfo &out, const NRResolutionInfo &in)
    {
        out.width = in.width;
        out.height = in.height;
        out.refresh_rate = in.refresh_rate;
    }
}