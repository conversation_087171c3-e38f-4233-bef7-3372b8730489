#pragma once
#include <heron/util/types.h>

#include <framework/util/os_time.h>

#define UTIL_COPY(__DST__, __SRC__, __NAME__) \
    (__DST__)->__NAME__ = (__SRC__)->__NAME__;

#define UTIL_COPY_EX(__DST__, __SRC__, __NAME__) \
    (__DST__).__NAME__ = (__SRC__).__NAME__;

namespace heron
{
    const float EPS = 1e-4;

    inline uint64_t GetTimeNano()
    {
        struct timespec ts;
        int ret = clock_gettime(CLOCK_MONOTONIC_RAW, &ts);
        if (ret != 0)
        {
            return 0;
        }

        return framework::util::FTimespecToNs(&ts);
    }

    // helper functions
    bool ReadLocalTextFile(const std::string &file_name, std::string &content);
    void DumpFrameBytes(const std::string &file_full_path, const DpFrameData &frame, int hidden_lines);
    void DumpSrcFrameFirstLine(const std::string &file_full_path, const DpFrameData &frame, bool bw_decode_result);

    void PrintObject(std::string key_msg, const Quatf &quat);
    void PrintObject(std::string key_msg, const Mat4f &mat);
    void PrintObject(std::string key_msg, const Mat3f &mat);
    void PrintObject(std::string key_msg, const Transform &transform);
    void PrintObject(std::string key_msg, const Vector2i &vec2);
    void PrintObject(std::string key_msg, const Vector3f &vec3);
    void PrintObject(std::string key_msg, const Vector4f &vec4);
    void PrintObject(std::string key_msg, const Fov4f &fov);
    void PrintObject(std::string key_msg, const Rectf &rect);
    void PrintObject(std::string key_msg, const FrameMetaInfoTwin &metadata);
    void PrintObject(std::string key_msg, const FrameMetadataInternal &metadata);
} // namespace heron
