#include <heron/env/system_config.h>
#include <heron/interface/include/flinger/nr_plugin_flinger.h>
#include <heron/model/model_manager.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/control/control_osd.h>
#include <heron/control/control_dp.h>
#include <heron/control/control_display.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>
#include <heron/util/nr_type_converter.h>

#include <framework/util/util.h>

using namespace framework::util;
using namespace heron;
using namespace model;
using namespace control;
using namespace dispatch;

/// interface
static NRPluginResult StartDpRender(NRPluginHandle handle,
                                    const NRResolutionInfo *dp_resolution_info);
static NRPluginResult StopDpRender(NRPluginHandle handle);
static NRPluginResult StartOsdRender(NRPluginHandle handle,
                                     const NRRectf *left_screen_roi,
                                     const NRRectf *right_screen_roi,
                                     NRFrameBufferFormat frame_buffer_format);
static NRPluginResult StopOsdRender(NRPluginHandle handle);
static NRPluginResult AllocateOsdFrameBufferQueue(NRPluginHandle handle,
                                                  const NRFrameBufferAllocateInfo *allocate_info,
                                                  uint32_t count,
                                                  NRFrameBufferQueue **out_frame_buffer_queue);
static NRPluginResult SubmitOsdFrameBuffer(NRPluginHandle handle, const NRFrameBuffer *frame_buffer);
static NRPluginResult WaitOsdFrameBuffer(NRPluginHandle handle, const NRFrameBuffer *frame_buffer);
static NRPluginResult ReleaseOsdFrameBufferQueue(NRPluginHandle handle, const NRFrameBufferQueue *frame_buffer_queue);
static NRPluginResult NotifyDisplayResolutionReset(NRPluginHandle, NRResolution display_resolution);
static NRPluginResult NotifyDisplayDutyChanged(NRPluginHandle, int32_t display_duty_value);
/// global variables
static std::vector<void *> s_flinger_provider{
    (void *)&StartDpRender,
    (void *)&StopDpRender,
    (void *)&StartOsdRender,
    (void *)&StopOsdRender,
    (void *)&AllocateOsdFrameBufferQueue,
    (void *)&SubmitOsdFrameBuffer,
    (void *)&WaitOsdFrameBuffer,
    (void *)&ReleaseOsdFrameBufferQueue,
    (void *)&NotifyDisplayResolutionReset,
    (void *)&NotifyDisplayDutyChanged,
};

const void *GetFlingerProvider(uint32_t &size) // size in bytes
{
    size = s_flinger_provider.size() * sizeof(void *);
    return s_flinger_provider.data();
}

// static std::mutex s_dp_mutex;        // Mutex to protect the Start and Stop methods
/// interface implementations
NRPluginResult StartDpRender(NRPluginHandle, const NRResolutionInfo *nr_res_info)
{
    HERON_LOG_INFO("StartDpRender {}x{} {}fps", nr_res_info->width, nr_res_info->height, nr_res_info->refresh_rate);
    // std::lock_guard<std::mutex> lock(s_dp_mutex);
    //  start dp
    ResolutionInfo res_info;
    ConvertToResolutionInfo(res_info, *nr_res_info);
    DpCtrl::GetInstance()->StartPresentDpFrames(res_info);
    HERON_LOG_INFO("DpRender Started");
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult StopDpRender(NRPluginHandle)
{
    HERON_LOG_INFO("StopDpRender");
    // std::lock_guard<std::mutex> lock(s_dp_mutex);
    //  XXX: careful with the callback happen after Dp thread quit
    DpCtrl::GetInstance()->StopPresentDpFrames();
    DisplayCtrl::GetInstance()->FlushDisplayWithDummyFrame();
    HERON_LOG_INFO("DpRender Stopped.");
    return NR_PLUGIN_RESULT_SUCCESS;
}

static std::mutex s_osd_mutex;         // Mutex to protect the Start and Stop methods
static bool s_osd_is_running = false;  // State of OSD Render
static bool s_osd_initialized = false; // State of OSD Render
NRPluginResult StartOsdRender(NRPluginHandle,
                              const NRRectf *left_screen_roi,
                              const NRRectf *right_screen_roi,
                              NRFrameBufferFormat frame_buffer_format)
{
    HERON_LOG_INFO("StartOsdRender foramt: {}", frame_buffer_format);
    std::lock_guard<std::mutex> lock(s_osd_mutex);
    if (s_osd_is_running)
    {
        return NR_PLUGIN_RESULT_SUCCESS;
    }
    HERON_LOG_DEBUG("left osd roi: {} {} {} {}", left_screen_roi->left, left_screen_roi->right, left_screen_roi->top, left_screen_roi->bottom);
    HERON_LOG_DEBUG("right osd roi: {} {} {} {}", right_screen_roi->left, right_screen_roi->right, right_screen_roi->top, right_screen_roi->bottom);
    uint32_t left_width = abs(left_screen_roi->left - left_screen_roi->right);
    uint32_t left_height = abs(left_screen_roi->top - left_screen_roi->bottom);
    uint32_t right_width = abs(right_screen_roi->left - right_screen_roi->right);
    uint32_t right_height = abs(right_screen_roi->top - right_screen_roi->bottom);
    uint32_t width = ModelManager::DEFAULT_OSD_QUAD_SIZE_PIXEL.x();
    uint32_t height = ModelManager::DEFAULT_OSD_QUAD_SIZE_PIXEL.y();
    if (left_width > width || right_width > width)
    {
        HERON_LOG_ERROR("Roi width must smaller than {}, now we got left_width:{} right_width:{}", width, left_width, right_width);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    if (left_height > height || right_height > height)
    {
        HERON_LOG_ERROR("Roi height must be smaller than {}, now we got left_height:{} right_height:{}", height, left_height, right_height);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    if (!DispatcherWrapper::GetInstance()->StartOSDRender(DISPLAY_USAGE_LEFT, left_screen_roi->left, left_screen_roi->top, width, height, (FramebufferFormat)frame_buffer_format))
        return NR_PLUGIN_RESULT_FAILURE;
    if (!DispatcherWrapper::GetInstance()->StartOSDRender(DISPLAY_USAGE_RIGHT, right_screen_roi->left, right_screen_roi->top, width, height, (FramebufferFormat)frame_buffer_format))
        return NR_PLUGIN_RESULT_FAILURE;
    s_osd_initialized = true;
    ModelManager::GetInstance()->UpdateOsdFramebufferFormat((FramebufferFormat)frame_buffer_format);
    s_osd_is_running = true;
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult StopOsdRender(NRPluginHandle)
{
    HERON_LOG_INFO("StopOsdRender");
    std::lock_guard<std::mutex> lock(s_osd_mutex);
    if (!s_osd_is_running)
        return NR_PLUGIN_RESULT_SUCCESS;
    s_osd_is_running = false;
    if (!DispatcherWrapper::GetInstance()->StopOSDRender())
        return NR_PLUGIN_RESULT_FAILURE;
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult UpdateOsdRender(NRPluginHandle)
{
    HERON_LOG_INFO("UpdateOsdRender");
    return NR_PLUGIN_RESULT_SUCCESS;
}

static std::vector<std::shared_ptr<NRFrameBufferQueue>> s_nr_framebuffer_queues;
NRPluginResult AllocateOsdFrameBufferQueue(
    NRPluginHandle,
    const NRFrameBufferAllocateInfo *allocate_info,
    uint32_t count,
    NRFrameBufferQueue **out_frame_buffer_queue)
{
    HERON_LOG_DEBUG("AllocateOsdFrameBufferQueue. width: {} height: {} format: {} count: {}",
                    allocate_info->width, allocate_info->height, allocate_info->format, count);
    if (count != 1 && count != 2)
    {
        HERON_LOG_ERROR("count = {} now only support count = 1 or 2", count);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    if (!ModelManager::GetInstance()->AllocOSDFramebufferQueue(count))
    {
        HERON_LOG_ERROR("AllocOSDFramebufferQueue failure");
        return NR_PLUGIN_RESULT_FAILURE;
    }
    FramebufferQueuePtr framebuffer_queue_ptr = ModelManager::GetInstance()->GetLatestFrameBufferQueue();
    std::shared_ptr<NRFrameBufferQueue> nr_framebuffer_queue_ptr = std::make_shared<NRFrameBufferQueue>();
    ConvertToNRFrameBufferQueue(*nr_framebuffer_queue_ptr, *framebuffer_queue_ptr);
    s_nr_framebuffer_queues.emplace_back(nr_framebuffer_queue_ptr);
    *out_frame_buffer_queue = nr_framebuffer_queue_ptr.get();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SubmitOsdFrameBuffer(NRPluginHandle, const NRFrameBuffer *frame_buffer)
{
    std::lock_guard<std::mutex> lock(s_osd_mutex);
    // HERON_LOG_TRACE("{} left:{} right: {}", __FUNCTION__, frame_buffer->left_buffer, frame_buffer->right_buffer);
    if (!s_osd_is_running)
    {
        HERON_LOG_ERROR("osd not running when calling SubmitOsdFrameBuffer");
        if (ModelManager::GetInstance()->GetBlankOverlayFrameInfoPtr(DISPLAY_USAGE_LEFT) == nullptr)
        {
            uint32_t osd_image_plane_size = OSD_FRAME_STRIDE * ModelManager::DEFAULT_OSD_QUAD_SIZE_PIXEL.y();
            std::memset(frame_buffer->left_buffer, 0, osd_image_plane_size * 4);
            std::memset(frame_buffer->right_buffer, 0, osd_image_plane_size * 4);
            control::OSDCtrl::GetInstance()->SubmitOSDFramebuffer(frame_buffer->left_buffer, frame_buffer->right_buffer);
        }
        return NR_PLUGIN_RESULT_FAILURE;
    }
    if (!control::OSDCtrl::GetInstance()->SubmitOSDFramebuffer(frame_buffer->left_buffer, frame_buffer->right_buffer))
    {
        return NR_PLUGIN_RESULT_FAILURE;
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult WaitOsdFrameBuffer(NRPluginHandle, const NRFrameBuffer *frame_buffer)
{
    // HERON_LOG_TRACE("{}", __FUNCTION__);
    UNUSED(frame_buffer);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult ReleaseOsdFrameBufferQueue(NRPluginHandle, const NRFrameBufferQueue *frame_buffer_queue)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    ModelManager::GetInstance()->DeallocOSDBuffer();
    s_nr_framebuffer_queues.clear();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult NotifyDisplayResolutionReset(NRPluginHandle, NRResolution display_resolution)
{
    HERON_LOG_DEBUG("{} display_resolution:{}", __FUNCTION__, display_resolution);
    ResolutionInfo resolution_info((Resolution)display_resolution);
    ModelManager::GetInstance()->UpdateDisplayTimingInfo(resolution_info.refresh_rate);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult NotifyDisplayDutyChanged(NRPluginHandle, int32_t display_duty_value)
{
    HERON_LOG_DEBUG("{} display_duty_value:{}", __FUNCTION__, display_duty_value);
    ModelManager::GetInstance()->SetDisplayDutyValue((uint32_t)display_duty_value);
    return NR_PLUGIN_RESULT_SUCCESS;
}