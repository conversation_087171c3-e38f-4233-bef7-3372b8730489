#include <heron/env/system_config.h>
#include <heron/interface/include/common/nr_plugin_device_message.h>
#include <heron/model/model_manager.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/control/control_osd.h>
#include <heron/control/control_display.h>
#include <heron/message/message.h>
#include <heron/util/log.h>
#include <heron/util/misc.h>
#include <heron/util/debug.h>
#include <heron/util/warp.h>

#include <framework/util/util.h>
#include <framework/net/message/message.h>
#include <framework/net/util/socket.h>

using namespace framework::util;
using namespace framework::net;
using namespace heron;
using namespace model;
using namespace dispatch;

#define MSG(__NAME__, __OBJ__)                  \
    assert(size_ == sizeof(message::__NAME__)); \
    const message::__NAME__ &__OBJ__ = *static_cast<const message::__NAME__ *>(data);

/// interface
static NRPluginResult OnDeviceMessage(NRPluginHandle handle,
                                      int32_t connid,
                                      const void *data,
                                      uint32_t size);

/// global variables
static std::vector<void *> s_device_message_provider{
    (void *)&OnDeviceMessage,
};

const void *GetDeviceMessageProvider(uint32_t &size) // size in bytes
{
    size = s_device_message_provider.size() * sizeof(void *);
    return s_device_message_provider.data();
}

// static std::mutex s_dp_mutex;        // Mutex to protect the Start and Stop methods
/// interface implementations
NRPluginResult OnDeviceMessage(NRPluginHandle, int32_t connid, const void *data, uint32_t size)
{
    const message::MsgHeader *mh = static_cast<const message::MsgHeader *>(data);
    // HERON_LOG_TRACE("OnDeviceMessage type:{}", mh->msg_type)
    switch (mh->msg_type)
    {
    case message::MSG_UPDATE_HMD_INFO:
    {
        MSG(HmdInfoMsg, msg);
        ModelManager::GetInstance()->host_modified_display_projection_[NR_DISPLAY_USAGE_LEFT] = warp::GetProjectionMatrixFromFov(msg.left_fov.left_tan,
                                                                                                                                 msg.left_fov.right_tan,
                                                                                                                                 msg.left_fov.top_tan,
                                                                                                                                 msg.left_fov.bottom_tan,
                                                                                                                                 0.3f, 100.0f);
        PrintObject("Left fov: ", msg.left_fov);
        ModelManager::GetInstance()->host_modified_display_projection_[NR_DISPLAY_USAGE_RIGHT] = warp::GetProjectionMatrixFromFov(msg.right_fov.left_tan,
                                                                                                                                  msg.right_fov.right_tan,
                                                                                                                                  msg.right_fov.top_tan,
                                                                                                                                  msg.right_fov.bottom_tan,
                                                                                                                                  0.3f, 100.0f);
        PrintObject("Right fov: ", msg.right_fov);
        MsgTransformToTransform(ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_LEFT], msg.left_pose_from_head);
        // ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_LEFT] = left_pose_from_head;
        PrintObject("Left pose from head: ", ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_LEFT]);
        MsgTransformToTransform(ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_RIGHT], msg.right_pose_from_head);
        // ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_RIGHT] = msg.right_pose_from_head;
        PrintObject("Right pose from head: ", ModelManager::GetInstance()->host_pose_from_head_[DISPLAY_USAGE_RIGHT]);
    }
    break;
    case message::MSG_HEART_BEAT:
    {
    }
    break;
    case message::MSG_CLOSE_ENCRYPT_REQ:
    {
        // send MSG_CLOSE_ENCRYPT_RSP message
    }
    break;
    default:
        MessagePtr message;
        int32_t rlt = MessageInfo::Decode(data, size, message);
        if (rlt != framework::net::BasicMessageInfo::DECODE_SUCCESS)
        {
            HERON_LOG_DEBUG("Failed to decode proto message.")
            return NR_PLUGIN_RESULT_FAILURE;
        }
        message->HandleMessage(nullptr, connid);
        break;
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}