#include <heron/env/system_config.h>
#include <heron/interface/include/misc/nr_plugin_misc.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>

#include <framework/util/util.h>

using namespace framework::util;

/// interface
static NRPluginResult SetGlassesNetLogEnable(NRPluginHandle handle,
                                             ::NREnableValue enable_value,
                                             const char *ip, uint32_t ip_size, uint32_t port);

/// global variables
static std::vector<void *> s_misc_provider{
    (void *)&SetGlassesNetLogEnable,
};

const void *GetMiscProvider(uint32_t &size) // size in bytes
{
    size = s_misc_provider.size() * sizeof(void *);
    return s_misc_provider.data();
}

NRPluginResult SetGlassesNetLogEnable(NRPluginHandle handle,
                                      ::NREnableValue enable_value,
                                      const char *ip, uint32_t ip_size, uint32_t port)
{

    HERON_LOG_DEBUG("{}", __FUNCTION__);
    int32_t log_level = heron::Logger::GetInstance()->GetLogLevel();
    if (enable_value == ::NR_ENABLE_VALUE_ENABLE)
    {
        heron::Logger::GetInstance()->GetLogger()->add_feature_tcp(
            std::string(ip, ip_size), port);
        if (heron::DebugManager::GetInstance()->no_sys_log_when_sending_tcp_log)
            heron::Logger::GetInstance()->GetLogger()->remove_feature_syslog();
    }
    else
    {
        heron::Logger::GetInstance()->GetLogger()->remove_all_feature();
    }
    heron::Logger::GetInstance()->SetLogLevel(log_level);

    return NR_PLUGIN_RESULT_SUCCESS;
}
