#include <heron/env/system_config.h>
#include <heron/interface/include/common/nr_plugin_file.h>
#include <heron/model/permanent_config.h>
#include <heron/util/log.h>

#include <framework/util/util.h>

using namespace framework::util;

/// interface
static NRPluginResult RemoveUserConfigFiles(NRPluginHandle handle);

/// global variables
static std::vector<void *> s_file_provider{
    (void *)&RemoveUserConfigFiles,
};

const void *GetFileProvider(uint32_t &size) // size in bytes
{
    size = s_file_provider.size() * sizeof(void *);
    return s_file_provider.data();
}


NRPluginResult RemoveUserConfigFiles(NRPluginHandle)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    heron::PermanentConfig::GetInstance()->ResetUserConfigs();
    return NR_PLUGIN_RESULT_SUCCESS;
}
