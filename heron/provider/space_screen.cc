#include <heron/env/system_config.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/interface_provider/flinger.h>
#include <heron/interface/include/channel/nr_plugin_glasses_types.h>
#include <heron/model/model_manager.h>
#include <heron/model/permanent_config.h>
#include <heron/control/control_display.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

#include <framework/util/util.h>

using namespace heron;
using namespace heron::model;
using namespace heron::interface_provider;

/*
    空间屏相关
*/
static NRPluginResult SetDpInputMode(NRPluginHandle handle, NRDpInputMode mode);
static NRPluginResult SetLocalPerceptionType(NRPluginHandle handle, NRPerceptionType type);
/*
    返回当前模式下的画布尺寸，单位是m。不同模式下有自己的画布尺寸，比如odof或者小窗
*/
static NRPluginResult GetCanvasDiagonalSize(NRPluginHandle handle, float *out_size);
static NRPluginResult UpdateCanvasSize(NRPluginHandle handle, NROperationType operation_type, NRStepType step_type);
/*
    返回当前模式下的画布距离，单位是m。不同模式下有自己的画布距离，比如odof或者小窗
*/
static NRPluginResult GetCanvasDepth(NRPluginHandle handle, float *out_depth);
static NRPluginResult UpdateCanvasDepth(NRPluginHandle handle, NROperationType operation_type, NRStepType step_type);
static NRPluginResult GetSpaceMode(NRPluginHandle handle, NRSpaceMode *out_space_mode);
static NRPluginResult SetSpaceMode(NRPluginHandle handle, NRSpaceMode space_mode);
static NRPluginResult GetPupilLevelCount(NRPluginHandle handle, int32_t *out_level);
static NRPluginResult GetPupilLevel(NRPluginHandle handle, int32_t *out_level);
static NRPluginResult SetPupilLevel(NRPluginHandle handle, int32_t level);
static NRPluginResult StartPupilAdjust(NRPluginHandle handle);
static NRPluginResult StopPupilAdjust(NRPluginHandle handle);
static NRPluginResult GetThumbnailPositionType(NRPluginHandle handle, NRThumbnailPositionType *out_thumbnail_position);
static NRPluginResult SetThumbnailPositionType(NRPluginHandle handle, NRThumbnailPositionType thumbnail_position);
static NRPluginResult Recenter(NRPluginHandle handle, uint32_t deep_value);
static NRPluginResult GetLookingAtArea(NRPluginHandle handle, NRCanvasAreaType *type);
static NRPluginResult GetUltraWideHeadArea(NRPluginHandle handle, NRCanvasAreaType *type);

/// global variables
static std::vector<void *> s_space_screen_provider{
    (void *)&SetDpInputMode,
    (void *)&SetLocalPerceptionType,
    (void *)&GetCanvasDiagonalSize,
    (void *)&UpdateCanvasSize,
    (void *)&GetCanvasDepth,
    (void *)&UpdateCanvasDepth,
    (void *)&GetSpaceMode,
    (void *)&SetSpaceMode,
    (void *)&GetPupilLevelCount,
    (void *)&GetPupilLevel,
    (void *)&SetPupilLevel,
    (void *)&StartPupilAdjust,
    (void *)&StopPupilAdjust,
    (void *)&GetThumbnailPositionType,
    (void *)&SetThumbnailPositionType,
    (void *)&Recenter,
    (void *)&GetLookingAtArea,
    (void *)&GetUltraWideHeadArea,
};

const void *GetSpaceScreenProvider(uint32_t &size) // size in bytes
{
    size = s_space_screen_provider.size() * sizeof(void *);
    return s_space_screen_provider.data();
}

NRPluginResult SetDpInputMode(NRPluginHandle, NRDpInputMode mode)
{
    HERON_LOG_DEBUG("{} mode:{}", __FUNCTION__, mode);
    ModelManager::GetInstance()->SetDpInputMode((DpInputMode)mode);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SetLocalPerceptionType(NRPluginHandle, NRPerceptionType type)
{
    HERON_LOG_DEBUG("{} type:{}", __FUNCTION__, type);
    if (ModelManager::GetInstance()->GetLocalPerceptionType() == (PerceptionType)type)
        return NR_PLUGIN_RESULT_SUCCESS;
    control::DisplayCtrl::GetInstance()->InvalidateLatestHeadTransform();
    ModelManager::GetInstance()->SetLocalPerceptionType((PerceptionType)type);
    if (type == NR_PERCEPTION_TYPE_3DOF || type == NR_PERCEPTION_TYPE_6DOF)
        control::DisplayCtrl::GetInstance()->NeedRecenter();
    else
        control::DisplayCtrl::GetInstance()->ResetRecenterTransform();
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*
    返回当前模式下的画布尺寸，单位是m。不同模式下有自己的画布尺寸，比如0dof或者小窗
*/
NRPluginResult GetCanvasDiagonalSize(NRPluginHandle, float *out_size)
{
    float diagonal_size_meters = ModelManager::GetInstance()->GetSpaceScreenStatus()->GetTargetActualDiagonalSizeMeters();
    *out_size = diagonal_size_meters * ModelManager::GetInstance()->GetPresentationSizeFactor();
    HERON_LOG_DEBUG("{} presentation_size_factor: {} diagonal_size_meters:{}", __FUNCTION__,
                    ModelManager::GetInstance()->GetPresentationSizeFactor(), diagonal_size_meters);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult UpdateCanvasSize(NRPluginHandle, NROperationType operation_type, NRStepType step_type)
{
    HERON_LOG_DEBUG("{} operation:{} step:{}", __FUNCTION__, operation_type, step_type);
    ModelManager::GetInstance()->UpdateCanvasSizeAndSave((OperationType)operation_type, (StepType)step_type);
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*
    返回当前模式下的画布距离，单位是m。不同模式下有自己的画布距离，比如0dof或者小窗
*/
NRPluginResult GetCanvasDepth(NRPluginHandle, float *out_depth)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    *out_depth = ModelManager::GetInstance()->GetTargetDepth();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult UpdateCanvasDepth(NRPluginHandle, NROperationType operation_type, NRStepType step_type)
{
    HERON_LOG_DEBUG("{} operation:{} step:{}", __FUNCTION__, operation_type, step_type);
    ModelManager::GetInstance()->UpdateCanvasDepthAndSave((OperationType)operation_type, (StepType)step_type);
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*
    <pfunction name="DecreaseCanvasDepth">
    <param name="handle" type="NRPluginHandle"/>
    <param name="step_type" type="NRStepType"/>
    </pfunction>
*/
NRPluginResult GetSpaceMode(NRPluginHandle, NRSpaceMode *out_space_mode)
{
    SpaceMode mode = ModelManager::GetInstance()->GetSpaceMode();
    *out_space_mode = (NRSpaceMode)mode;
    // HERON_LOG_DEBUG("{}: {}", __FUNCTION__, mode);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SetSpaceMode(
    NRPluginHandle,
    NRSpaceMode space_mode)
{
    HERON_LOG_DEBUG("{}: {}", __FUNCTION__, space_mode);
    ModelManager::GetInstance()->SetSpaceModeAndSave((SpaceMode)space_mode);
    if (space_mode == NR_SPACE_MODE_HOVER || space_mode == NR_SPACE_MODE_ULTRA_WIDE)
        control::DisplayCtrl::GetInstance()->NeedRecenter();
    else
        control::DisplayCtrl::GetInstance()->ResetRecenterTransform();
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*

    瞳距调节

*/
NRPluginResult GetPupilLevelCount(
    NRPluginHandle handle,
    int32_t *out_level)
{
    *out_level = ModelManager::PUPIL_LEVEL_ARRAY.size();
    HERON_LOG_DEBUG("{} {}", __FUNCTION__, *out_level);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult GetPupilLevel(
    NRPluginHandle handle,
    int32_t *out_level)
{
    *out_level = PermanentConfig::GetInstance()->GetPupilLevelIndexSaved();
    HERON_LOG_DEBUG("{} {}", __FUNCTION__, *out_level);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SetPupilLevel(
    NRPluginHandle,
    int32_t level)
{
    HERON_LOG_DEBUG("{} level:{}", __FUNCTION__, level);
    if (!ModelManager::GetInstance()->SetPupilLevelAndSave(level))
        return NR_PLUGIN_RESULT_FAILURE;
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*

    进入/退出瞳距调节页面

*/
NRPluginResult StartPupilAdjust(
    NRPluginHandle)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    ModelManager::GetInstance()->SetPupilAdjustFlag(true);
    ModelManager::GetInstance()->ResetDirectCanvasSizeFactor();
    PermanentConfig::GetInstance()->SetCanvasSizeFactor(1.0f);
    PermanentConfig::GetInstance()->SetCanvasDepth(DEPTH_METERS_FOR_PUPIL_ADJUST);
    SetPupilLevel(0, PermanentConfig::GetInstance()->GetPupilLevelIndexSaved());
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult StopPupilAdjust(NRPluginHandle handle)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    Recenter(handle, 0); // recenter before set pupil adjust flag to false
    ModelManager::GetInstance()->SetPupilAdjustFlag(false);
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*

    小窗位置

*/
NRPluginResult GetThumbnailPositionType(
    NRPluginHandle,
    NRThumbnailPositionType *out_thumbnail_position)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    SpaceMode mode;
    ThumbnailPositionType type;
    ModelManager::GetInstance()->GetThumbnailPositionType(&type);
    *out_thumbnail_position = (NRThumbnailPositionType)type;
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SetThumbnailPositionType(
    NRPluginHandle,
    NRThumbnailPositionType thumbnail_position)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    ModelManager::GetInstance()->SetThumbnailPositionTypeAndSave((ThumbnailPositionType)thumbnail_position);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult Recenter(NRPluginHandle, uint32_t)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    SpaceMode mode = ModelManager::GetInstance()->GetSpaceMode();
    if (mode != SPACE_MODE_HOVER && mode != SPACE_MODE_ULTRA_WIDE)
    {
        HERON_LOG_DEBUG("in space_mode:{}, no need to recenter", mode);
        return NR_PLUGIN_RESULT_SUCCESS;
    }
    control::DisplayCtrl::GetInstance()->NeedRecenter();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult GetLookingAtArea(NRPluginHandle handle, NRCanvasAreaType *type)
{
    // HERON_LOG_TRACE("{}", __FUNCTION__);
    *type = NR_CANVAS_AREA_UNKNOWN;
    bool is_looking_at_inner, is_looking_at_outer;
    // type is UNKNOWN if failed to get head pose
    if (!control::DisplayCtrl::GetInstance()->IsLookingAt(is_looking_at_inner, is_looking_at_outer))
        return NR_PLUGIN_RESULT_SUCCESS;
    if (is_looking_at_inner)
        *type = NR_CANVAS_AREA_INSIDE_IMMERSIVE_BOUND;
    else if (is_looking_at_outer)
        *type = NR_CANVAS_AREA_BETWEEN_IMMERSIVE_AND_INTERACTION_BOUND;
    else
        *type = NR_CANVAS_AREA_OUTSIDE_INTERACTION_BOUND;
    if (DebugManager::GetInstance()->debug_log.focus_judger)
    {
        HERON_LOG_DEBUG("IsLookingAt inner:{} outer:{} type:{}", is_looking_at_inner, is_looking_at_outer, *type);
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetUltraWideHeadArea(NRPluginHandle handle, NRCanvasAreaType *type)
{
    // HERON_LOG_TRACE("{}", __FUNCTION__);
    *type = NR_CANVAS_AREA_UNKNOWN;
    if (ModelManager::GetInstance()->GetSpaceMode() != SPACE_MODE_ULTRA_WIDE)
        return NR_PLUGIN_RESULT_SUCCESS;
    bool inner, outer;
    // type is UNKNOWN if failed to get head pose
    if (!control::DisplayCtrl::GetInstance()->InCylinder(inner, outer))
        return NR_PLUGIN_RESULT_SUCCESS;
    // HERON_LOG_TRACE("InCylinder inner:{} outer:{}", inner, outer);
    if (inner)
        *type = NR_CANVAS_AREA_INSIDE_IMMERSIVE_BOUND;
    else if (outer)
        *type = NR_CANVAS_AREA_BETWEEN_IMMERSIVE_AND_INTERACTION_BOUND;
    else
        *type = NR_CANVAS_AREA_OUTSIDE_INTERACTION_BOUND;
    return NR_PLUGIN_RESULT_SUCCESS;
}
