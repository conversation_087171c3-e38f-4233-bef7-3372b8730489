#include <heron/env/export.h>
#include <heron/interface/include/public/nr_plugin_interface.h>

#include <framework/util/log.h>

void ShutdownLog()
{
#ifdef HERON_SHARED_LIBS
    framework::util::log::Logger::shutdown();
#endif
}

#ifdef HERON_SHARED_LIBS
HERON_EXTERN_C_BEGIN
void NRPluginLoad_FLINGER(NRInterfaces *interfaces);
void NRPluginUnload_FLINGER();

void NR_INTERFACE_EXPORT NR_INTERFACE_API
NRPluginLoad(NRInterfaces *interfaces)
{
    NRPluginLoad_FLINGER(interfaces);
}
void NR_INTERFACE_EXPORT NR_INTERFACE_API
NRPluginUnload()
{
    NRPluginUnload_FLINGER();
}
HERON_EXTERN_C_END
#endif
