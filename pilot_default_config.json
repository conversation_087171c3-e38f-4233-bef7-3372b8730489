{
    //显示相关配置
	"display" : 
	{
        //亮度相关配置
        "brightness" :
        {
            //普通模式下，亮度和色温的档位数
            "level_count" : 8,
            //增强模式下，亮度和色温的档位数
            "enhance_level_count" : 12,            
            //luminance的组数据
            "luminance_data" :
            {
                //共有4个组，其中最后一组是增强模式下的luminance数据
                "group_count" : 4,
                "group_data" : 
                [
                    //第0组luminance数据
                    [21,  32,  46,  63, 82, 104, 130, 158],
                    //第1组luminance数据
                    [200, 315, 435, 500, 500, 500, 500, 500],
                    //第2组luminance数据
                    [200, 315, 435, 500, 500, 500, 500, 500],
                    //第3组亮度增强的luminance数据
                    [21,  32,  46,  63, 82, 104, 130, 158, 210, 271, 340, 418]
                ]
            },
            //duty的组数据
            "duty_data" :
            {
                //共有4个组，其中最后一组是增强模式下的duty数据
                "group_count" : 4,
                "group_data" : 
                [
                    //第0组duty数据
                    [95,  95,  95,  95,  95,  95,  95,  95],
                    //第1组duty数据
                    [10,  10,  10,  12,  16,  20,  25,  30],
                    //第2组duty数据
                    [10,  10,  10,  12,  16,  20,  25,  30],
                    //第3组亮度增强的duty数据
                    [95,  95,  95,  95,  95,  95,  95,  95,  95, 95, 95, 95]
                ]
            },
            //base色温的组数据
            "base_color_temperature_data" :
            {
                //共有5个组，其中最后两组是增强模式下的色温base数据
                "group_count" : 5,
                "group_data" : 
                [
                    //第0组色温数据 [-23, -23]: [x偏移值, y偏移值]，其中[0, 0]为基准
                    [[-23, -23], [-23, -23], [-35, -34], [-28, -29], [-16, -20], [-9, -13], [-10, -14], [-10, -14]],
                    //第1组色温数据
                    [[-11, -17], [-18, -27], [-16, -24], [-17, -25], [-17, -25], [-17, -25], [-17, -25], [-17, -25]],
                    //第2组色温数据
                    [[-23, -23], [-23, -23], [-35, -34], [-28, -29], [-16, -20], [-9, -13], [-10, -14], [-10, -14]],
                    //第3组色温数据
                    [[-23, -23], [-23, -23], [-35, -34], [-28, -29], [-16, -20], [-9, -13], [-10, -14], [-10, -14], [-11, -17], [-13, -19], [-14, -20], [-14, -20]],
                    //第4组色温数据
                    [[-23, -23], [-23, -23], [-35, -34], [-28, -29], [-16, -20], [-9, -13], [-10, -14], [-10, -14], [-11, -17], [-13, -19], [-14, -20], [-14, -20]]
                ]
            },
            //不同刷新率和模式下的不同配置项
            "setting" : 
            [
                //以下的group index和level都是从0开始
                {
                    //没有enhance_brightness项时，默认enhance_brightness为false
                    //oled刷新率为60，跟头模式下亮度参数配置
                    "oled_refesh_rate" : 60,
                    "mode" : "head_lock",
                    //luminance的group index，0对应于上面/display/brightness/luminance_data/group_data下的第0组数据
                    "luminance_group_index" : 0,
                    //duty的group index，0对应于上面/display/brightness/duty_data/group_data下的第0组数据
                    "duty_group_index" : 0,
                    //色温的group index，0对应于上面/display/brightness/color_temperature_data/group_data下的第0组数据
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 0,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 0
                },
                {
                    "oled_refesh_rate" : 72,
                    "mode" : "head_lock",
                    "luminance_group_index" : 0,
                    "duty_group_index" : 0,
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 0,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 0
                },
                {
                    "oled_refesh_rate" : 90,
                    "mode" : "head_lock",
                    "luminance_group_index" : 0,
                    "duty_group_index" : 0,
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 0,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 0
                },
                {
                    "oled_refesh_rate" : 120,
                    "mode" : "head_lock",
                    "luminance_group_index" : 0,
                    "duty_group_index" : 0,
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 0,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 0
                },
                {
                    "oled_refesh_rate" : 60,
                    "mode" : "world_lock",
                    "luminance_group_index" : 1,
                    "duty_group_index" : 1,
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 1,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 1
                },
                {
                    "oled_refesh_rate" : 72,
                    "mode" : "world_lock",
                    "luminance_group_index" : 1,
                    "duty_group_index" : 1,
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 1,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 1
                },
                {
                    "oled_refesh_rate" : 90,
                    "mode" : "world_lock",
                    "luminance_group_index" : 1,
                    "duty_group_index" : 1,
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 1,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 1
                },
                {
                    "oled_refesh_rate" : 120,
                    "mode" : "world_lock",
                    "luminance_group_index" : 1,
                    "duty_group_index" : 1,
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 1,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 1
                },
                {
                    //没有oled_refesh_rate时，默认oled_refesh_rate适用所有的刷新率
                    "enhance_brightness" : true,
                    "mode" : "head_lock",
                    "luminance_group_index" : 3,
                    "duty_group_index" : 3,
                    //开启屏显优化的色温base数据group索引
                    "color_temperature_calibration_group_index" : 3,
                    //不开启屏显优化的色温base数据group索引
                    "color_temperature_none_group_index" : 3
                }
            ],
            //默认的亮度档位
            "brightness_default_level" : 4,
            //默认的亮度增强配置
            "enhance_brightness" : false
        },
        //色温调节配置
        "color_temperature_adjust" :
        {
            "color_temperature_offset_list": [[67, 53], [32, 28], [19, 17], [9, 8], [0, 0], [-7, -7], [-13, -13], [-18, -19], [-33, -35]],
            "default_level": 4        
        },
        //屏显优化配置
        "color_calibration" :
        {
            //默认开启
            "default" : "calibration"
        },
        //dp配置
        "dp" :
        {
            //由bsp管理开机时默认的dp edid，由此可以解析出dp input mode
            //"default_input_mode" : "mono",
            //mono时对应的edid
            "mono" : "1920_1080_90_DEFAULT", 
            //stereo时对应的edid
            "stereo" : "3840_1080_60",
            //切换edid时，若以下设定时间内dp没有上报ok，则显示dp错误。单位为秒
            "error_delay" : 10.0
        },
        //开关屏时，渐入或者淡出的配置
        "fade" :
        {
            //开屏渐变次数, 最小粒度为20毫秒(open_last_time/open_step_count)
            "open_step_count" : 10,
            //开屏渐入持续的总时间，单位为秒
            "open_last_time" : 0.2,
            //关屏渐变次数, 最小粒度为20毫秒
            "close_step_count" : 25,
            //关屏淡出持续的总时间，单位为秒
            "close_last_time" : 0.5
        }
	},
    //音频相关配置
    "audio" : 
    {
        //默认的音频模式由bsp管理
        // "default_mode" : "uac",
        //dp音频的档位(共16档)，列表中为该档位对应的音量百分比值
        "dp_volume_list" : [0, 6, 12, 18, 24, 30, 37, 44, 51, 58, 65, 72, 79, 86, 93, 100],
        //默认的dp音量的档位索引，索引从0开始
        "default_dp_volume_level" : 12,
        // //dp音量的调节步长
        // "dp_volume_step" : 10,
        //支持的音频模式
        "mode_list" : ["uac", "dp"]
    },
    //电致变色相关配置
    "ec" : 
    {
        //默认的电致变色档位
        "default_level" : 1,
        //电致变色档位数据配置
        //从标定文件读取
        "level_data" : 
        {
            //档位数
            "count" : 3,
            //透过率：0.1%（最暗）、3%、15%（最透/通透模式）
            //设置duty：100%、35%、0%
            //[0.1, 100, 100] 表示 [通透率， 左眼duty， 右眼duty]
            "datas" : [[0.1, 100, 100], [3, 35, 35], [15, 0, 0]]
        }
        
    },
    //设备相关信息
    "device" : 
    {
        //从bsp接口获取
        //眼镜名字
        "model_name" : "XREAL One",
        //眼镜支持的设备
        "supported_devices" : ["imu", "magnetic", "ambient_light"],
        //眼镜vsync blank信息
        //从bsp接口获取
        "vsync" : 
        {
            //每个dp帧开头的无效像素行个数与总像素行个数的比列
            "invalid_line_scale_start" : 0.03644,
            //每个dp帧结束的无效像素行个数与总像素行个数的比列
            "invalid_line_scale_end" :  0.00355
        }
    },
    //按键时间相关配置
    "key_event" :
    {
        //判定为长按的时间阈值，单位为秒
        "press_time" : 2,
        //判定为双击的间隔阈值，单位为秒
        "double_click_time" : 0.3,
        //支持的功能列表
        "hot_key_list" : ["none", "transparent", "ultra_wide", "dp_in_mode", "audio_mode", "ec_adjust"], 
		//"voice_assistant"],
        //热键配置
        "hot_key_setting" :
        [
            //单击的热键配置
            {
                "event" : "click",
                //默认为通透模式切换功能
                "default" : "ec_adjust"
            },
            //长按的热键配置
            {
                "event" : "press",
                //默认为通透模式切换功能
                "default" : "transparent"
            }
        ]
    },
    //头部追踪相关配置
    "tracking" :
    {
        //支持的头部追踪模式列表：跟头模式，防抖模式，3维悬停模式
        "mode_list" : ["0dof", "eis", "3dof"],
        //默认支持3维旋转追踪，即悬停
        // "default_mode" : "3dof"
    },
    //设备状态相关配置
    "device_state" :
    {
        //进入深度休眠模式前的待机时间列表，单位为秒 0表示永久
        "time_before_deep_sleep_list" : [30, 300, 600, 1200, 3600, 0],
        //默认的深度休眠模式前的待机时间档位
        "time_before_deep_sleep_default_level" : 2,
        //眼镜处于未佩戴状态持续以下时间则进入休眠模式，单位为秒
        "time_before_sleep" : 30000,
        //在进入深度休眠前的以下时间通知host端应用，单位为秒
        "notify_time_before_deep_sleep" : 0.5,
        //是否允许进入休眠，debug用 //TODO 目前用于测试，改为false
        "enable_sleep" : false,
        //是否允许imu静置检测眼镜的佩戴状态
        "enable_imu_motion_check" : false
    },
    //rgb摄像头相关配置
    // "rgb_camera" :
    // {
    //     //rgb摄像头处于工作状态时的led灯配置
    //     "led_shinings" : 
    //     [
    //         //0号led灯配置
    //         {
    //             "led_id" : 0,
    //             //亮灯模式，默认常亮
    //             "shining_method" : "always_on"
    //         }
    //     ]
    // },
    //温度相关配置
    "temperature" :
    {
        //温度采样间隔，单位为秒
        "sample_interval" : 20,
        //采样间隔内的采样次数
        "sample_count" : 4,
        //过温阈值，单位为度  //TODO 默认48， 这里改成100用来测试，不关机
        "danger_temperature" : 52,
        //高温温度阈值，单位为度
        "high_temperature" : 100,
        // //高温警告时间，单位为秒，移到osd配置了
        // "high_warning_time" : 3,
        //高温警告间隔，单位为秒
        "high_warning_interval" : 300,
        //温度传感器配置
        "temperature_sensors" : 
        [
            //1号温度传感器 NTC0
            {
                "id" : "1",
                //拟合系数
                "fit_factor" : 0.93892858
            },
            //2号温度传感器 NTC1
            {
                "id" : "2",
                //拟合系数
                "fit_factor" : 0.06329895
            },
            //常数系数
            {
                //拟合系数
                "fit_factor" : -9.60261019
            }
        ]
    },
    //gdc渲染相关
    "flinger" : 
    {
		// 默认 dp 画面距离(单位：米)
		"canvas_base_depth_meters_" : 4.0,
		// 单击 距离调整步长(单位：米)
		"canvas_depth_step_meters" : 1.0,
		// 长按 距离调整步长(单位：米)
		"extended_canvas_depth_step_meters" : 0.1,
		// 画布最大深度(单位：米)
		"max_canvas_depth_meters" : 10.0,
		// 画布最小深度(单位：米)
		"min_canvas_depth_meters" : 2.0,

        // 默认眼镜屏幕FOV（OSD画面上展示的尺寸数字按照此值计算而来)
        "presentation_diagonal_fov_degree" : 50.0,
        // 单机+/-调整画布尺寸时使用的档位（从小到达按顺序，第一单位的index是0）
        "canvas_diagonal_fov_factor_array" :
        [
            0.8, 0.9, 1.0, 1.3
        ],
        // 默认使用的档位index序号（第一个对应index 0）
        "default_canvas_fov_index_" : 1, // 这里写1对应上面canvas_diagonal_fov_factor_array中的第二个数值：0.9
		// 长按 对角线尺寸调整步长百分比
		"extended_canvas_diagonal_fov_factor_step" : 0.01,

        // 小窗相关配置
		"thumbnail" : 
		{
            // 小窗距离
			"depth_meters" : 4.0,
            // 小窗对角线尺寸(单位：英寸)
			"diagonal_size_inches" : 50.0,
            // 小窗位置为“左”时，小窗中心点位置(单位：米)
			"left_position" : 
			[
				-1.0,
				0.5
			],
            // 小窗位置为“右”时，小窗中心点位置(单位：米)
			"right_position" : 
			[
				1.0,
				0.5
			]
		}
    },
    //osd相关配置
    "osd" :
    {
        //界面超时隐藏时间设置
        "screen_time":{
            //过温提示时间，单位为秒
            "danger_temperature_osd_time" : 5.0,
            //rgb摄像头插拔提示时间，单位为秒
            "rgb_camera_plugin_osd_time" : 3.0,
            //模式切换osd提示时间，单位为秒
            "space_mode_osd_time" : 1.5,
            //高温osd提示时间，单位为秒
            "temperature_high_osd_time" : 5.0,
            //亮度调节提示时间
            "brightness_osd_time" : 3.0,
            //电致变色调节提示时间
            "ec_osd_time" : 2.0,
            //开机界面
            "boot_osd_time" : -1,
            // 按键教程 提示时间
            "keyboard_tutorial_osd_time" : -1,
            // recenter 提示时间
            "recenter_osd_time" : 2.0,
            // formatting 提示时间
            "wrong_disk_format_osd_time" : -1,
            // 开机新手引导提示时间
            "startup_tutorial_osd_time" : -1,
            //传感器校准
            "sensor_calibration_osd_time": -1,
            //瞳距调整
            "ipd_adjust_osd_time": 10.0,
            //照片已保存
            "take_picture_result_osd_time":2.0,
            //视频已保存
            "take_video_result_osd_time":2.0,
            //传输模式已打开tips
            "transport_mode_open_osd_time":-1
        },
        //osd画布的距离，单位是米
        "osd_quad_distance" : 1.67,
        //支持的语言配置
        "language" :
        {
            //默认为英文
            "default" : "english",
            //支持的语言列表
			"list" : ["english", "simple_chinese", "traditional_chinese", "japanese", "spanish", "french", "german", "italian", "korean", "dutch", "thai", "polish", "czech", "latvian"]
        },
        //用户配置录制视频level
        "duration_level_of_video_to_record" : 0,
        //录制视频时长配置
        "video_recorder":{
            "duration_list":[15, 30, 60],
            "default_level": 0
        },
        //是否打开cpu warp
        "enable_cpu_warp":1,
        //是否打开开机流程
        "enable_boot_screen":0
    },
    // 录像拍照相关配置
    "media" :
    {
        // 录像fps
        "video_fps_list" : [30, 60],
        // 录像fps默认档位
        "default_video_fps_level" : 0,
        // 拍照需要最小空闲空间 单位为字节， 2M
        "picture_min_free_storage" :  2097152,
        // 录像需要最小空闲空间 单位为字节，360M
        "video_min_free_storage" : 377487360,
        // 媒体文件存放的基础路径
        "media_base_path" : "/media_data/",
        // 录像中间全路径文件名 正式结束时会改成正式名称
        "video_tmp_file_path" : "/media_data/.video.mp4",
        // 拍照中间全路径文件名 正式结束时会改成正式名称
        "picture_tmp_file_path" : "/media_data/.picture.jpg",
        // 媒体文件名字序号 从 00000 开始 第一个会+1
        "media_file_name_id" :  "00000"
    },
    // 埋点
    "usage_stats" :
    {
        // 每间隔多少秒统计一下时长
        "update_sec" : 10
    }
}
