cmake_minimum_required( VERSION 3.25.3 )

project(heron
        VERSION  1.0.0
        DESCRIPTION "Heron project for nrsdk!"
        LANGUAGES CXX C
        )

# Define relative dir for ${CMAKE_SOURCE_DIR}
set(PROJECT_SRC_DIR "heron")

find_package(framework CONFIG REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(doctest REQUIRED)
find_package(warpcore REQUIRED)

######################################################################################
##  							OPTIONS  											##
######################################################################################

######################################################################################
##                              Compiler                                            ##
######################################################################################
# set(CMAKE_C_FLAGS " xxx")
# set(CMAKE_CXX_FLAGS " xxx")

if (CMAKE_CXX_COMPILER_ID MATCHES "Clang" OR CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
	if (CMAKE_CXX_COMPILER_ID MATCHES "Clang")
		set(PROJECT_CMAKE_CXX_FLAGS "-Wno-deprecated -Wno-unused-parameter -Wno-unused-function -Wno-unused-variable -Wno-shorten-64-to-32 -Wno-reserved-user-defined-literal \
									 -Wno-extern-c-compat")
	elseif (CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
		set(PROJECT_CMAKE_CXX_FLAGS "-Wno-deprecated -Wno-unused-parameter -Wno-unused-function -Wno-unused-variable")
	endif()
elseif (CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
	set(PROJECT_CMAKE_CXX_FLAGS "/WX- /wd4800 /wd4100 /wd4201")
endif()
######################################################################################
##                              Config Project										##
######################################################################################
set(EXTERNAL_PROJECT_DIR ${PROJECT_SOURCE_DIR}/external/project)
list( APPEND CMAKE_MODULE_PATH "${EXTERNAL_PROJECT_DIR}/cmake" )

set(EXTERNAL_INCLUDE_DIR "${Eigen3_INCLUDE_DIR}")
# After set environment variable, we includ project cmake.
include(Project)

# Set git hook
execute_process(COMMAND git config core.hooksPath external/project/githook)
