import numpy as np
from PIL import Image
import argparse

def yuv420_to_rgb(yuv420_data, width, height):
    # Calculate the size of each plane
    frame_size = width * height
    chroma_size = frame_size // 4

    # Extract Y, U, and V planes
    y_plane = yuv420_data[0:frame_size].reshape((height, width))
    u_plane = yuv420_data[frame_size:frame_size + chroma_size].reshape((height // 2, width // 2))
    v_plane = yuv420_data[frame_size + chroma_size:].reshape((height // 2, width // 2))

    # Upsample U and V planes to match the size of the Y plane
    u_plane = u_plane.repeat(2, axis=0).repeat(2, axis=1)
    v_plane = v_plane.repeat(2, axis=0).repeat(2, axis=1)

    # Convert YUV to RGB
    yuv = np.stack((y_plane, u_plane, v_plane), axis=-1).astype(np.float32)
    m = np.array([[1.0, 0.0, 1.402],
                  [1.0, -0.344136, -0.714136],
                  [1.0, 1.772, 0.0]])
    yuv[..., 1:] -= 128
    rgb = yuv @ m.T
    rgb = np.clip(rgb, 0, 255).astype(np.uint8)

    return rgb

def save_rgb_image(rgb_data, width, height, output_path):
    image = Image.fromarray(rgb_data, 'RGB')
    image.save(output_path)

def main():
    parser = argparse.ArgumentParser(description="Convert YUV420 planar image data to a PNG image.")
    parser.add_argument('input_file', help="Path to the input YUV420 file")
    parser.add_argument('output_file', help="Path to the output PNG file")
    parser.add_argument('width', type=int, help="Width of the image")
    parser.add_argument('height', type=int, help="Height of the image")
    args = parser.parse_args()
    input_file = args.input_file
    output_file = args.output_file
    width = args.width
    height = args.height

    # Read the YUV420 file
    with open(input_file, 'rb') as f:
        yuv420_data = np.frombuffer(f.read(), dtype=np.uint8)

    # Convert YUV420 to RGB
    rgb_data = yuv420_to_rgb(yuv420_data, width, height)

    # Save the RGB image as PNG
    save_rgb_image(rgb_data, width, height, output_file)
    print(f'Successfully saved the PNG image to {output_file}')

if __name__ == '__main__':
    main()

