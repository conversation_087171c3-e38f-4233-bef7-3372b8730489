# ATW Details Parser

This tool parses and analyzes binary files containing ATW (Asynchronous Time Warp) details that were dumped from the Heron system.

## Requirements

- Python 3.6+
- NumPy
- Matplotlib

Install the required packages:

```bash
pip install numpy matplotlib
```

## Usage

### Basic Usage

```bash
python parse_atw_details.py path/to/atw_details_20230615_123045.bin
```

This will:
1. Parse the ATW details from the binary file
2. Print statistics about the data
3. Display plots of the data

### Saving Plots

To save the plots to a directory instead of displaying them:

```bash
python parse_atw_details.py path/to/atw_details_20230615_123045.bin --output-dir ./atw_analysis
```

## Understanding the Output

### Statistics

The script provides the following statistics:

- **Total frames**: Number of ATW detail records in the file
- **Frame number range**: Range of frame numbers
- **Render to pose time**: Time (in milliseconds) between rendering start and pose prediction
  - Min, Max, and Average values
- **Line differences**: Differences in line positions due to ATW
  - Min, Max, and Average values

### Plots

The script generates the following plots:

1. **Line Difference Distribution**: Histogram showing the distribution of line differences
2. **Render to Pose Time**: Graph showing the time between rendering start and pose prediction for each frame

## Data Structure

The binary file contains ATW details with the following structure:

```cpp
struct ATWFrameDetail {
    uint32_t frame_num;
    Transform old_transform;
    uint64_t start_render_ns;
    uint64_t osd_pose_ns;
    uint64_t device_predict_ns[35];
    int32_t line_diff[35];
    Transform new_transform[35];
};

struct Transform {
    Vector3f position;
    Quatf rotation;
};
```

Where:
- `frame_num`: Frame number
- `old_transform`: Original head transform
- `start_render_ns`: Timestamp when rendering started (nanoseconds)
- `osd_pose_ns`: Timestamp for OSD pose (nanoseconds)
- `device_predict_ns`: Array of device prediction timestamps (nanoseconds)
- `line_diff`: Array of line differences
- `new_transform`: Array of new transforms after ATW

## Advanced Analysis

You can modify the script to perform more advanced analysis:

1. **Correlation Analysis**: Check for correlations between line differences and transform changes
2. **Temporal Analysis**: Analyze how ATW behavior changes over time
3. **Performance Analysis**: Identify frames with unusually high latency or large corrections

## Troubleshooting

### File Format Issues

If you encounter errors parsing the file, check:

1. The file was generated by the Heron system's ATW debug functionality
2. The file format matches the expected structure
3. The file is not corrupted

### Visualization Issues

If plots don't display correctly:

1. Ensure Matplotlib is installed correctly
2. Try saving plots to files instead of displaying them
3. Check if your data contains valid values (not all zeros or extreme values)

## Contributing

Feel free to enhance this script with additional analysis or visualization features.
