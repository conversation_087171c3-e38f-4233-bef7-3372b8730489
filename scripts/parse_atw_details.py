#!/usr/bin/env python3
"""
ATW Details Parser

This script parses binary files containing ATW (Asynchronous Time Warp) details
and provides analysis and visualization of the data.

The script generates visualizations including:
- Line differences over time
- Time differences (device_predict_ns - old_pose_ns)
- Frame numbers over device_predict_ns
- New transform rotations split into yaw, pitch, and roll components with overall rotation differences
- Time differences (device_predict_ns - device_warp_ns)
- Time differences (device_predict_ns - start_render_ns)

Usage:
    python parse_atw_details.py <path_to_atw_details_file>
    python parse_atw_details.py <path_to_atw_details_file> --output-dir <directory>
    python parse_atw_details.py <directory_with_atw_details_files>
    python parse_atw_details.py <directory_with_atw_details_files> --output-dir <directory>

When a directory is provided, the script will scan for all binary files with "atw_details" as prefix,
combine the data from all files, and analyze/visualize the combined dataset as a continuous sequence.
"""

import sys
import os
import struct
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import argparse
import matplotlib.ticker as ticker

# Define the structure of the ATWFrameDetail struct
# Based on the C++ struct definition:
# struct ATWFrameDetail {
#     uint32_t frame_num;
#     uint64_t start_render_ns;
#     uint64_t old_pose_ns;
#     uint64_t device_warp_ns[35];
#     uint64_t device_predict_ns[35];
#     int32_t line_diff[35];
#     Transform old_transform;
#     Transform new_transform[35];
# };
#
# struct Transform {
#     Vector3f position;
#     Quatf rotation;
# };
#
# Vector3f is 3 floats (x, y, z)
# Quatf is 4 floats (w, x, y, z)

def parse_transform(data, offset):
    """Parse a Transform struct from binary data."""
    # Position (Vector3f): 3 floats (x, y, z)
    position = struct.unpack('fff', data[offset:offset+12])
    offset += 16 # 4 bytes padding

    # Rotation (Quatf): 4 floats (w, x, y, z)
    rotation = struct.unpack('ffff', data[offset:offset+16])
    offset += 16

    return {
        'position': {'x': position[0], 'y': position[1], 'z': position[2]},
        'rotation': {'w': rotation[0], 'x': rotation[1], 'y': rotation[2], 'z': rotation[3]}
    }, offset

def parse_atw_detail(data, offset):
    """Parse an ATWFrameDetail struct from binary data."""
    start_offset = offset

    # frame_num (uint32_t)
    if start_offset == 4:
        print(f"frame_num start pos: {offset - start_offset}")
    frame_num = struct.unpack('I', data[offset:offset+4])[0]
    offset += 8 # 4 bytes padding

    # start_render_ns (uint64_t)
    if start_offset == 4:
        print(f"start_render_ns start pos: {offset - start_offset}")
    start_render_ns = struct.unpack('Q', data[offset:offset+8])[0]
    offset += 8

    # old_pose_ns (uint64_t)
    if start_offset == 4:
        print(f"old_pose_ns start pos: {offset - start_offset}")
    old_pose_ns = struct.unpack('Q', data[offset:offset+8])[0]
    offset += 8

    # device_warp_ns[35] (uint64_t[35])
    if start_offset == 4:
        print(f"device_warp_ns start pos: {offset - start_offset}")
    device_warp_ns = list(struct.unpack('35Q', data[offset:offset+35*8]))
    offset += 35 * 8

    # device_predict_ns[35] (uint64_t[35])
    if start_offset == 4:
        print(f"device_predict_ns start pos: {offset - start_offset}")
    device_predict_ns = list(struct.unpack('35Q', data[offset:offset+35*8]))
    offset += 35 * 8

    # line_diff[35] (int32_t[35])
    if start_offset == 4:
        print(f"line_diff start pos: {offset - start_offset}")
    line_diff = list(struct.unpack('35i', data[offset:offset+35*4]))
    offset += 35 * 4
    # Ensure we're at a 16-byte boundary with 4-byte offset
    while offset % 16 != 4:
        offset += 4

    # old_transform (Transform)
    if start_offset == 4:
        print(f"old_transform start pos: {offset - start_offset}")
    old_transform, offset = parse_transform(data, offset)

    # new_transform[35] (Transform[35])
    if start_offset == 4:
        print(f"new_transform start pos: {offset - start_offset}")
    new_transforms = []
    for i in range(35):
        transform, offset = parse_transform(data, offset)
        new_transforms.append(transform)

    return {
        'frame_num': frame_num,
        'start_render_ns': start_render_ns,
        'old_pose_ns': old_pose_ns,
        'device_warp_ns': device_warp_ns,
        'device_predict_ns': device_predict_ns,
        'line_diff': line_diff,
        'old_transform': old_transform,
        'new_transform': new_transforms
    }, offset - start_offset

def parse_atw_details_file(file_path):
    """Parse an ATW details binary file.

    Returns data indexed by device_predict_ns instead of frame number.
    """
    with open(file_path, 'rb') as f:
        data = f.read()

    # First 4 bytes contain the number of details
    num_details = struct.unpack('I', data[0:4])[0]
    print(f"File contains {num_details} ATW details")

    offset = 4
    details_by_predict_ns = {}
    frame_details = []  # Keep original structure for printing

    for i in range(num_details):
        detail, detail_size = parse_atw_detail(data, offset)
        frame_details.append(detail)
        offset += detail_size

        # Reorganize data to be indexed by device_predict_ns
        frame_num = detail['frame_num']
        old_transform = detail['old_transform']
        start_render_ns = detail['start_render_ns']
        old_pose_ns = detail['old_pose_ns']

        # For each device_predict_ns value, create an entry
        for j, predict_ns in enumerate(detail['device_predict_ns']):
            if predict_ns > 0:  # Only include valid timestamps
                # Create a unique key for each device_predict_ns
                if predict_ns not in details_by_predict_ns:
                    details_by_predict_ns[predict_ns] = {
                        'frame_num': frame_num,
                        'old_transform': old_transform,
                        'start_render_ns': start_render_ns,
                        'old_pose_ns': old_pose_ns,
                        'device_warp_ns': detail['device_warp_ns'][j],
                        'device_predict_ns': predict_ns,
                        'line_diff': detail['line_diff'][j],  # Include zero line_diff values as valid data
                        'new_transform': detail['new_transform'][j],
                        'sample_index': j
                    }
                else:
                    print(f"WARNING: Duplicate device_predict_ns value {predict_ns} found for frame {frame_num} sample {j}")
            else:
                print(f"WARNING: Invalid device_predict_ns value {predict_ns} found for frame {frame_num} sample {j}")

        # Print detailed information every 400 frames
        if i % 400 == 0:
            print(f"\nParsing detail {i}/{num_details}...")
            print(f"  Frame number: {detail['frame_num']}")
            print(f"  Start render time: {detail['start_render_ns']}")
            print(f"  Old pose time: {detail['old_pose_ns']}")

            # Print old transform
            pos = detail['old_transform']['position']
            rot = detail['old_transform']['rotation']
            print(f"  Old transform position: ({pos['x']:.3f}, {pos['y']:.3f}, {pos['z']:.3f})")
            print(f"  Old transform rotation: ({rot['w']:.3f}, {rot['x']:.3f}, {rot['y']:.3f}, {rot['z']:.3f})")

            # Print a sample of line differences
            non_zero_diffs = [d for d in detail['line_diff'] if d != 0]
            if non_zero_diffs:
                print(f"  Line differences: {detail['line_diff']} (sample {j})")
                print(f"  Line diff min: {min(non_zero_diffs)}, max: {max(non_zero_diffs)}")
            else:
                print(f"  Line differences: All zeros")

            # Print a sample of device predict times
            non_zero_predicts = [t for t in detail['device_predict_ns'] if t != 0]
            if non_zero_predicts:
                print(f"  Device predict times: {detail['device_predict_ns'] } (sample {j})")
            else:
                print(f"  Device predict times: All zeros")

    print(f"Successfully parsed {len(frame_details)} ATW details")
    print(f"Created {len(details_by_predict_ns)} entries indexed by device_predict_ns")
    print(f"Total size of details data: {sys.getsizeof(details_by_predict_ns)} bytes")

    # Convert to a list sorted by device_predict_ns
    details_list = [details_by_predict_ns[predict_ns] for predict_ns in sorted(details_by_predict_ns.keys())]
    return details_list

def analyze_atw_details(details):
    """Analyze ATW details and print statistics."""
    if not details:
        print("No details to analyze")
        return

    # Extract frame numbers and device_predict_ns values
    frame_nums = [detail['frame_num'] for detail in details]
    predict_times = [detail['device_predict_ns'] for detail in details]

    # Count unique frames
    unique_frames = len(set(frame_nums))

    # Calculate time differences (device_predict_ns - old_pose_ns)
    pose_time_diffs_ms = []
    for detail in details:
        if detail['device_predict_ns'] > 0 and detail['old_pose_ns'] > 0:
            pose_time_diffs_ms.append((detail['device_predict_ns'] - detail['old_pose_ns']) / 1e6)  # Convert to ms

    # Analyze line differences
    all_line_diffs = [detail['line_diff'] for detail in details]
    non_zero_diffs = [d for d in all_line_diffs if d != 0]
    entries_with_non_zero_diffs = len(non_zero_diffs)

    print("\nStatistics:")
    print(f"Total entries: {len(details)}")
    # Calculate a more accurate size by including the size of each entry
    total_size = sys.getsizeof(details)
    for detail in details:
        total_size += sys.getsizeof(detail)
        for key, value in detail.items():
            total_size += sys.getsizeof(key) + sys.getsizeof(value)

    print(f"Total size of details: {total_size} bytes")
    print(f"Average size per entry: {total_size/len(details):.2f} bytes")
    print(f"Unique frames: {unique_frames}")
    print(f"Frame number range: {min(frame_nums)} to {max(frame_nums)}")
    print(f"Device predict time range: {min(predict_times)/1e6:.2f}ms to {max(predict_times)/1e6:.2f}ms")

    if pose_time_diffs_ms:
        print(f"\nTime difference (device_predict_ns - old_pose_ns) (ms):")
        print(f"  Min: {min(pose_time_diffs_ms):.2f}")
        print(f"  Max: {max(pose_time_diffs_ms):.2f}")
        print(f"  Avg: {sum(pose_time_diffs_ms)/len(pose_time_diffs_ms):.2f}")

    print(f"\nLine differences:")
    print(f"  Entries with non-zero diffs: {entries_with_non_zero_diffs}/{len(details)} ({entries_with_non_zero_diffs/len(details)*100:.1f}%)")
    print(f"  Entries with zero diffs: {len(details) - entries_with_non_zero_diffs}/{len(details)} ({(len(details) - entries_with_non_zero_diffs)/len(details)*100:.1f}%)")

    if all_line_diffs:
        print(f"  Min: {min(all_line_diffs)}")
        print(f"  Max: {max(all_line_diffs)}")
        print(f"  Avg: {sum(all_line_diffs)/len(all_line_diffs):.2f}")

        # Print distribution information
        print("\nLine difference distribution:")
        # Count occurrences in different ranges
        ranges = [(-1000, -100), (-100, -50), (-50, -10), (-10, 0), (0, 10), (10, 50), (50, 100), (100, 1000)]
        for start, end in ranges:
            count = sum(1 for d in all_line_diffs if start <= d < end)
            if count > 0:
                print(f"  {start} to {end}: {count} ({count/len(all_line_diffs)*100:.1f}%)")
    else:
        print("  No non-zero line differences found!")


def ms_to_min_sec(ms):
    """Convert milliseconds to minutes:seconds.milliseconds format.

    Args:
        ms: Time in milliseconds

    Returns:
        String in format "MM:SS.mmm"
    """
    # Convert to seconds
    seconds = ms / 1000.0
    # Extract minutes and remaining seconds
    minutes = int(seconds // 60)
    remaining_seconds = seconds % 60
    # Format as MM:SS.mmm
    return f"{minutes:02d}:{remaining_seconds:06.3f}"


def format_min_sec_tick(x, pos):  # pylint: disable=unused-argument
    """Format function for matplotlib tick labels to show minutes:seconds.

    Args:
        x: The tick value in milliseconds
        pos: The position (unused, required by matplotlib's formatter interface)

    Returns:
        String in format "MM:SS"
    """
    return ms_to_min_sec(x).split('.')[0]  # Remove milliseconds part for tick labels


def quaternion_to_euler(q):
    """Convert quaternion to Euler angles (yaw, pitch, roll) in degrees.

    Uses the coordinate system:
    - Y-axis is up (gravitational direction)
    - Z-axis is pointing backward (opposite to facing direction)
    - X-axis is pointing to the right

    Based on feedback, the angles are rearranged to match the expected output:
    - Yaw: What was previously calculated as pitch (rotation around X-axis)
    - Pitch: What was previously calculated as roll (rotation around Z-axis)
    - Roll: What was previously calculated as yaw (rotation around Y-axis)

    Args:
        q: Dictionary with quaternion components {'w', 'x', 'y', 'z'}

    Returns:
        Tuple of (yaw, pitch, roll) in degrees
    """
    # Extract quaternion components
    w, x, y, z = q['w'], q['x'], q['y'], q['z']

    # Calculate the three rotations

    # Rotation around Y-axis (will be mapped to roll)
    siny_cosp = 2.0 * (w * x - y * z)
    cosy_cosp = 1.0 - 2.0 * (x * x + y * y)
    y_rotation = np.arctan2(siny_cosp, cosy_cosp)

    # Rotation around X-axis (will be mapped to yaw)
    sinp = 2.0 * (w * y + x * z)
    if abs(sinp) >= 1:
        x_rotation = np.copysign(np.pi / 2, sinp)  # Use 90 degrees if out of range
    else:
        x_rotation = np.arcsin(sinp)

    # Rotation around Z-axis (will be mapped to pitch)
    sinr_cosp = 2.0 * (w * z + x * y)
    cosr_cosp = 1.0 - 2.0 * (y * y + z * z)
    z_rotation = np.arctan2(sinr_cosp, cosr_cosp)

    # Convert to degrees
    x_rotation_deg = np.degrees(x_rotation)
    y_rotation_deg = np.degrees(y_rotation)
    z_rotation_deg = np.degrees(z_rotation)

    # Wrap the z_rotation_deg (pitch) to a specific range
    # Wrap to [-90, 90] range
    if z_rotation_deg > 90:
        z_rotation_deg = 180 - z_rotation_deg
    elif z_rotation_deg < -90:
        z_rotation_deg = -180 - z_rotation_deg

    # Remap the rotations to match the expected output:
    # yaw = x_rotation (previously pitch)
    # pitch = z_rotation (previously roll)
    # roll = y_rotation (previously yaw)
    return (
        x_rotation_deg,  # Yaw
        z_rotation_deg,  # Pitch
        y_rotation_deg   # Roll
    )

def calculate_rotation_difference(old_transform, new_transform):
    """Calculate a simple difference metric between two quaternions.

    This returns the angular difference in degrees between two quaternions.
    """
    old_rot = old_transform['rotation']
    new_rot = new_transform['rotation']

    # Calculate dot product between quaternions
    dot_product = (old_rot['w'] * new_rot['w'] +
                   old_rot['x'] * new_rot['x'] +
                   old_rot['y'] * new_rot['y'] +
                   old_rot['z'] * new_rot['z'])

    # Clamp to valid range for acos
    dot_product = max(min(dot_product, 1.0), -1.0)

    # Convert to angle in degrees (2 * acos(dot) gives the angle in radians)
    angle_diff = 2 * np.arccos(abs(dot_product)) * 180 / np.pi

    return angle_diff

# Function removed as requested

def plot_atw_details(details, output_dir=None):
    """Generate plots from ATW details."""
    if not details:
        print("No details to plot")
        return

    # Enable interactive mode to show all plots at once
    plt.ion()

    # Create a list to store figure references
    figures = []

    # If output_dir is specified, create it with a timestamp
    if output_dir:
        # Add timestamp to output directory if not already present
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if not output_dir.endswith(timestamp):
            output_dir = os.path.join(output_dir, f"atw_details_{timestamp}")

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"Created output directory: {output_dir}")

    # Extract data for plotting
    all_line_diffs = [detail['line_diff'] for detail in details]
    all_predict_times_ms = [detail['device_predict_ns'] / 1e6 for detail in details]  # Convert to ms

    # Extract frame numbers
    frame_nums = [detail['frame_num'] for detail in details]

    # Calculate time differences (device_predict_ns - old_pose_ns)
    pose_time_diffs_ms = [(detail['device_predict_ns'] - detail['old_pose_ns']) / 1e6 for detail in details]

    # Calculate time differences (device_predict_ns - device_warp_ns)
    warp_predict_diffs_ms = [(detail['device_predict_ns'] - detail['device_warp_ns']) / 1e6 for detail in details]

    # Calculate time differences (device_predict_ns - start_render_ns)
    render_predict_diffs_ms = [(detail['device_predict_ns'] - detail['start_render_ns']) / 1e6 for detail in details]

    # Calculate rotation differences
    rotation_diffs = [calculate_rotation_difference(detail['old_transform'], detail['new_transform'])
                      for detail in details]

    # Extract Euler angles from both old and new transforms
    old_transform_euler = [quaternion_to_euler(detail['old_transform']['rotation']) for detail in details]
    new_transform_euler = [quaternion_to_euler(detail['new_transform']['rotation']) for detail in details]

    # Separate yaw, pitch, roll for old transforms
    old_yaw = [euler[0] for euler in old_transform_euler]
    old_pitch = [euler[1] for euler in old_transform_euler]
    old_roll = [euler[2] for euler in old_transform_euler]

    # Separate yaw, pitch, roll for new transforms
    new_yaw = [euler[0] for euler in new_transform_euler]
    new_pitch = [euler[1] for euler in new_transform_euler]
    new_roll = [euler[2] for euler in new_transform_euler]

    # We only need the overall rotation difference, not individual Euler angle differences

    if not all_line_diffs:
        print("No line differences to plot")
        return

    # Plot 1: Line differences distribution histogram
    fig1 = plt.figure(figsize=(12, 8))
    figures.append(fig1)

    # Full range histogram (including zeros)
    plt.hist(all_line_diffs, bins=50, alpha=0.7)
    plt.title('Distribution of Line Differences')
    plt.xlabel('Line Difference')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)

    # Calculate min, max, and average values
    min_val = min(all_line_diffs)
    max_val = max(all_line_diffs)
    avg_val = sum(all_line_diffs) / len(all_line_diffs)

    # Get current axis limits to position annotations properly
    ax = plt.gca()
    _, ymax = ax.get_ylim()  # Only need ymax for annotation positioning
    _, xmax = ax.get_xlim()  # Only need xmax for text positioning

    # Create a text box with all three values
    stats_text = f"Min: {min_val}\nMax: {max_val}\nAvg: {avg_val:.2f}"

    # Position the text box in the upper right corner
    plt.text(xmax*0.7, ymax*0.9, stats_text,
             fontsize=12,
             bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray'),
             verticalalignment='top')

    if output_dir:
        plt.savefig(os.path.join(output_dir, 'line_diff_distribution.png'))
    else:
        plt.draw()  # Use draw instead of show for non-blocking behavior

    # Plot 2: All line diff values over time
    fig2 = plt.figure(figsize=(14, 8))
    figures.append(fig2)

    # Scatter plot of all line diff values
    plt.scatter(all_predict_times_ms, all_line_diffs, alpha=0.7, s=10)
    plt.title('All Line Difference Values')
    plt.xlabel('Device Predict Time (ms / MM:SS)')
    plt.ylabel('Line Difference')
    plt.grid(True, alpha=0.3)

    # Add a horizontal line at y=0 for reference
    plt.axhline(y=0, color='r', linestyle='-', alpha=0.3)

    # Add secondary x-axis with minutes:seconds format
    ax = plt.gca()
    secax = ax.secondary_xaxis('top', functions=(lambda x: x, lambda x: x))
    secax.xaxis.set_major_formatter(ticker.FuncFormatter(format_min_sec_tick))

    # Calculate min, max, and average values for line differences
    min_val = min(all_line_diffs)
    max_val = max(all_line_diffs)
    avg_val = sum(all_line_diffs) / len(all_line_diffs)

    # Get current axis limits to position annotations properly
    _, ymax = ax.get_ylim()  # Only need ymax for text positioning
    _, xmax = ax.get_xlim()  # Only need xmax for text positioning

    # Create a text box with all three values
    stats_text = f"Min: {min_val}\nMax: {max_val}\nAvg: {avg_val:.2f}"

    # Position the text box in the upper right corner
    plt.text(xmax*0.9, ymax*0.9, stats_text,
             fontsize=12,
             bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray'),
             verticalalignment='top')

    if output_dir:
        plt.savefig(os.path.join(output_dir, 'all_line_diff_values.png'))
    else:
        plt.draw()  # Use draw instead of show for non-blocking behavior

    # Plot 3: Frame numbers over device_predict_ns
    fig3 = plt.figure(figsize=(14, 8))
    figures.append(fig3)

    plt.scatter(all_predict_times_ms, frame_nums, alpha=0.7, s=10)
    plt.title('Frame Numbers')
    plt.xlabel('Device Predict Time (ms / MM:SS)')
    plt.ylabel('Frame Number')
    plt.grid(True, alpha=0.3)

    # Add secondary x-axis with minutes:seconds format
    ax = plt.gca()
    secax = ax.secondary_xaxis('top', functions=(lambda x: x, lambda x: x))
    secax.xaxis.set_major_formatter(ticker.FuncFormatter(format_min_sec_tick))

    if output_dir:
        plt.savefig(os.path.join(output_dir, 'frame_numbers.png'))
    else:
        plt.draw()  # Use draw instead of show for non-blocking behavior

    # Plot 4: Time differences (device_predict_ns - old_pose_ns) over device_predict_ns
    fig4 = plt.figure(figsize=(14, 8))
    figures.append(fig4)

    plt.scatter(all_predict_times_ms, pose_time_diffs_ms, alpha=0.7, s=10)
    plt.title('host_predict to device_predict')
    plt.xlabel('Device Predict Time (ms / MM:SS)')
    plt.ylabel('Time Difference (ms)')
    plt.grid(True, alpha=0.3)

    # Add secondary x-axis with minutes:seconds format
    ax = plt.gca()
    secax = ax.secondary_xaxis('top', functions=(lambda x: x, lambda x: x))
    secax.xaxis.set_major_formatter(ticker.FuncFormatter(format_min_sec_tick))

    # Calculate min, max, and average values for time differences
    min_val = min(pose_time_diffs_ms)
    max_val = max(pose_time_diffs_ms)
    avg_val = sum(pose_time_diffs_ms) / len(pose_time_diffs_ms)

    # Get current axis limits to position annotations properly
    _, ymax = ax.get_ylim()  # Only need ymax for text positioning
    _, xmax = ax.get_xlim()  # Only need xmax for text positioning

    # Create a text box with all three values
    stats_text = f"Min: {min_val:.2f} ms\nMax: {max_val:.2f} ms\nAvg: {avg_val:.2f} ms"

    # Position the text box in the upper right corner
    plt.text(xmax*0.9, ymax*0.9, stats_text,
             fontsize=12,
             bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray'),
             verticalalignment='top')

    if output_dir:
        plt.savefig(os.path.join(output_dir, 'pose_time_differences.png'))
    else:
        plt.draw()  # Use draw instead of show for non-blocking behavior

    # Plot 5: Old and New transform Euler angles and rotation differences over device_predict_ns
    fig5 = plt.figure(figsize=(14, 10))  # Increased height for more space
    figures.append(fig5)

    # Adjust the figure layout to provide more space at the top
    plt.subplots_adjust(top=0.85, bottom=0.1)  # Increase space at the top and bottom

    # Create a second y-axis for rotation differences
    ax1 = plt.gca()
    ax2 = ax1.twinx()

    # Plot New transform Euler angles with solid lines and markers
    ax1.scatter(all_predict_times_ms, new_yaw, alpha=0.7, s=10, label='New Yaw', color='blue')
    ax1.scatter(all_predict_times_ms, new_pitch, alpha=0.7, s=10, label='New Pitch', color='green')
    ax1.scatter(all_predict_times_ms, new_roll, alpha=0.7, s=10, label='New Roll', color='red')

    # Plot Old transform Euler angles with dashed lines and different markers
    ax1.scatter(all_predict_times_ms, old_yaw, alpha=0.5, s=10, label='Old Yaw', color='blue', marker='x')
    ax1.scatter(all_predict_times_ms, old_pitch, alpha=0.5, s=10, label='Old Pitch', color='green', marker='x')
    ax1.scatter(all_predict_times_ms, old_roll, alpha=0.5, s=10, label='Old Roll', color='red', marker='x')

    ax1.set_ylabel('Angle (degrees)', color='black')

    # Plot rotation differences on the second y-axis
    ax2.scatter(all_predict_times_ms, rotation_diffs, alpha=0.7, s=10, label='Rotation Diff', color='purple')
    ax2.set_ylabel('Rotation Difference (degrees)', color='purple')

    # Set x-axis label
    ax1.set_xlabel('Device Predict Time (ms / MM:SS)')

    # Add legends for both axes
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

    # Add grid
    ax1.grid(True, alpha=0.3)

    # Add secondary x-axis with minutes:seconds format
    secax = ax1.secondary_xaxis('top', functions=(lambda x: x, lambda x: x))
    secax.xaxis.set_major_formatter(ticker.FuncFormatter(format_min_sec_tick))

    # Calculate min, max, and average values for rotation differences
    min_rot_diff = min(rotation_diffs)
    max_rot_diff = max(rotation_diffs)
    avg_rot_diff = sum(rotation_diffs) / len(rotation_diffs)

    # Get current axis limits to position annotations properly
    _, y2max = ax2.get_ylim()  # Get y-axis limits from the rotation difference axis
    _, xmax = ax1.get_xlim()  # Get x-axis limits

    # Create a text box with rotation difference statistics
    rot_stats_text = f"Rotation Diff:\nMin: {min_rot_diff:.2f}°\nMax: {max_rot_diff:.2f}°\nAvg: {avg_rot_diff:.2f}°"

    # Position the text box in the upper right corner, but below the legend
    ax2.text(xmax*0.9, y2max*0.7, rot_stats_text,
             fontsize=12,
             bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray'),
             verticalalignment='top',
             color='purple')

    # Add title with extra padding
    plt.title('Old and New Transform Euler Angles with Rotation Difference', pad=30)

    if output_dir:
        plt.savefig(os.path.join(output_dir, 'old_new_transform_euler_and_rotation_diff.png'))
    else:
        plt.draw()  # Use draw instead of show for non-blocking behavior

    # Plot 6: Time differences (device_predict_ns- device_warp_ns) over device_predict_ns
    fig6 = plt.figure(figsize=(14, 8))
    figures.append(fig6)

    plt.scatter(all_predict_times_ms, warp_predict_diffs_ms, alpha=0.7, s=10)
    plt.title('chip warp to present(MTP)')
    plt.xlabel('Device Predict Time (ms / MM:SS)')
    plt.ylabel('Time Difference (ms)')
    plt.grid(True, alpha=0.3)

    # Add secondary x-axis with minutes:seconds format
    ax = plt.gca()
    secax = ax.secondary_xaxis('top', functions=(lambda x: x, lambda x: x))
    secax.xaxis.set_major_formatter(ticker.FuncFormatter(format_min_sec_tick))

    # Calculate min, max, and average values for time differences
    min_val = min(warp_predict_diffs_ms)
    max_val = max(warp_predict_diffs_ms)
    avg_val = sum(warp_predict_diffs_ms) / len(warp_predict_diffs_ms)

    # Get current axis limits to position annotations properly
    _, ymax = ax.get_ylim()  # Only need ymax for text positioning
    _, xmax = ax.get_xlim()  # Only need xmax for text positioning

    # Create a text box with all three values
    stats_text = f"Min: {min_val:.2f} ms\nMax: {max_val:.2f} ms\nAvg: {avg_val:.2f} ms"

    # Position the text box in the upper right corner
    plt.text(xmax*0.9, ymax*0.9, stats_text,
             fontsize=12,
             bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray'),
             verticalalignment='top')

    if output_dir:
        plt.savefig(os.path.join(output_dir, 'warp_predict_time_differences.png'))
    else:
        plt.draw()  # Use draw instead of show for non-blocking behavior

    # Plot 7: Time differences (device_predict_ns - start_render_ns) over device_predict_ns
    fig7 = plt.figure(figsize=(14, 8))
    figures.append(fig7)

    plt.scatter(all_predict_times_ms, render_predict_diffs_ms, alpha=0.7, s=10)
    plt.title('device_predict to start_render')
    plt.xlabel('Device Predict Time (ms / MM:SS)')
    plt.ylabel('Time Difference (ms)')
    plt.grid(True, alpha=0.3)

    # Add secondary x-axis with minutes:seconds format
    ax = plt.gca()
    secax = ax.secondary_xaxis('top', functions=(lambda x: x, lambda x: x))
    secax.xaxis.set_major_formatter(ticker.FuncFormatter(format_min_sec_tick))

    # Calculate min, max, and average values for time differences
    min_val = min(render_predict_diffs_ms)
    max_val = max(render_predict_diffs_ms)
    avg_val = sum(render_predict_diffs_ms) / len(render_predict_diffs_ms)

    # Get current axis limits to position annotations properly
    _, ymax = ax.get_ylim()  # Only need ymax for text positioning
    _, xmax = ax.get_xlim()  # Only need xmax for text positioning

    # Create a text box with all three values
    stats_text = f"Min: {min_val:.2f} ms\nMax: {max_val:.2f} ms\nAvg: {avg_val:.2f} ms"

    # Position the text box in the upper right corner
    plt.text(xmax*0.9, ymax*0.9, stats_text,
             fontsize=12,
             bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray'),
             verticalalignment='top')

    if output_dir:
        plt.savefig(os.path.join(output_dir, 'render_predict_time_differences.png'))
    else:
        plt.draw()  # Use draw instead of show for non-blocking behavior

    # If we're displaying plots (not saving to files), wait for user to close them
    if not output_dir:
        print("\nDisplaying all plots. Close any plot window to continue...")
        # Keep the figures open until the user closes one of them
        plt.show(block=True)  # This will block until all figures are closed


def check_data_issues(details):
    """Check for potential issues in the parsed data."""
    print("\nChecking for potential data issues...")

    # Check 1: Are there any entries with invalid timestamps?
    invalid_timestamps = 0
    for detail in details:
        if (detail['start_render_ns'] == 0 or detail['old_pose_ns'] == 0 or
            detail['device_predict_ns'] == 0 or detail['device_warp_ns'] == 0):
            invalid_timestamps += 1

    if invalid_timestamps > 0:
        print(f"WARNING: {invalid_timestamps} entries ({invalid_timestamps/len(details)*100:.1f}%) have invalid timestamps")

    # Check 2: Check for duplicate device_predict_ns values
    predict_times = [detail['device_predict_ns'] for detail in details]
    unique_predict_times = set(predict_times)
    if len(unique_predict_times) < len(predict_times):
        print(f"WARNING: Found {len(predict_times) - len(unique_predict_times)} duplicate device_predict_ns values")

    # Check 3: Check for gaps in device_predict_ns sequence
    if predict_times:
        sorted_times = sorted(predict_times)
        time_diffs = [(sorted_times[i+1] - sorted_times[i])/1e6 for i in range(len(sorted_times)-1)]  # Convert to ms
        if time_diffs:
            avg_diff = sum(time_diffs) / len(time_diffs)
            max_diff = max(time_diffs)
            if max_diff > 5 * avg_diff:  # Arbitrary threshold for detecting gaps
                print(f"WARNING: Possible gaps in device_predict_ns sequence")
                print(f"  Average time difference: {avg_diff:.2f} ms")
                print(f"  Maximum time difference: {max_diff:.2f} ms")

def parse_file(file_path):
    """Parse a single ATW details file without analysis or plotting.

    Args:
        file_path: Path to the ATW details binary file

    Returns:
        list: Parsed details or empty list if parsing failed
    """
    print(f"Parsing file: {file_path}")
    try:
        details = parse_atw_details_file(file_path)
        if not details:
            print(f"Warning: No valid details found in {file_path}")
            return []
        return details
    except Exception as e:
        print(f"Error parsing file {file_path}: {str(e)}")
        return []

def print_debug_info(details, count=3):
    """Print debug information for the first few entries."""
    if not details:
        return

    print(f"\nDetailed information for first {min(count, len(details))} entries:")
    for i, detail in enumerate(details[:min(count, len(details))]):
        print(f"\nEntry {i}:")
        print(f"  Frame number: {detail['frame_num']}")
        print(f"  Device predict time: {detail['device_predict_ns']} ns ({detail['device_predict_ns']/1e6:.2f} ms)")
        print(f"  Device warp time: {detail['device_warp_ns']} ns ({detail['device_warp_ns']/1e6:.2f} ms)")
        print(f"  Start render time: {detail['start_render_ns']} ns ({detail['start_render_ns']/1e6:.2f} ms)")
        print(f"  Old pose time: {detail['old_pose_ns']} ns ({detail['old_pose_ns']/1e6:.2f} ms)")

        # Print time difference
        if detail['old_pose_ns'] > 0 and detail['device_predict_ns'] > 0:
            time_diff_ms = (detail['device_predict_ns'] - detail['old_pose_ns']) / 1e6
            print(f"  Time diff (device_predict_ns - old_pose_ns): {time_diff_ms:.2f} ms")

        print(f"  Sample index: {detail.get('sample_index', 'N/A')}")

        # Print line difference
        if detail['line_diff'] != 0:
            print(f"  Line difference: {detail['line_diff']}")
        else:
            print(f"  Line difference: 0 (zero)")

        # Print transform information
        pos = detail['old_transform']['position']
        rot = detail['old_transform']['rotation']
        print(f"  Old transform position: ({pos['x']:.3f}, {pos['y']:.3f}, {pos['z']:.3f})")
        print(f"  Old transform rotation: ({rot['w']:.3f}, {rot['x']:.3f}, {rot['y']:.3f}, {rot['z']:.3f})")

        new_pos = detail['new_transform']['position']
        new_rot = detail['new_transform']['rotation']
        print(f"  New transform position: ({new_pos['x']:.3f}, {new_pos['y']:.3f}, {new_pos['z']:.3f})")
        print(f"  New transform rotation: ({new_rot['w']:.3f}, {new_rot['x']:.3f}, {new_rot['y']:.3f}, {new_rot['z']:.3f})")

def main():
    parser = argparse.ArgumentParser(description='Parse and analyze ATW details binary files')
    parser.add_argument('path', help='Path to the ATW details binary file or directory containing ATW details files')
    parser.add_argument('--output-dir', '-o', help='Directory to save output plots (will create timestamped subdirectory)')
    parser.add_argument('--debug', '-d', action='store_true', help='Print additional debug information for the first 3 entries')

    args = parser.parse_args()

    if not os.path.exists(args.path):
        print(f"Error: Path {args.path} does not exist")
        return 1

    # Create output directory with timestamp if specified
    output_dir = None
    if args.output_dir:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(args.output_dir, f"atw_details_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        print(f"Created output directory: {output_dir}")

    # Initialize an empty list to store all details
    all_details = []

    # Check if the path is a directory
    if os.path.isdir(args.path):
        print(f"Scanning directory: {args.path} for ATW details files")

        # Find all binary files with "atw_details" prefix
        atw_files = []
        for file in os.listdir(args.path):
            if file.startswith("atw_details") and file.endswith(".bin"):
                atw_files.append(os.path.join(args.path, file))

        if not atw_files:
            print(f"Error: No ATW details files found in {args.path}")
            return 1

        print(f"Found {len(atw_files)} ATW details files")

        # Parse each file and combine the data
        for i, file_path in enumerate(sorted(atw_files)):
            print(f"\nParsing file {i + 1}/{len(atw_files)}: {os.path.basename(file_path)}")
            file_details = parse_file(file_path)
            if file_details:
                print(f"  Added {len(file_details)} entries from {os.path.basename(file_path)}")
                all_details.extend(file_details)
            else:
                print(f"  No valid details found in {os.path.basename(file_path)}")

        if not all_details:
            print("Error: No valid details found in any of the files")
            return 1

        print(f"\nSuccessfully parsed {len(all_details)} entries from {len(atw_files)} files")
    else:
        # Parse a single file
        all_details = parse_file(args.path)
        if not all_details:
            print(f"Error: No valid details found in {args.path}")
            return 1

    # Sort all details by device_predict_ns to ensure chronological order
    all_details.sort(key=lambda x: x['device_predict_ns'])

    print(f"\nAnalyzing combined data with {len(all_details)} entries...")
    analyze_atw_details(all_details)
    check_data_issues(all_details)

    # Generate plots from the combined data
    if output_dir:
        print(f"\nGenerating plots in directory: {output_dir}")
    plot_atw_details(all_details, output_dir)

    # Print debug information if requested
    if args.debug:
        print_debug_info(all_details)

    return 0

if __name__ == "__main__":
    sys.exit(main())
