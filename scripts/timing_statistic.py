import os
import sys
import matplotlib.pyplot as plt
from pathlib import Path

def get_or_create_recent_entry(log_entries, frame_number, check_last_n=5):
    # print("get_or_create_recent_entry {}", frame_number)
    # 查找
    for entry in log_entries[-check_last_n:]:
        if entry.get("frame_number") == frame_number:
            # print("get_or_create_recent_entry got entry")
            return entry
    # 没找到，创建新的
    # print("get_or_create_recent_entry create entry")
    new_entry = {"frame_number": frame_number}
    log_entries.append(new_entry)
    return new_entry

# Let's implement reading the logs from a file, parsing, and then sorting them by the number of bytes lost.
def parse_log_file(file_path):
    log_entries = []

    with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
        lines = file.readlines()
        j = 0        
        for curr_line in lines:  # Every entry spans 4 lines
            # Parse the first line to get the number of bytes and allocation
            curr_line = curr_line.strip()
            words = curr_line.split(' ')
            word_size = len(words)

            # 检查当前行是否为第一个日志信息行
            if "frame_num:" in curr_line:
                print("curr_line is {}".format(curr_line))
                if words[9] == "frame_num:":
                    tmp = words[10]
                    target_frame_number = int(tmp)
                    # print("target_frame_number is {}".format(target_frame_number))
                    if target_frame_number > 0:
                        entry = get_or_create_recent_entry(log_entries, target_frame_number)
                        if "start_render:" in curr_line:
                            index = words.index("start_render:")
                            entry["start_render"] = words[index+1]
                        if "submit:" in curr_line:
                            index = words.index("submit:")
                            entry["submit"] = words[index+1]
                        if "start_comp:" in curr_line:
                            index = words.index("start_comp:")
                            entry["start_compose"] = words[index+1]
                        if "start_embedded:" in curr_line:
                            index = words.index("start_embedded:")
                            entry["start_embedded"] = words[index+1]
                        if "pred_pose:" in curr_line:
                            index = words.index("pred_pose:")
                            entry["predict_pose"] = words[index+1]
                        if "data_recv:" in curr_line:
                            index = words.index("data_recv:")
                            entry["data_recv"] = words[index+1]
                        if "recv_dp:" in curr_line:
                            index = words.index("recv_dp:")
                            entry["recv_dp"] = words[index+1]
                        if "vsync_gen:" in curr_line:
                            index = words.index("vsync_gen:")
                            entry["vsync_gen"] = words[index+1]
                        if "vsync_sent:" in curr_line:
                            index = words.index("vsync_sent:")
                            entry["vsync_sent"] = words[index+1]
                        if "embedded_parsed:" in curr_line:
                            index = words.index("embedded_parsed:")
                            entry["embedded_parsed"] = words[index+1]
                        if "frame_parsed:" in curr_line:
                            index = words.index("frame_parsed:")
                            entry["frame_parsed"] = words[index+1]
                        if "apply:" in curr_line:
                            index = words.index("apply:")
                            entry["apply_time"] = words[index+1]
                        if "disp_mid:" in curr_line:
                            # print("curr_line is {}".format(curr_line))
                            index = words.index("disp_mid:")
                            entry["display_mid"] = words[index+1]

    return log_entries

def draw_data(log_entries):
    frame_number = []
    compose_to_receive = []
    render_to_receive = []
    render_to_compose = []
    render_to_mid_display = []
    submit_to_reveive = []
    predict_to_mid_display = []
    render_to_predict = []
    receive_to_apply = []
    receive_to_mid_display = []
    receive_to_vsync_gen = []
    receive_to_vsync_sent = []
    receive_to_embedded_parsed = []
    receive_to_parse_done = []
    for entry in log_entries:
        print(entry)
        # print("frame_number is {} receive_dp_time {} start_compose_time {} start_render_time {} submit_time {}".format(
        #     entry["frame_number"], entry["receive_dp_time"], entry["start_compose_time"], entry["start_render_time"], entry["submit_time"]))
        if "recv_dp" in entry and "start_compose" in entry and "start_render" in entry and "predict_pose" in entry and "display_mid" in entry and "apply_time" in entry:
            number = int(entry["frame_number"])
            if number > 100:
                frame_number.append(number)
                delta = float(int(entry["start_compose"]) - int(entry["start_render"]))/1_000_000.0
                render_to_compose.append(delta)
                delta = float(int(entry["recv_dp"]) - int(entry["start_compose"]))/1_000_000.0
                compose_to_receive.append(delta)
                delta = float(int(entry["recv_dp"]) - int(entry["start_render"]))/1_000_000.0
                render_to_receive.append(delta)
                delta = float(int(entry["display_mid"]) - int(entry["start_render"]))/1_000_000.0
                render_to_mid_display.append(delta)
                #delta = float(int(entry["recv_dp"]) - int(entry["submit"]))/1_000_000.0
                #submit_to_reveive.append(delta)
                delta = float(int(entry["predict_pose"]) - int(entry["start_render"]))/1_000_000.0
                render_to_predict.append(delta)
                delta = float(int(entry["apply_time"]) - int(entry["recv_dp"]))/1_000_000.0
                receive_to_apply.append(delta)
                delta = float(int(entry["display_mid"]) - int(entry["recv_dp"]))/1_000_000.0
                receive_to_mid_display.append(delta)
                delta = float(int(entry["display_mid"]) - int(entry["predict_pose"]))/1_000_000.0
                predict_to_mid_display.append(delta)
                #delta = float(int(entry["vsync_gen"]) - int(entry["recv_dp"]))/1_000_000.0
                #receive_to_vsync_gen.append(delta)
                #delta = float(int(entry["vsync_sent"]) - int(entry["recv_dp"]))/1_000_000.0
                #receive_to_vsync_sent.append(delta)
                #delta = float(int(entry["embedded_parsed"]) - int(entry["recv_dp"]))/1_000_000.0
                #receive_to_embedded_parsed.append(delta)
                #delta = float(int(entry["frame_parsed"]) - int(entry["recv_dp"]))/1_000_000.0
                #receive_to_parse_done.append(delta)

    # 创建图形
    plt.figure(figsize=(8, 5))
    # end to end latency
    plt.plot(frame_number, compose_to_receive, label='compose_to_receive', color='blue', marker='o')
    plt.plot(frame_number, render_to_receive, label='render_to_receive', color='green', marker='s')
    plt.plot(frame_number, render_to_predict, label='render_to_predict', color='red', marker='^')
    plt.plot(frame_number, predict_to_mid_display, label='predict_to_mid_display', color='purple', marker='d')
    plt.plot(frame_number, receive_to_mid_display, label='receive_to_mid_display', color='orange', marker='d')
    plt.plot(frame_number, receive_to_apply, label='receive_to_apply', color='#81D8BF', marker='d')
    # 添加图例、标题、坐标轴标签
    plt.title("latency")
    plt.xlabel("frame number")
    plt.ylabel("time(ms)")
    plt.legend()
    plt.grid(True)


    #plt.figure(figsize=(8, 5))
    ## parse frame time cost
    #plt.plot(frame_number, receive_to_vsync_gen, label='recv_to_vsync_gen', color='green', marker='s')
    #plt.plot(frame_number, receive_to_vsync_sent, label='recv_to_vsync_sent', color='orange', marker='d')
    #plt.plot(frame_number, receive_to_embedded_parsed, label='recv_to_embedded_parsed', color='red', marker='^')
    #plt.plot(frame_number, receive_to_parse_done, label='recv_to_parse_done', color='purple', marker='d')

    ## 添加图例、标题、坐标轴标签
    #plt.title("latency")
    #plt.xlabel("frame number")
    #plt.ylabel("time(ms)")
    #plt.legend()
    #plt.grid(True)

    # 保存图像
    plt.savefig("multi_line_plot.png", dpi=1000)

    # 显示图像
    plt.show()

if __name__ == '__main__':
    if len(sys.argv) < 3:
        print("Usage: python {0} output_dir <plugin.xml>".format(sys.argv[0]))
        exit(-1)

    # output
    output_dir = sys.argv[1]
    os.makedirs(output_dir, 0o755, True)

    for file_path in sys.argv[2:]:
        print("file_path is {}".format(file_path))
        # Parse, sort and format the logs
        log_entries = parse_log_file(file_path)
        # path = Path(file_path)
        # # 获取文件夹路径（文件的父目录）
        # folder_path = path.parent
        draw_data(log_entries)

