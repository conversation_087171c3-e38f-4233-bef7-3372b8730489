import numpy as np
from PIL import Image
import os
import argpar<PERSON>

def bin_to_grayscale_image(bin_file, width, output_image):
    # Step 1: Check if binary file exists
    if not os.path.exists(bin_file):
        raise FileNotFoundError(f"The file {bin_file} does not exist.")

    # Step 2: Get the file size
    file_size = os.path.getsize(bin_file)

    # Step 3: Calculate height (ceil to handle padding)
    height = (file_size + width - 1) // width  # Equivalent to math.ceil(file_size / width)
    print(f"Computed dimensions: width={width}, height={height}")

    # Step 4: Read binary file into a numpy array
    with open(bin_file, 'rb') as f:
        data = f.read()

    # Step 5: Pad data if needed
    total_size = width * height
    if len(data) < total_size:
        print(f"Padding with {total_size - len(data)} bytes to complete the last row.")
        data += b'\x00' * (total_size - len(data))

    # Convert data into a numpy array and reshape
    array = np.frombuffer(data, dtype=np.uint8).reshape((height, width))

    # Step 6: Convert to image using Pillow
    img = Image.fromarray(array, 'L')  # 'L' mode for grayscale

    # Step 7: Ensure output directory exists
    output_dir = os.path.dirname(output_image)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Step 8: Save the image with error handling
    try:
        img.save(output_image)
        print(f"Image saved as {output_image}")
    except IOError as e:
        print(f"Failed to save image: {e}")

# Main script
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert a binary file to a grayscale image.")
    parser.add_argument("bin_file", type=str, help="Path to the binary file.")
    parser.add_argument("width", type=int, help="Width of the grayscale image.")
    parser.add_argument("output_image", type=str, help="Path to save the output image.")

    args = parser.parse_args()

    # Call the function with command-line arguments
    bin_to_grayscale_image(args.bin_file, args.width, args.output_image)
