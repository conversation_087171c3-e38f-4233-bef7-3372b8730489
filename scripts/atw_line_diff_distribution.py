import matplotlib.pyplot as plt
import numpy as np

# 原始数据
bin_range = [-400, 400, 50]  # min, max, bin_width
counts = [0,0,0,0,11,334,1942,8721,8187,2012,242,106,13,17,19,0]

# 计算bin边界
min_val, max_val, bin_width = bin_range
bin_edges = np.arange(min_val, max_val + bin_width, bin_width)

# 确保counts长度与bin数量匹配
n_bins = len(bin_edges) - 1
if len(counts) != n_bins:
    print(f"Warning: Counts length ({len(counts)}) doesn't match expected bins ({n_bins})")

# 创建直方图
plt.figure(figsize=(12, 6))
bars = plt.bar(bin_edges[:-1], counts, width=bin_width*0.9, align='edge')

# 添加数值标签
for bar in bars:
    height = bar.get_height()
    if height > 0:  # 只显示非零值的标签
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{int(height)}',
                ha='center', va='bottom')

# 设置图表属性
plt.title('Distribution Histogram', fontsize=14)
plt.xlabel('Value Range', fontsize=12)
plt.ylabel('Frequency', fontsize=12)
plt.grid(axis='y', alpha=0.5)

# 调整x轴刻度
plt.xticks(bin_edges, rotation=45)

# 显示图表
plt.tight_layout()
plt.show()